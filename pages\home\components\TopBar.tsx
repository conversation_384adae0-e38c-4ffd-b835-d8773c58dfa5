import { Colors } from '@/utils/colors';
import { safeNavigate } from '@/utils/navigationUtils';
import { Ionicons } from '@expo/vector-icons';
import { Image, Pressable, View } from 'react-native';

const TopBar = () => {
  return (
    <View className="h-[95] w-full flex-row items-center justify-between">
      <Image source={require('../../../assets/images/logo/Logo.png')} className={'h-11 w-11'} />
      <View className="h-full flex-row items-center justify-between gap-3">
        <Pressable
          className="h-11 w-11 items-center justify-center rounded-full bg-white"
          onPress={() => {
            safeNavigate('/search');
          }}>
          <Ionicons name="search" size={24} color={Colors.secondary['950']} />
        </Pressable>
        <Pressable className="h-11 w-11 items-center justify-center rounded-full bg-white">
          <Ionicons name="notifications" size={24} color={Colors.secondary['950']} />
        </Pressable>
        <Pressable
          onPress={() => {
            safeNavigate('/profile');
          }}>
          <Image
            source={{
              uri: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            }}
            className={'border-1 h-11 w-11 rounded-full border-white'}
            resizeMode="cover"
          />
        </Pressable>
      </View>
    </View>
  );
};

export default TopBar;
