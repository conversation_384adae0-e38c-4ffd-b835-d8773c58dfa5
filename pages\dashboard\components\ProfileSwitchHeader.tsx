import { ImageAdvanced } from '@/components';
import VerifiedIcon from '@/components/ui/VerifiedIcon';
import ProfileData from '@/store/data/ProfileData';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { FlexLayouts, Spacing } from '@/utils/layoutStyles';
import { safeNavigate } from '@/utils/navigationUtils';
import { shadows } from '@/utils/shadows';
import { Ionicons } from '@expo/vector-icons';
import { SquircleView } from 'expo-squircle-view';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const ProfileSwitchHeader = () => {
  return (
    <View style={[FlexLayouts.rowBetween, styles.container]}>
      <TouchableOpacity
        style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm }]}
        onPress={() => {
          safeNavigate('/profile');
        }}>
        <ImageAdvanced source={ProfileData.profile.avatar} style={styles.avatar}></ImageAdvanced>
        <View>
          <View style={FlexLayouts.rowCenter}>
            <Text style={styles.nameTitle}>{ProfileData.profile.name} </Text>
            <VerifiedIcon color={Colors.primary[950]} size={16}></VerifiedIcon>
          </View>
          <Text style={styles.username}>@{ProfileData.profile.username}</Text>
        </View>
      </TouchableOpacity>
      <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm }]}>
        <TouchableOpacity>
          <SquircleView
            cornerSmoothing={100}
            preserveSmoothing={true}
            backgroundColor={Colors.background.muted}
            style={styles.button}>
            <Ionicons name="add" size={24} color={Colors.secondary['950']}></Ionicons>
          </SquircleView>
        </TouchableOpacity>
        <TouchableOpacity>
          <SquircleView
            cornerSmoothing={100}
            preserveSmoothing={true}
            backgroundColor={Colors.background.muted}
            style={styles.button}>
            <Ionicons name="chevron-down" size={24} color={Colors.secondary['950']}></Ionicons>
          </SquircleView>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ProfileSwitchHeader;
const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    height: 90,
    paddingHorizontal: 16,
    borderRadius: 14,
    ...shadows.large,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 9999,
  },
  nameTitle: {
    fontSize: 18,
    fontFamily: Fonts.roboto.bold,
    color: Colors.secondary[950],
  },
  username: {
    fontSize: 14,
    fontFamily: Fonts.roboto.regular,
    color: Colors.text.muted,
  },
  button: {
    width: 35,
    height: 35,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
