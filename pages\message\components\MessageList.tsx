import { Message } from "@/types/media";
import { Colors } from "@/utils/colors";
import { Fonts } from "@/utils/fonts";
import { Spacing } from "@/utils/layoutStyles";
import React, { useCallback, useEffect, useMemo, useRef } from "react";
import { FlatList, ListRenderItem, StyleSheet, Text, View } from "react-native";
import MessageItem from "./MessageItem";

interface MessageWithSequence extends Message {
    isLastInSequence: boolean;
}

interface DateSeparator {
    id: string;
    type: 'date-separator';
    date: string;
    displayDate: string;
}

type ListItem = MessageWithSequence | DateSeparator;

interface MessageListProps {
    messages: Message[];
    contactAvatar?: string;
    visibleTimestampId: string | null;
    onToggleTimestamp: (messageId: string) => void;
}

const MessageList: React.FC<MessageListProps> = ({
    messages,
    contactAvatar,
    visibleTimestampId,
    onToggleTimestamp
}) => {
    const flatListRef = useRef<FlatList>(null);

    // Helper function to format date
    const formatDateHeader = (dateStr: string): string => {
        const date = new Date(dateStr);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        // If it's today
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        }

        // If it's yesterday
        if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        }

        // Format as "Month Day" (e.g., "July 30")
        return date.toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric'
        });
    };

    // Helper function to get date from timestamp
    const getDateFromTimestamp = (timestamp: string): string => {
        // If timestamp is in format "09:25 AM", use today's date
        if (/^\d{1,2}:\d{2}\s?(AM|PM)$/i.test(timestamp)) {
            return new Date().toDateString();
        }

        // Try to parse as full date/time
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            // Fallback to today if parsing fails
            return new Date().toDateString();
        }

        return date.toDateString();
    };

    // Process messages to include date separators and sequence info
    const processedListItems = useMemo((): ListItem[] => {
        if (messages.length === 0) return [];

        const items: ListItem[] = [];
        let currentDate = '';

        // Process messages with sequence info
        const messagesWithSequence = messages.map((message, index) => ({
            ...message,
            isLastInSequence: index === messages.length - 1 ||
                messages[index + 1]?.isOwn !== message.isOwn
        }));

        messagesWithSequence.forEach((message, index) => {
            const messageDate = getDateFromTimestamp(message.timestamp);

            // Add date separator if date changed
            if (messageDate !== currentDate) {
                currentDate = messageDate;
                const dateSeparator: DateSeparator = {
                    id: `date-${messageDate}`,
                    type: 'date-separator',
                    date: messageDate,
                    displayDate: formatDateHeader(messageDate)
                };
                items.push(dateSeparator);
            }

            items.push(message);
        });

        return items;
    }, [messages]);

    // Reverse for inverted FlatList (newest at top of inverted list = bottom of screen)
    const reversedListItems = useMemo(() => {
        return [...processedListItems].reverse();
    }, [processedListItems]);

    // Stable callback using useCallback
    const handleToggleTimestamp = useCallback((messageId: string) => {
        onToggleTimestamp(messageId);
    }, [onToggleTimestamp]);

    // Track previous message count to detect actual new messages
    const prevMessageCountRef = useRef(messages.length);

    // Auto-scroll to bottom only when new messages are actually added
    useEffect(() => {
        const currentCount = messages.length;
        const wasNewMessageAdded = currentCount > prevMessageCountRef.current;

        if (flatListRef.current && wasNewMessageAdded && reversedListItems.length > 0) {
            // For inverted list, scroll to index 0 (which is the newest message)
            setTimeout(() => {
                flatListRef.current?.scrollToIndex({
                    index: 0,
                    animated: true,
                    viewPosition: 0
                });
            }, 100);
        }

        prevMessageCountRef.current = currentCount;
    }, [messages.length, reversedListItems.length]);

    const renderListItem: ListRenderItem<ListItem> = useCallback(({ item }) => {
        if (item.type === 'date-separator') {
            return (
                <View style={styles.dateSeparatorContainer}>
                    <Text style={styles.dateSeparatorText}>{item.displayDate}</Text>
                </View>
            );
        }

        return (
            <MessageItem
                message={item}
                isLastInSequence={item.isLastInSequence}
                contactAvatar={contactAvatar}
                visibleTimestampId={visibleTimestampId}
                onToggleTimestamp={handleToggleTimestamp}
            />
        );
    }, [contactAvatar, visibleTimestampId, handleToggleTimestamp]);

    const keyExtractor = useCallback((item: ListItem) => item.id, []);

    const onContentSizeChange = useCallback(() => {
        // Only auto-scroll on content size change if we recently added a new message
        const currentCount = messages.length;
        const wasNewMessageAdded = currentCount > prevMessageCountRef.current;

        if (wasNewMessageAdded && reversedListItems.length > 0) {
            setTimeout(() => {
                flatListRef.current?.scrollToIndex({
                    index: 0,
                    animated: false,
                    viewPosition: 0
                });
            }, 50);
        }
    }, [messages.length, reversedListItems.length]);

    // Add scroll to index failure handler
    const onScrollToIndexFailed = useCallback((info: any) => {
        // Fallback to scrollToOffset if scrollToIndex fails
        setTimeout(() => {
            flatListRef.current?.scrollToOffset({
                offset: 0,
                animated: true
            });
        }, 100);
    }, []);

    return (
        <View style={styles.container}>
            <FlatList
                ref={flatListRef}
                data={reversedListItems}
                renderItem={renderListItem}
                keyExtractor={keyExtractor}
                contentContainerStyle={styles.listContent}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
                removeClippedSubviews={false}
                maxToRenderPerBatch={15}
                windowSize={15}
                initialNumToRender={25}
                inverted={true}
                onContentSizeChange={onContentSizeChange}
                onScrollToIndexFailed={onScrollToIndexFailed}
                maintainVisibleContentPosition={{
                    minIndexForVisible: 0,
                }}
            />
        </View>
    );
};

export default MessageList;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background.primary,
    },
    listContent: {
        paddingHorizontal: Spacing.horizontal.md,
        paddingTop: Spacing.vertical.sm,
        paddingBottom: 20,
    },
    dateSeparatorContainer: {
        alignItems: 'center',
        marginVertical: Spacing.vertical.md,
    },
    dateSeparatorText: {
        fontSize: 12,
        fontFamily: Fonts.roboto.medium,
        color: Colors.text.muted,
        backgroundColor: Colors.background.primary,
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 12,
        overflow: 'hidden',
    },
});