import { Colors } from '@/utils/colors';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { ImageBackground, StyleSheet, View } from 'react-native';

interface ThemeData {
  type: 'image' | 'gradient' | 'dashboard' | null;
  source?: string;
  colors?: [string, string, ...string[]];
}

interface PostBackgroundProps {
  imageUri?: string;
  children?: React.ReactNode;
  selectedTheme?: ThemeData | null;
}

const PostBackground: React.FC<PostBackgroundProps> = ({ 
  imageUri, 
  children,
  selectedTheme
}) => {
  const defaultImageUri = 
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';

  const renderBackground = () => {
    if (selectedTheme?.type === 'gradient') {
      return (
        <LinearGradient
          colors={selectedTheme.colors || ['#3b82f6', '#E20071']}
          start={{ x: 1, y: 1 }}
          end={{ x: 0, y: 0 }}
          style={styles.background}
        />
      );
    } else if (selectedTheme?.type === 'image') {
      return (
        <ImageBackground
          source={{ uri: selectedTheme.source || defaultImageUri }}
          borderRadius={24}
          style={styles.background}
        />
      );
    } else {
      // Default background (no theme selected or dashboard theme)
      return (
        <View
          style={styles.background}
        ></View>
      );
    }
  };

  return (
    <View style={styles.container}>
      {renderBackground()}
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 24,
  },
});

export default PostBackground;
