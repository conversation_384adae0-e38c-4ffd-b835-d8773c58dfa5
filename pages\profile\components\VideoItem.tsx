import ImageAdvanced from '@/components/ui/ImageAdvanced';
import VideoIcon from '@/components/ui/VideoIcon';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { calculateDynamicLayout, formatViewCount } from '@/utils/layoutUtils';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useCallback } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface VideoItemProps {
  mediaItem: any;
  itemIndex: number;
  isVisible: boolean;
  onVideoPress: (index: number) => void;
  layoutDimensions: ReturnType<typeof calculateDynamicLayout>;
}

const VideoItem: React.FC<VideoItemProps> = React.memo(({ 
  mediaItem, 
  itemIndex, 
  isVisible, 
  onVideoPress, 
  layoutDimensions 
}) => {
  const handlePress = useCallback(() => {
    onVideoPress(itemIndex);
  }, [itemIndex, onVideoPress]);

  return (
    <View style={{
      width: layoutDimensions.VIDEO_ITEM_WIDTH,
      height: layoutDimensions.VIDEO_ITEM_HEIGHT,
      borderRadius: 12,
      backgroundColor: Colors.background.secondary,
      position: 'relative',
      overflow: 'hidden',
    }}>
      <ImageAdvanced
        source={mediaItem.thumbnail || mediaItem.url}
        style={StyleSheet.absoluteFill}
        contentFit="cover"
        showPlaceholder={false}
        placeholderColor={Colors.background.secondary}
      />
      
      <View style={styles.playButtonOverlay}>
        <View style={styles.playIconContainer}>
          <VideoIcon size={24} color="rgba(255, 255, 255, 0.95)" />
        </View>
      </View>
      
      <View style={styles.videoBottomOverlay}>
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.6)']}
          locations={[0, 1]}
          style={styles.videoGradient}
        />
        <View style={styles.videoBottomSection}>
          <Text style={styles.videoViewsText} numberOfLines={1}>
            {formatViewCount(mediaItem.views || Math.floor(Math.random() * 100) + 1)}
          </Text>
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.videoTouchable}
        onPress={handlePress}
        activeOpacity={0.9}
      />
    </View>
  );
});

VideoItem.displayName = 'VideoItem';

export default VideoItem;

const styles = StyleSheet.create({
  playButtonOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  videoBottomOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 60,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    overflow: 'hidden',
    zIndex: 1,
  },
  videoGradient: {
    flex: 1,
  },
  videoTouchable: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 3,
  },
  playIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  videoBottomSection: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
  },
  videoViewsText: {
    color: 'white',
    fontSize: 11,
    fontFamily: Fonts.roboto.medium,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
