import ImageAdvanced from '@/components/ui/ImageAdvanced';
import { Colors } from '@/utils/colors';
import { Ionicons } from '@expo/vector-icons';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface Comment {
  id: number;
  author: string;
  avatar: string;
  text: string;
  timestamp: string;
  likes: number;
  replies?: Comment[];
}

interface CommentsProps {
  comments: Comment[];
}

const Comments: React.FC<CommentsProps> = ({ comments }) => {
  const formatTimestamp = (timestamp: string) => {
    // Since timestamps are now in simple format like "2h", "1h", just return them as is
    return timestamp;
  };

  const renderComment = (comment: Comment, isReply: boolean = false) => (
    <View key={comment.id} className={`${isReply ? 'ml-8 mt-2' : 'mb-4'}`}>
      <View className="flex-row items-start justify-between">
        <View className="flex-1 flex-row items-start gap-3 pr-3">
          <ImageAdvanced
            source={comment.avatar}
            style={isReply ? styles.replyAvatarImage : styles.avatarImage}
            contentFit="cover"
            transition={300}
            showPlaceholder={false}
            placeholderText="Loading image..."
            placeholderColor="#f0f0f0">
          </ImageAdvanced>
          <View className="flex-1">
            <Text className="font-roboto-medium text-base color-primary-950">{comment.author}</Text>
            <Text className="font-roboto-regular text-sm" numberOfLines={3} ellipsizeMode="tail">
              {comment.text}
            </Text>
            <View className="flex-row items-center gap-5 pt-2">
              <TouchableOpacity>
                <Text className="font-roboto-bold text-xs text-text-muted">Reply</Text>
              </TouchableOpacity>
              <TouchableOpacity>
                <Text className="font-roboto-bold text-xs text-text-muted">Like</Text>
              </TouchableOpacity>
              <Text className="font-roboto-regular text-xs text-text-muted">
                {formatTimestamp(comment.timestamp)}
              </Text>
            </View>
          </View>
        </View>
        <View className="flex-shrink-0">
          <TouchableOpacity onPress={() => {}} style={styles.optionsButton} activeOpacity={0.7}>
            <Ionicons name="ellipsis-vertical" size={16} color={Colors.text.muted} />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Render replies (max 1 level deep) */}
      {!isReply && comment.replies && comment.replies.length > 0 && (
        <View className="mt-2">
          {comment.replies.slice(0, 1).map(reply => renderComment(reply, true))}
          {comment.replies.length > 1 && (
            <TouchableOpacity className="ml-8 mt-2">
              <Text className="font-roboto-medium text-xs text-primary-500">
                View {comment.replies.length - 1} more replies
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );

  return (
    <View>
      {comments.map(comment => renderComment(comment))}
    </View>
  );
};

export default Comments;

const styles = StyleSheet.create({
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 100,
  },
  replyAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 100,
  },
  optionsButton: {
    padding: 5,
    borderRadius: 100,
    backgroundColor: Colors.background.primary,
  },
});
