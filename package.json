{"name": "wconnect", "main": "expo-router/entry", "version": "1.1.beta", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.1", "@expo-google-fonts/lexend": "^0.4.0", "@expo-google-fonts/roboto": "^0.4.0", "@expo/metro-config": "^0.20.17", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-community/datetimepicker": "8.4.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "expo": "~53.0.20", "expo-audio": "~0.4.8", "expo-blur": "~14.1.5", "expo-checkbox": "~4.1.4", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-media-library": "^17.1.7", "expo-router": "~5.1.4", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.10", "expo-squircle-view": "^1.1.0", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-video": "~2.2.2", "expo-web-browser": "~14.2.0", "glob": "^11.0.3", "lottie-react-native": "^7.2.2", "nativewind": "^4.1.23", "prettier-plugin-tailwindcss": "^0.5.11", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-bottomsheet-reanimated": "^0.3.1", "react-native-easing-gradient": "^1.1.1", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-iconify": "^2.0.3", "react-native-image-colors": "^2.5.0", "react-native-interactable-reanimated": "^0.0.15", "react-native-keyboard-controller": "^1.18.2", "react-native-pager-view": "^6.8.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "reanimated-collapsible-helpers": "^2.0.1", "rimraf": "^6.0.1", "rn-round-checkbox": "^1.0.0", "styled-components": "^6.1.19", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.20.0", "@iconify-json/solar": "^1.2.2", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}