import { Href, router } from 'expo-router';

// Simple debounce utility for navigation
let navigationTimeout: NodeJS.Timeout | null = null;
let isNavigating = false;

export const debounceNavigation = (path: Href, delay: number = 500) => {
  if (navigationTimeout) {
    clearTimeout(navigationTimeout);
  }

  navigationTimeout = setTimeout(() => {
    if (!isNavigating) {
      isNavigating = true;
      router.push(path);
      
      // Reset flag after a short delay
      setTimeout(() => {
        isNavigating = false;
      }, 1000);
    }
  }, delay);
};

// Simple guard for preventing rapid navigation
export const safeNavigate = (path: Href, cooldownMs: number = 1000) => {
  if (isNavigating) return;
  
  isNavigating = true;
  router.push(path);
  
  setTimeout(() => {
    isNavigating = false;
  }, cooldownMs);
};

// Example usage in components:
/*
// Instead of: router.push('/create-post')
// Use: debounceNavigation('/create-post', 300)
// Or: safeNavigate('/create-post', 1000)
*/ 