# Layout Folder

This folder contains layout components that define the structure and common UI elements across different screens.

## Purpose
- Shared layout components like headers, footers, navigation bars
- Screen wrappers and containers
- Common layout patterns used throughout the app
- Components that handle screen orientation and responsive design

## Examples
- `Header.tsx` - Common header component
- `TabLayout.tsx` - Tab navigation layout
- `DrawerLayout.tsx` - Side drawer navigation
- `ScreenWrapper.tsx` - Common screen container with padding/margins
