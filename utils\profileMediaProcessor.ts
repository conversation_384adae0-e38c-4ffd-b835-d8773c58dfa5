// Utility functions for processing profile media data
import { detectMediaType } from './mediaTypeDetection';

export interface MediaItem {
  url: string;
  type: 'image' | 'video';
  postId?: string | number;
  thumbnail?: string;
}

export interface ProcessedMediaData {
  images: MediaItem[];
  videos: MediaItem[];
  timeline: any[];
  exclusive: any[];
}

/**
 * Process posts data to extract and categorize media items
 * This function is flexible and won't break if data structure changes
 */
export const processProfileMedia = (posts: any[] = []): ProcessedMediaData => {
  const result: ProcessedMediaData = {
    images: [],
    videos: [],
    timeline: [],
    exclusive: []
  };

  if (!Array.isArray(posts)) {
    return result;
  }

  posts.forEach((post) => {
    // Safely access media array, handle both string arrays and object arrays
    const mediaArray = post?.media || [];
    const postId = post?.id || post?.postId || Math.random().toString();

    mediaArray.forEach((mediaItem: any) => {
      let mediaUrl: string;
      let mediaType: 'image' | 'video';
      let thumbnail: string | undefined;

      // Handle different media formats
      if (typeof mediaItem === 'string') {
        // Legacy format: just URL strings
        mediaUrl = mediaItem;
        mediaType = detectMediaType(mediaUrl);
      } else if (mediaItem && typeof mediaItem === 'object') {
        // New format: objects with url and optional type
        mediaUrl = mediaItem.url || mediaItem.source || mediaItem.uri || '';
        mediaType = mediaItem.type || detectMediaType(mediaUrl);
        thumbnail = mediaItem.thumbnail;
      } else {
        return; // Skip invalid media items
      }

      if (!mediaUrl) return; // Skip empty URLs

      const processedItem: MediaItem = {
        url: mediaUrl,
        type: mediaType,
        postId,
        thumbnail
      };

      // Categorize by type
      if (mediaType === 'image') {
        result.images.push(processedItem);
      } else if (mediaType === 'video') {
        result.videos.push(processedItem);
      }

      // Timeline includes all media items (for chronological view)
      result.timeline.push({
        ...processedItem,
        timestamp: post?.timestamp,
        content: post?.content,
        author: post?.name || post?.author
      });
    });
  });

  // Sort timeline by timestamp (newest first)
  result.timeline.sort((a, b) => {
    const dateA = new Date(a.timestamp || 0).getTime();
    const dateB = new Date(b.timestamp || 0).getTime();
    return dateB - dateA;
  });

  return result;
};

/**
 * Get content statistics for display
 */
export const getContentStats = (data: ProcessedMediaData) => {
  return {
    photos: data.images.length,
    videos: data.videos.length,
    timeline: data.timeline.length,
    exclusive: data.exclusive.length
  };
};
