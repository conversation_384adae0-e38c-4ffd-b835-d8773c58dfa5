const ProfileData = {
  profile: {
    id: '1',
    name: '<PERSON>',
    username: 'limgolden',
    musicProfile: {
      id: 'fromSpotify',
      name: '<PERSON><PERSON>',
      artist: '<PERSON>',
      coverImage:
        'https://upload.wikimedia.org/wikipedia/vi/5/52/Ros%C3%A9_and_<PERSON>_<PERSON>_-_Apt..png',
    },
    avatar:
      'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    coverImage:
      'https://images.samsung.com/vn/smartphones/galaxy-z-flip7/images/galaxy-z-flip7-features-selfie.jpg?imbypass=true',
    socialLinks: [
      {
        platform: 'GitHub',
        username: 'limgolden',
        url: 'github.com/limgolden',
      },
    ],
    career: 'Software Engineer & Gamer',
    location: 'Los Angeles, CA',
    education: 'Stanford University',
    collections: [
      {
        id: '1',
        title: 'My Travels',
        media: [
          'https://images.unsplash.com/photo-1682685797742-42c9987a2c34?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          'https://images.unsplash.com/photo-1663362464064-dd9908b5cf58?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          'https://example.com/a.mp4',
        ],
      },
      {
        id: '2',
        title: 'My Pets',
        media: [
          'https://images.unsplash.com/photo-1602233158242-3ba0ac4d2167?q=80&w=736&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          'https://images.unsplash.com/photo-1664464229780-5d4f367dd754?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        ],
      },
    ],
    friends: [
      {
        name: 'Alice',
        uri: 'https://images.unsplash.com/photo-1602233158242-3ba0ac4d2167?q=80&w=736&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: '1',
      },
      {
        name: 'John',
        uri: 'https://plus.unsplash.com/premium_photo-1664464229780-5d4f367dd754?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: '2',
      },
      {
        name: 'Anthony',
        uri: 'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: '3',
      },
      {
        name: 'Alice5',
        uri: 'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: '4',
      },
      {
        name: 'Alice4',
        uri: 'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        id: '5',
      },
    ],
  },
  posts: [
    {
      id: 1,
      name: 'Lim Golden',
      avatar: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      privacyStatus: '1',
      timestamp: '2025-07-12T07:30:00Z',
      content:
        "Just finished my first 10K run! 🏃‍♂️ Feeling exhausted but so accomplished. Who's up for a running challenge next weekend? #fitness #goals",
      media: [
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'https://images.unsplash.com/photo-1517649763962-0c623066013b?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'https://cdn.pixabay.com/video/2025/02/27/261244_large.mp4',
      ],
      videoInfo: [
        {
          id: '1',
          url: 'https://cdn.pixabay.com/video/2025/02/27/261244_large.mp4',
          views: 1500,
        },
      ],
      interaction: {
        likes: 220,
        hearts: 54,
        sad: 1,
        angry: 0,
        comments: 21,
        shares: 13,
      },
      isInteracted: {
        liked: true,
        hearted: false,
        sad: false,
        angry: false,
      },
      comments: [
        {
          id: 1,
          author: 'Sarah Johnson',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b1e05f1e?w=40&h=40&fit=crop&crop=face',
          text: "Congrats, John! That's amazing! 🥳",
          timestamp: '2025-07-12T07:45:00Z',
          likes: 0,
          replies: [
            {
              id: 2,
              author: 'Lim Golden',
              avatar: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?w=40&h=40&fit=crop&crop=face',
              text: 'Thanks! You should join next time!',
              timestamp: '2025-07-12T07:46:00Z',
              likes: 0,
              replies: [],
            },
          ],
        },
        {
          id: 3,
          author: 'Mike Wilson',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
          text: 'What was your time? I need some motivation!',
          timestamp: '2025-07-12T07:50:00Z',
          likes: 0,
          replies: [
            {
              id: 4,
              author: 'Lim Golden',
              avatar: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?w=40&h=40&fit=crop&crop=face',
              text: "1 hour 2 minutes! Let's beat it together!",
              timestamp: '2025-07-12T07:51:00Z',
              likes: 0,
              replies: [],
            },
          ],
        },
      ],
    },
    {
      id: 2,
      name: 'Lim Golden',
      avatar: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      privacyStatus: '1',
      timestamp: '2025-07-11T15:20:00Z',
      content:
        "Beautiful sunset today! 🌅 Nature never fails to amaze me. Perfect evening for some photography practice.",
      media: [
        'https://plus.unsplash.com/premium_photo-1748218891210-3610f2e11a12?q=80&w=735&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'https://plus.unsplash.com/premium_photo-1748783455056-cdc8758161c4?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      ],
      interaction: {
        likes: 156,
        hearts: 32,
        sad: 0,
        angry: 0,
        comments: 18,
        shares: 7,
      },
      isInteracted: {
        liked: false,
        hearted: true,
        sad: false,
        angry: false,
      },
      comments: [
        {
          id: 5,
          author: 'Alex Thompson',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
          text: "Amazing colors! 📸",
          timestamp: '2025-07-11T15:45:00Z',
          likes: 0,
          replies: [],
        },
      ],
    },
    {
      id: 3,
      name: 'Lim Golden',
      avatar: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      privacyStatus: '1',
      timestamp: '2025-07-10T12:15:00Z',
      content:
        "Cooking experiment time! 👨‍🍳 Trying out a new pasta recipe. Let's see how this turns out! #cooking #foodie",
      media: [
        'https://cdn.pixabay.com/video/2025/02/27/261244_large.mp4',
        'https://images.unsplash.com/photo-1551782450-17144efb9c50?q=80&w=869&auto=format&fit=crop',
      ],
      interaction: {
        likes: 89,
        hearts: 25,
        sad: 0,
        angry: 0,
        comments: 12,
        shares: 4,
      },
      isInteracted: {
        liked: true,
        hearted: false,
        sad: false,
        angry: false,
      },
      comments: [],
    },
  ],
  info: [
    {
      gender: 'Male',
      status: 1
    },
    {
      birthday: '20/10/1999',
      status: 0,
    },
    {
      hometown: 'England UK',
      status: 1,
    },
    {
      bio: "",
      status: 0
    },
    {
      subscription: {
        activate: 0,
        monthly: 'null',
        yearly: 'null',
        sales: []
      }
    },
    {
      pronouns: 'he/him/his',
      status: 2
    },
    {
      carriers: 'Actor/gamer',
      status: 1
    },
    {
      companies: [
        "Dreamer Studio",
        "Starry the sky"
      ],
      status: 0,
    },
    {
      education: "",
      status: 0
    },
    {
      relationship:[],
      status: 0,
    }
  ]
};

export default ProfileData;
