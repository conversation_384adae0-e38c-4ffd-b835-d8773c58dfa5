import Svg, { Path } from 'react-native-svg';
import { CustomSvgProps } from '../../types/svgTypes';


const InfoIcon = ({ size = 38, color = '#99A1BE', ...props }: CustomSvgProps) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 38 38"
    fill="none"
    {...props}
  >
    <Path
      fill={color}
      fillRule="evenodd"
      d="M18.81 14.18h-.018a1.426 1.426 0 0 1 0-2.85c.787 0 1.435.638 1.435 1.425 0 .786-.631 1.425-1.418 1.425Zm1.415 12.625a1.426 1.426 0 0 1-2.85 0v-7.4a1.426 1.426 0 0 1 2.85 0v7.4ZM18.8.88C5.124.88.275 5.729.275 19.405.275 33.08 5.124 37.93 18.8 37.93c13.676 0 18.525-4.849 18.525-18.525C37.325 5.729 32.476.88 18.8.88Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default InfoIcon;
