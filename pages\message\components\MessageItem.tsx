import { ImageAdvanced } from "@/components";
import ProfileData from "@/store/data/ProfileData";
import { Message, MessageStatus } from "@/types/media";
import { Colors } from "@/utils/colors";
import { Fonts } from "@/utils/fonts";
import { Spacing } from "@/utils/layoutStyles";
import { Ionicons } from "@expo/vector-icons";
import React, { useCallback, useEffect, useRef } from "react";
import { Animated, Dimensions, Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface MessageItemProps {
    message: Message;
    isLastInSequence: boolean; // Pre-calculated in parent
    contactAvatar?: string;
    visibleTimestampId: string | null;
    onToggleTimestamp: (messageId: string) => void;
}

const { width: screenWidth } = Dimensions.get('window');
const maxMediaWidth = screenWidth * 0.7;

const MessageItem: React.FC<MessageItemProps> = React.memo(({
    message,
    isLastInSequence,
    contactAvatar,
    visibleTimestampId,
    onToggleTimestamp
}) => {
    // Animation for timestamp
    const timestampAnimation = useRef(new Animated.Value(0)).current;
    const isTimestampVisible = visibleTimestampId === message.id;
    
    // Animate timestamp visibility
    useEffect(() => {
        Animated.timing(timestampAnimation, {
            toValue: isTimestampVisible ? 1 : 0,
            duration: 200,
            useNativeDriver: false, // We're animating height and opacity
        }).start();
    }, [isTimestampVisible, timestampAnimation]);
    
    // Stable onPress handler
    const handlePress = useCallback(() => {
        onToggleTimestamp(message.id);
    }, [message.id, onToggleTimestamp]);

    const renderMessageStatus = () => {
        if (!message.isOwn || !message.status) return null;

        const getStatusIcon = (status: MessageStatus) => {
            switch (status) {
                case 'sending':
                    return <Ionicons name="time-outline" size={12} color={Colors.text.muted} />;
                case 'sent':
                    return <Ionicons name="checkmark" size={12} color={Colors.text.muted} />;
                case 'delivered':
                    return <Ionicons name="checkmark-done" size={12} color={Colors.text.muted} />;
                case 'seen':
                    return <Ionicons name="checkmark-done" size={12} color={Colors.primary['500']} />;
                case 'failed':
                    return <Ionicons name="alert-circle" size={12} color={Colors.error} />;
                case 'blocked':
                    return <Ionicons name="ban" size={12} color={Colors.error} />;
                default:
                    return null;
            }
        };

        return (
            <View style={styles.statusContainer}>
                {getStatusIcon(message.status)}
            </View>
        );
    };

    const renderMediaContent = () => {
        if (!message.media || message.media.length === 0) return null;

        return (
            <View style={styles.mediaContainer}>
                {message.media.map((mediaItem, index) => {
                    const aspectRatio = mediaItem.width && mediaItem.height 
                        ? mediaItem.width / mediaItem.height 
                        : 1;
                    
                    const mediaWidth = Math.min(maxMediaWidth, mediaItem.width || maxMediaWidth);
                    const mediaHeight = mediaWidth / aspectRatio;

                    return (
                        <View key={index} style={[styles.mediaItem, { marginBottom: index < message.media!.length - 1 ? 4 : 0 }]}>
                            <Image 
                                source={{ uri: mediaItem.uri }} 
                                style={[
                                    styles.mediaImage, 
                                    { 
                                        width: mediaWidth, 
                                        height: Math.min(mediaHeight, 300),
                                        borderRadius: index === 0 ? (message.isOwn ? 12 : 12) : 8,
                                    }
                                ]}
                                resizeMode="cover"
                            />
                            {mediaItem.type === 'video' && (
                                <View style={styles.videoPlayButton}>
                                    <Ionicons name="play" size={24} color={Colors.background.primary} />
                                </View>
                            )}
                        </View>
                    );
                })}
            </View>
        );
    };

    // Early return for wave messages
    if (message.type === 'wave') {
        return (
            <View style={[styles.messageContainer, styles.ownMessage]}>
                <TouchableOpacity 
                    style={styles.messageRow}
                    onPress={handlePress}
                    activeOpacity={0.7}
                >
                    <View style={styles.waveMessage}>
                        <Text style={styles.waveEmoji}>👋</Text>
                        {message.isOwn && renderMessageStatus()}
                    </View>
                </TouchableOpacity>
                <Animated.View style={[
                    styles.timestampContainer,
                    {
                        opacity: timestampAnimation,
                        maxHeight: timestampAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, 30],
                        }),
                        transform: [{
                            translateY: timestampAnimation.interpolate({
                                inputRange: [0, 1],
                                outputRange: [-10, 0],
                            })
                        }]
                    }
                ]}>
                    <Text style={styles.timestamp}>{message.timestamp}</Text>
                </Animated.View>
            </View>
        );
    }

    // Handle media messages
    if (message.type === 'media') {
        return (
            <View style={[styles.messageContainer, message.isOwn ? styles.ownMessage : styles.otherMessage]}>
                <TouchableOpacity 
                    style={[styles.messageRow, message.isOwn ? styles.ownMessageRow : styles.otherMessageRow]}
                    onPress={handlePress}
                    activeOpacity={0.7}
                >
                    {!message.isOwn && isLastInSequence && (
                        <ImageAdvanced
                            source={contactAvatar || ProfileData.profile.avatar}
                            style={styles.messageAvatar}
                        />
                    )}
                    {!message.isOwn && !isLastInSequence && (
                        <View style={styles.messageAvatarPlaceholder} />
                    )}
                    <View style={[styles.mediaBubble, message.isOwn ? styles.ownBubble : styles.otherBubble]}>
                        {renderMediaContent()}
                        {message.text.trim() && (
                            <View style={styles.mediaTextContainer}>
                                <Text style={[styles.messageText, message.isOwn ? styles.ownText : styles.otherText, styles.mediaMessageText]}>
                                    {message.text}
                                </Text>
                                {message.isOwn && renderMessageStatus()}
                            </View>
                        )}
                        {!message.text.trim() && message.isOwn && renderMessageStatus()}
                    </View>
                </TouchableOpacity>
                <Animated.View style={[
                    styles.timestampContainer,
                    {
                        opacity: timestampAnimation,
                        maxHeight: timestampAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, 30],
                        }),
                        transform: [{
                            translateY: timestampAnimation.interpolate({
                                inputRange: [0, 1],
                                outputRange: [-10, 0],
                            })
                        }]
                    }
                ]}>
                    <Text style={styles.timestamp}>{message.timestamp}</Text>
                </Animated.View>
            </View>
        );
    }

    // Regular text messages
    return (
        <View style={[styles.messageContainer, message.isOwn ? styles.ownMessage : styles.otherMessage]}>
            <TouchableOpacity 
                style={[styles.messageRow, message.isOwn ? styles.ownMessageRow : styles.otherMessageRow]}
                onPress={handlePress}
                activeOpacity={0.7}
            >
                {!message.isOwn && isLastInSequence && (
                    <ImageAdvanced
                        source={contactAvatar || ProfileData.profile.avatar}
                        style={styles.messageAvatar}
                    />
                )}
                {!message.isOwn && !isLastInSequence && (
                    <View style={styles.messageAvatarPlaceholder} />
                )}
                <View style={[styles.messageBubble, message.isOwn ? styles.ownBubble : styles.otherBubble]}>
                    <View style={styles.textWithStatus}>
                        <Text style={[styles.messageText, message.isOwn ? styles.ownText : styles.otherText]}>
                            {message.text}
                        </Text>
                        {message.isOwn && renderMessageStatus()}
                    </View>
                </View>
            </TouchableOpacity>
            <Animated.View style={[
                styles.timestampContainer,
                {
                    opacity: timestampAnimation,
                    maxHeight: timestampAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, 30],
                    }),
                    transform: [{
                        translateY: timestampAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [-10, 0],
                        })
                    }]
                }
            ]}>
                <Text style={styles.timestamp}>{message.timestamp}</Text>
            </Animated.View>
        </View>
    );
}, (prevProps, nextProps) => {
    // Custom comparison function for React.memo
    return (
        prevProps.message.id === nextProps.message.id &&
        prevProps.message.text === nextProps.message.text &&
        prevProps.message.timestamp === nextProps.message.timestamp &&
        prevProps.message.isOwn === nextProps.message.isOwn &&
        prevProps.message.type === nextProps.message.type &&
        prevProps.message.status === nextProps.message.status &&
        JSON.stringify(prevProps.message.media) === JSON.stringify(nextProps.message.media) &&
        prevProps.isLastInSequence === nextProps.isLastInSequence &&
        prevProps.contactAvatar === nextProps.contactAvatar &&
        prevProps.visibleTimestampId === nextProps.visibleTimestampId
    );
});

MessageItem.displayName = 'MessageItem';

export default MessageItem;

const styles = StyleSheet.create({
    messageContainer: {
        marginVertical: Spacing.vertical.xs,
    },
    messageRow: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        marginBottom: 4,
    },
    ownMessageRow: {
        justifyContent: 'flex-end',
    },
    otherMessageRow: {
        justifyContent: 'flex-start',
    },
    ownMessage: {
        alignItems: 'flex-end',
    },
    otherMessage: {
        alignItems: 'flex-start',
    },
    messageAvatar: {
        width: 32,
        height: 32,
        borderRadius: 16,
        marginRight: 8,
    },
    messageAvatarPlaceholder: {
        width: 32,
        height: 32,
        marginRight: 8,
    },
    messageBubble: {
        maxWidth: '80%',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 20,
    },
    mediaBubble: {
        maxWidth: '80%',
        padding: 4,
        borderRadius: 20,
        overflow: 'hidden',
    },
    ownBubble: {
        backgroundColor: Colors.secondary['950'],
        borderBottomRightRadius: 6,
    },
    otherBubble: {
        backgroundColor: Colors.background.muted,
        borderBottomLeftRadius: 6,
    },
    messageText: {
        fontSize: 16,
        lineHeight: 20,
        fontFamily: Fonts.roboto.regular,
        flex: 1,
    },
    ownText: {
        color: Colors.background.primary,
        fontFamily: Fonts.roboto.regular,
    },
    otherText: {
        color: Colors.text.primary,
    },
    mediaMessageText: {
        marginTop: 8,
        paddingHorizontal: 12,
    },
    mediaTextContainer: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        paddingHorizontal: 12,
        paddingVertical: 8,
    },
    textWithStatus: {
        alignItems: 'flex-end',
        gap: 6,
    },
    statusContainer: {
        marginLeft: 4,
        justifyContent: 'flex-end',
    },
    timestampContainer: {
        overflow: 'hidden',
    },
    timestamp: {
        fontSize: 12,
        color: Colors.text.muted,
        marginTop: 4,
        textAlign: 'center',
        marginLeft: 40,
    },
    waveMessage: {
        backgroundColor: 'transparent',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        gap: 8,
    },
    waveEmoji: {
        fontSize: 32,
    },
    mediaContainer: {
        borderRadius: 16,
        overflow: 'hidden',
    },
    mediaItem: {
        position: 'relative',
    },
    mediaImage: {
        backgroundColor: Colors.background.muted,
    },
    videoPlayButton: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -15 }, { translateY: -15 }],
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderRadius: 20,
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
});