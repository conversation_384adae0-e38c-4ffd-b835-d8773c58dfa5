/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ['./App.{js,ts,tsx}',
        './components/**/*.{js,ts,tsx}',
        './pages/**/*.{js,ts,tsx}',
        './utils/**/*.{js,ts,tsx}'],

    presets: [require('nativewind/preset')],
    theme: {
        extend: {
            colors: {
                primary: {
                    50: '#eff6ff',
                    100: '#C3E4FF',
                    200: '#bfdbfe',
                    300: '#93c5fd',
                    400: '#60a5fa',
                    500: '#627fff', // Main primary color
                    600: '#2563eb',
                    700: '#1d4ed8',
                    800: '#1e40af',
                    900: '#1e3a8a',
                    950: '#172554'
                },
                secondary: {
                    50: '#F5FEFD',
                    100: '#f1f5f9',
                    200: '#e2e8f0',
                    300: '#cbd5e1',
                    400: '#94a3b8',
                    500: '#64748b', // Main secondary color
                    600: '#475569',
                    700: '#334155',
                    800: '#1e293b',
                    900: '#0f172a',
                },
                accent: '#10b981',
                success: '#22c55e',
                warning: '#f59e0b',
                error: '#ef4444',
                background: {
                    primary: '#ffffff',
                    secondary: '#f8fafc',
                },
                text: {
                    primary: '#1f2937',
                    secondary: '#6b7280',
                    muted: '#99A1BE',
                    muted2: '#535767'
                }
            },
            fontFamily: {
                'roboto': ['Roboto_400Regular', 'Roboto_500Medium', 'Roboto_700Bold'],
                'roboto-light': ['Roboto_300Light'],
                'roboto-regular': ['Roboto_400Regular'],
                'roboto-medium': ['Roboto_500Medium'],
                'roboto-bold': ['Roboto_700Bold'],
                'lexend': ['Lexend_400Regular', 'Lexend_500Medium', 'Lexend_600SemiBold', 'Lexend_700Bold'],
                'lexend-light': ['Lexend_300Light'],
                'lexend-regular': ['Lexend_400Regular'],
                'lexend-medium': ['Lexend_500Medium'],
                'lexend-semibold': ['Lexend_600SemiBold'],
                'lexend-bold': ['Lexend_700Bold'],
            },
        },
    },
    plugins: [],
};
