import ReactLikeButton from '@/components/ui/ReactLikeButton';
import { Alert, StyleSheet, Text, View } from 'react-native';

const ImmediateReactionTest = () => {
  const mockReactions = [
    {
      name: 'Like',
      id: 'like',
      source: require('../assets/images/icons/Like.png'),
    },
    {
      name: 'Heart',
      source: require('../assets/images/icons/Heart.png'),
      id: 'heart',
    },
    {
      name: 'Laugh',
      source: require('../assets/images/icons/Warm.png'),
      id: 'laugh',
    },
    {
      name: 'Sad',
      id: 'sad',
      emoji: '😢',
    },
    {
      name: 'Angry',
      id: 'angry',
      emoji: '😡',
    },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Immediate Reaction Test</Text>
      
      <View style={styles.instructions}>
        <Text style={styles.instructionText}>Test: Quick Reaction During Animation</Text>
        <Text style={styles.bullet}>1. <PERSON> press to open picker</Text>
        <Text style={styles.bullet}>2. IMMEDIATELY click on any reaction (don't wait for animation)</Text>
        <Text style={styles.bullet}>3. Should work instantly with close animation</Text>
      </View>

      <View style={styles.testArea}>
        <Text style={styles.testLabel}>Immediate Response Button:</Text>
        <ReactLikeButton
          postId={1}
          reactions={mockReactions}
          onReactionChange={(postId, reactionType) => {
            Alert.alert('Immediate Reaction!', `Selected: ${reactionType || 'None'}`);
          }}
        />
      </View>

      <View style={styles.improvements}>
        <Text style={styles.improvementsTitle}>Applied Improvements:</Text>
        <Text style={styles.bullet}>✓ Reactions start at 50% scale (immediately touchable)</Text>
        <Text style={styles.bullet}>✓ Picker starts at 30% scale (faster initial appearance)</Text>
        <Text style={styles.bullet}>✓ Larger touch areas with hitSlop</Text>
        <Text style={styles.bullet}>✓ Immediate close animation on selection</Text>
        <Text style={styles.bullet}>✓ No waiting for animations to complete</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  instructions: {
    backgroundColor: '#e3f2fd',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#2196f3',
  },
  instructionText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#1976d2',
  },
  bullet: {
    fontSize: 14,
    marginLeft: 8,
    marginBottom: 4,
    color: '#555',
  },
  testArea: {
    backgroundColor: 'white',
    padding: 32,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  testLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 24,
    color: '#333',
  },
  improvements: {
    backgroundColor: '#e8f5e8',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
  },
  improvementsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#2e7d32',
  },
});

export default ImmediateReactionTest;
