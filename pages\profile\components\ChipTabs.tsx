import { ChipGroup } from "@/components/chip/Chip";
import { useScreenDimensions } from "@/hooks/useScreenDimensions";
import { Colors } from "@/utils/colors";
import { Fonts } from '@/utils/fonts';
import { calculateDynamicLayout } from "@/utils/layoutUtils";
import { processProfileMedia, type ProcessedMediaData } from "@/utils/profileMediaProcessor";
import * as React from "react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  FlatList,
  Platform,
  StyleSheet,
  Text,
  View,
  type ListRenderItem
} from "react-native";
import MediaItem from './MediaItem';
import TimelinePostItem from './TimelinePostItem';

// Constants
const RENDER_BATCH_SIZE = Platform.OS === 'android' ? 3 : 6;

interface ChipTabsProps {
  posts?: any[];
  onOpenComments?: (post: any) => void;
}

interface MediaItemProps {
  item: any;
  index: number;
  isVisible: boolean;
  onVideoPress: (index: number) => void;
  layoutDimensions: ReturnType<typeof calculateDynamicLayout>;
}

// Item separator component
const ItemSeparator = React.memo(() => <View style={styles.itemSeparator} />);

const ChipTabs: React.FunctionComponent<ChipTabsProps> = ({ posts = [], onOpenComments }): React.ReactNode => {
  const [index, setIndex] = useState<number>(0);
  const [visibleIndices, setVisibleIndices] = useState<Set<number>>(new Set());
  
  const listRefs = useRef<{ [key: number]: FlatList | null }>({});
  
  // Get responsive screen dimensions
  const { width: screenWidth } = useScreenDimensions();
  
  // Calculate responsive layout dimensions
  const layoutDimensions = useMemo(() => {
    const dimensions = calculateDynamicLayout(screenWidth);
    
    // Debug info (remove in production)
    console.log('Layout calculated:', {
      screenWidth,
      ...dimensions,
    });
    
    return dimensions;
  }, [screenWidth]);
  
  // Memoize processed media data
  const mediaData = useMemo<ProcessedMediaData>(() => {
    try {
      return processProfileMedia(posts);
    } catch (error) {
      console.warn('Error processing media data:', error);
      return { images: [], videos: [], exclusive: [], timeline: [] };
    }
  }, [posts]);

  // Cleanup when switching tabs
  useEffect(() => {
    setVisibleIndices(new Set());
  }, [index]);

  // Memoized chip data
  const chipData = useMemo(() => [
    {
      label: "Photos",
      icon: 'solar:gallery-linear',
      activeIcon: 'solar:gallery-bold',
      activeColor: Colors.primary[950],
      iconType: 'iconify' as const,
      count: mediaData.images.length,
    },
    {
      label: "Videos",
      icon: "solar:video-library-line-duotone",
      activeIcon: "solar:video-library-bold-duotone",
      activeColor: Colors.primary[950],
      iconType: 'iconify' as const,
      count: mediaData.videos.length,
    },
    {
      label: "Timeline",
      icon: "solar:posts-carousel-vertical-linear",
      activeIcon: "solar:posts-carousel-vertical-bold",
      activeColor: Colors.primary[950],
      iconType: 'iconify' as const,
      count: posts.length,
    },
    {
      label: "Exclusive",
      icon: "solar:folder-favourite-star-linear",
      activeIcon: "solar:folder-favourite-star-bold",
      activeColor: Colors.primary[950],
      iconType: 'iconify' as const,
      count: mediaData.exclusive.length,
    },
  ], [mediaData, posts.length]);

  // Video press handler
  const handleVideoPress = useCallback((videoIndex: number) => {
    console.log('Video clicked:', videoIndex);
  }, []);

  // Tab change handler
  const handleChipChange = useCallback((newIndex: number) => {
    if (newIndex === index) return;
    setVisibleIndices(new Set());
    setIndex(newIndex);
  }, [index]);

  // Optimized viewability config
  const viewabilityConfig = useMemo(() => ({
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 100,
  }), []);

  const onViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: Array<{ index: number | null }> }) => {
    const newVisibleIndices = new Set(
      viewableItems
        .map((item) => item.index)
        .filter((idx): idx is number => idx !== null)
    );
    setVisibleIndices(newVisibleIndices);
  }, []);

  // Optimized render functions
  const renderMediaItem: ListRenderItem<any> = useCallback(({ item, index: itemIndex }) => (
    <MediaItem
      item={item}
      index={itemIndex}
      isVisible={visibleIndices.has(itemIndex)}
      onVideoPress={handleVideoPress}
      layoutDimensions={layoutDimensions}
    />
  ), [visibleIndices, handleVideoPress, layoutDimensions]);

  const renderTimelinePost: ListRenderItem<any> = useCallback(({ item, index: itemIndex }) => (
    <TimelinePostItem 
      item={item} 
      isVisible={visibleIndices.has(itemIndex)}
      onOpenComments={onOpenComments}
    />
  ), [visibleIndices, onOpenComments]);

  // Get item layout for grid performance
  const getGridItemLayout = useCallback((data: any, itemIndex: number) => {
    const rowIndex = Math.floor(itemIndex / 3);
    const isVideoTab = index === 1;
    const itemHeight = isVideoTab ? 
      layoutDimensions.VIDEO_ITEM_HEIGHT + layoutDimensions.GRID_GAP : 
      layoutDimensions.MEDIA_ITEM_SIZE + layoutDimensions.GRID_GAP;
    return {
      length: itemHeight,
      offset: itemHeight * rowIndex,
      index: itemIndex,
    };
  }, [index, layoutDimensions]);

  // Get timeline layout
  const getTimelineItemLayout = useCallback((data: any, itemIndex: number) => {
    const itemHeight = 400;
    return {
      length: itemHeight,
      offset: itemHeight * itemIndex,
      index: itemIndex,
    };
  }, []);

  // Key extractor functions
  const getMediaKey = useCallback((item: any, itemIndex: number) => 
    `${item.id || item.url?.slice(-10) || itemIndex}`, []);
  
  const getTimelineKey = useCallback((item: any, itemIndex: number) => 
    `timeline-${item.id || itemIndex}`, []);

  // Content renderer with optimizations
  const renderContent = useCallback(() => {
    const tabConfigs = {
      0: {
        data: mediaData.images,
        renderItem: renderMediaItem,
        keyExtractor: getMediaKey,
        numColumns: 3,
        getItemLayout: getGridItemLayout,
      },
      1: {
        data: mediaData.videos,
        renderItem: renderMediaItem,
        keyExtractor: getMediaKey,
        numColumns: 3,
        getItemLayout: getGridItemLayout,
      },
      2: {
        data: posts,
        renderItem: renderTimelinePost,
        keyExtractor: getTimelineKey,
        numColumns: 1,
        getItemLayout: getTimelineItemLayout,
      },
      3: {
        data: mediaData.exclusive,
        renderItem: renderMediaItem,
        keyExtractor: getMediaKey,
        numColumns: 3,
        getItemLayout: getGridItemLayout,
      },
    };

    const config = tabConfigs[index as keyof typeof tabConfigs];
    if (!config || config.data.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            No {chipData[index]?.label.toLowerCase()} available
          </Text>
        </View>
      );
    }

    const isTimeline = index === 2;
    
    return (
      <FlatList
        key={`flatlist-${index}-${config.numColumns}`}
        ref={(ref) => { listRefs.current[index] = ref; }}
        data={config.data}
        renderItem={config.renderItem}
        keyExtractor={config.keyExtractor}
        numColumns={config.numColumns}
        getItemLayout={config.getItemLayout}
        
        removeClippedSubviews={true}
        maxToRenderPerBatch={RENDER_BATCH_SIZE}
        windowSize={Platform.OS === 'android' ? 3 : 5}
        initialNumToRender={Platform.OS === 'android' ? 2 : 4}
        updateCellsBatchingPeriod={100}
        
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        
        contentContainerStyle={isTimeline ? styles.timelineContent : styles.gridContent}
        columnWrapperStyle={!isTimeline ? { gap: layoutDimensions.GRID_GAP, paddingHorizontal: 0 } : undefined}
        ItemSeparatorComponent={isTimeline ? ItemSeparator : undefined}
        
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        disableVirtualization={false}
        legacyImplementation={false}
        keyboardShouldPersistTaps="handled"
        maintainVisibleContentPosition={undefined}
      />
    );
  }, [
    index, 
    mediaData, 
    posts, 
    chipData, 
    renderMediaItem, 
    renderTimelinePost, 
    getMediaKey, 
    getTimelineKey, 
    getGridItemLayout, 
    getTimelineItemLayout,
    onViewableItemsChanged,
    viewabilityConfig,
    layoutDimensions
  ]);

  return (
    <View style={styles.container}>
      <View style={styles.chipContainer}>
        <ChipGroup
          chips={chipData as any}
          selectedIndex={index}
          onChange={handleChipChange}
          containerStyle={styles.chipGroupContainer}
        />
      </View>

      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
    </View>
  );
};

export default ChipTabs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  chipContainer: {
    marginBottom: 24,
  },
  chipGroupContainer: {
    paddingHorizontal: 0,
  },
  contentContainer: {
    flex: 1,
  },

  timelineContent: {
    paddingTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.text.muted,
    textAlign: 'center',
    fontFamily: Fonts.roboto.regular,
  },
  gridContent: {
    paddingTop: 8,
  },
  itemSeparator: {
    height: 12,
  },
});