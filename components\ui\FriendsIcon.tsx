import React from 'react';
import Svg, { SvgProps, Path, Defs, LinearGradient, Stop } from 'react-native-svg';

interface CustomSvgProps extends SvgProps {
  size?: number;
}

const FriendsIcon = ({ size = 54, ...props }: CustomSvgProps) => (
  <Svg
    width={size}
    height={(size * 49) / 54} // maintain aspect ratio based on original 54x49
    viewBox="0 0 54 49"
    fill="none"
    {...props}
  >
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M42.057 21.333h-.067a1.996 1.996 0 0 1-1.926-2.06c.085-2.526-1.15-4.166-3.305-4.386a1.992 1.992 0 1 1 .406-3.965c4.273.436 7.039 3.845 6.882 8.485a1.994 1.994 0 0 1-1.99 1.926Zm-.218-19.7C37.269.172 30.942.778 26.994 5.258 22.846.81 16.734.164 12.197 1.636 1.794 4.984-1.45 17.036 1.51 26.28v.003C6.18 40.82 21.712 48.661 27.03 48.661c4.748 0 20.895-7.695 25.513-22.38 2.96-9.242-.292-21.294-10.705-24.648Z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={27.026}
        x2={27.026}
        y1={-15.063}
        y2={76.605}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="red" />
        <Stop offset={1} stopColor="#8F00FF" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default FriendsIcon;
