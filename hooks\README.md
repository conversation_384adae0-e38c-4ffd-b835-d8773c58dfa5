# Hooks Folder

This folder contains custom React hooks that encapsulate reusable stateful logic.

## Purpose
- Custom hooks for shared state management
- Reusable logic that can be used across multiple components
- Data fetching hooks
- Device-specific hooks (camera, location, etc.)
- Form handling and validation hooks

## Naming Convention
All custom hooks should start with "use" prefix (e.g., useAuth, useLocation, useApi)

## Examples
- `useAuth.ts` - Authentication state and methods
- `useNetwork.ts` - Network connectivity status
- `usePermissions.ts` - Device permissions management
