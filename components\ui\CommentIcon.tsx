import Svg, { SvgProps, Path } from "react-native-svg"

interface CommentIconProps extends SvgProps {
  width?: number;
  height?: number;
  color?: string;
  dotColor?: string;
}

const CommentIcon = ({
  width = 35, 
  height = 35, 
  color = "#1877F2",
  dotColor = "#EFF8FF",
  ...props
}: CommentIconProps) => (
  <Svg
    width={width}
    height={height}
    fill="none"
    viewBox="0 0 35 35"
    {...props}
  >
    <Path
      fill={color}
      fillRule="evenodd"
      d="M29.311 29.515a17.748 17.748 0 0 1-8.174 4.471 13.522 13.522 0 0 1-10.42-1.465l-.37-.227-.087-.048a.407.407 0 0 0-.078-.024.42.42 0 0 0-.09 0h-.068l-.332.084H9.65c-.945.245-1.714.442-2.33.559a4.7 4.7 0 0 1-1.794.063 4.29 4.29 0 0 1-3.347-3.347 4.687 4.687 0 0 1 .063-1.795c.117-.619.314-1.384.56-2.33v-.041l.083-.332c.018-.072 0-.072 0-.072a.27.27 0 0 0-.024-.165s-.015-.03-.048-.086a12.177 12.177 0 0 0-.227-.368 13.458 13.458 0 0 1-1.528-10.357A17.69 17.69 0 0 1 5.612 5.81 16.763 16.763 0 0 1 29.32 29.515h-.009Z"
      clipRule="evenodd"
    />
    <Path
      fill={dotColor}
      d="M24.039 20.3a2.632 2.632 0 1 0 0-5.263 2.632 2.632 0 0 0 0 5.264ZM17.459 20.3a2.632 2.632 0 1 0 0-5.263 2.632 2.632 0 0 0 0 5.264ZM10.88 20.3a2.632 2.632 0 1 0 0-5.263 2.632 2.632 0 0 0 0 5.264Z"
    />
  </Svg>
)

export default CommentIcon