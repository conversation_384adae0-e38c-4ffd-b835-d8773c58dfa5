import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Animated,
  Dimensions,
  FlatList,
  ListRenderItem,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardAvoidingView, KeyboardProvider } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { runOnJS } from 'react-native-reanimated';
import { Colors } from '../../utils/colors';

// Import modular components and hooks
import CommentInput from './threaded-comments/CommentInput';
import CommentItem from './threaded-comments/CommentItem';
import EmptyCommentsState from './threaded-comments/EmptyCommentsState';
import { ITEM_HEIGHT } from './threaded-comments/constants';
import { FlattenedComment, ThreadedCommentsPopupProps } from './threaded-comments/types';
import { useBottomSheetGesture } from './threaded-comments/useBottomSheetGesture';
import { useCommentActions } from './threaded-comments/useCommentActions';
import { flattenComments } from './threaded-comments/utils';

const { height } = Dimensions.get('window');

// Main component
const OptimizedThreadedComments: React.FC<ThreadedCommentsPopupProps> = ({
  visible,
  onClose,
  postId,
  initialComments = [],
}) => {
  const insets = useSafeAreaInsets();
  const [isAtTop, setIsAtTop] = useState(true);
  const [isScrolling, setIsScrolling] = useState(false);

  // Custom hooks
  const {
    sheetHeight,
    backgroundOpacity,
    openSheet,
    closeSheet,
    expandToFull,
    isExpanded,
    onGestureEvent,
    onHandlerStateChange,
  } = useBottomSheetGesture(onClose);

  const {
    newComment,
    setNewComment,
    replyingTo,
    comments,
    textInputRef,
    flatListRef,
    handleReply,
    handleLike,
    addComment,
    cancelReply,
    resetComments,
  } = useCommentActions(initialComments, expandToFull, isExpanded);

  // Handle input focus with two-step process
  const handleInputPress = useCallback(() => {
    if (!isExpanded) {
      textInputRef.current?.blur();
      expandToFull(() => {
        textInputRef.current?.focus();
      });
    }
  }, [isExpanded, expandToFull, textInputRef]);

  // Enhanced close modal with state cleanup
  const closeModal = useCallback(() => {
    closeSheet();
    resetComments();
  }, [closeSheet, resetComments]);

  // Effect to handle opening/closing the bottom sheet
  useEffect(() => {
    if (visible) {
      openSheet();
    } else {
      closeSheet();
    }
  }, [visible, openSheet, closeSheet]);

  const getMentionDisplayName = useCallback((mentionText: string): string => {
    return mentionText.startsWith('@') ? mentionText.substring(1) : mentionText;
  }, []);

  // Flatten nested comments for FlatList
  const flatComments = useMemo(() => flattenComments(comments), [comments]);

  // Handle FlatList scroll to track position
  const handleScroll = useCallback((event: any) => {
    const { contentOffset } = event.nativeEvent;
    const newIsAtTop = contentOffset.y <= 10; // Small threshold for better UX
    setIsAtTop(newIsAtTop);
  }, []);

  const handleScrollBeginDrag = useCallback(() => {
    setIsScrolling(true);
  }, []);

  const handleScrollEndDrag = useCallback(() => {
    setIsScrolling(false);
  }, []);

  // Create pan gesture for the handle area
  const handlePanGesture = Gesture.Pan()
    .onUpdate((event) => {
      runOnJS(onGestureEvent)({ nativeEvent: event });
    })
    .onEnd((event) => {
      runOnJS(onHandlerStateChange)({ nativeEvent: { ...event, state: 5 } }); // State.END = 5
    });

  // Create pan gesture for FlatList area - only for dismissal when at top
  const flatListPanGesture = Gesture.Pan()
    .enabled(isAtTop && !isScrolling) // Only enable when at top and not actively scrolling
    .minDistance(15) // Require minimum distance before activation
    .failOffsetX([-20, 20]) // Fail if horizontal movement is too much
    .activeOffsetY(10) // Only activate on downward movement
    .onUpdate((event) => {
      // Only handle downward gestures with sufficient velocity
      if (event.translationY > 0 && event.velocityY >= 0) {
        runOnJS(onGestureEvent)({ nativeEvent: event });
      }
    })
    .onEnd((event) => {
      // Only handle downward gestures
      if (event.translationY > 0) {
        runOnJS(onHandlerStateChange)({ 
          nativeEvent: { ...event, state: 5 } // State.END = 5
        });
      }
    });

  const renderComment: ListRenderItem<FlattenedComment> = useCallback(
    ({ item }) => (
      <CommentItem 
        item={item} 
        onReply={handleReply} 
        onLike={handleLike} 
        getMentionDisplayName={getMentionDisplayName}
      />
    ),
    [handleReply, handleLike, getMentionDisplayName]
  );

  const getItemLayout = useCallback(
    (data: ArrayLike<FlattenedComment> | null | undefined, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    []
  );

  const keyExtractor = useCallback((item: FlattenedComment) => item.id, []);

  if (!visible) return null;

  return (
    <GestureHandlerRootView style={StyleSheet.absoluteFillObject}>
      {/* Background Overlay */}
      <Animated.View style={[styles.overlay, { opacity: backgroundOpacity }]}>
        <TouchableOpacity
          style={StyleSheet.absoluteFillObject}
          onPress={closeModal}
          activeOpacity={1}
        />
      </Animated.View>
      
      <Animated.View style={[styles.bottomSheet, { height: sheetHeight }]}>
        <KeyboardProvider>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={insets.bottom + 75}
            enabled={true}>
            <View style={styles.sheetContainer}>
              {/* Handle - Primary gesture area */}
              <GestureDetector gesture={handlePanGesture}>
                <View style={styles.handleContainer}>
                  <View style={styles.handle} />
                </View>
              </GestureDetector>

              {flatComments.length > 0 ? (
                <GestureDetector gesture={flatListPanGesture}>
                  <View style={styles.contentContainer}>
                    <FlatList
                      ref={flatListRef}
                      data={flatComments}
                      renderItem={renderComment}
                      keyExtractor={keyExtractor}
                      getItemLayout={getItemLayout}
                      removeClippedSubviews={true}
                      maxToRenderPerBatch={10}
                      updateCellsBatchingPeriod={50}
                      initialNumToRender={20}
                      windowSize={10}
                      style={styles.commentsList}
                      contentContainerStyle={[styles.commentsListContent, { paddingBottom: 20 }]}
                      showsVerticalScrollIndicator={false}
                      onScroll={handleScroll}
                      onScrollBeginDrag={handleScrollBeginDrag}
                      onScrollEndDrag={handleScrollEndDrag}
                      scrollEventThrottle={16}
                      bounces={true} // Enable bouncing for better gesture feel
                      alwaysBounceVertical={false} // Prevent unnecessary bounce when content is short
                      onScrollToIndexFailed={(info) => {
                        const wait = new Promise((resolve) => setTimeout(resolve, 500));
                        wait.then(() => {
                          flatListRef.current?.scrollToIndex({
                            index: info.index,
                            animated: true,
                            viewPosition: 1,
                            viewOffset: 0.5,
                          });
                        });
                      }}
                    />
                  </View>
                </GestureDetector>
              ) : (
                <View style={styles.emptyStateContainer}>
                  <EmptyCommentsState />
                </View>
              )}

              <CommentInput
                newComment={newComment}
                replyingTo={replyingTo}
                onCommentChange={setNewComment}
                onSubmit={addComment}
                onCancelReply={cancelReply}
                onInputPress={handleInputPress}
                isExpanded={isExpanded}
                textInputRef={textInputRef}
              />
            </View>
          </KeyboardAvoidingView>
        </KeyboardProvider>
      </Animated.View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'black',
    zIndex: 9999,
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    zIndex: 10000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 20,
  },
  sheetContainer: {
    flex: 1,
    maxHeight: '100%',
  },
  keyboardAvoidingView: {
    flex: 1,
    minHeight: 0,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: Colors.primary[500],
    borderRadius: 2,
  },
  contentContainer: {
    flex: 1,
  },
  commentsList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  commentsListContent: {
    flexGrow: 1,
    paddingTop: 8,
  },
  emptyStateContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
});

export default OptimizedThreadedComments;