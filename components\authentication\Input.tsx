import {StyleSheet, TextInput, TextInputProps, View, TouchableOpacity} from 'react-native';
import {Colors} from "@/utils/colors";
import {Fonts} from "@/utils/fonts";
import React, {useState} from 'react';

interface InputProps extends TextInputProps {
    placeholder?: string;
    inputType?: 'text' | 'password' | 'email' | 'number' | 'phone' | 'date';
    backgroundColor?: string;
    enableShadow?: boolean;
    shadowColor?: string;
    shadowOpacity?: number;
    shadowRadius?: number;
    elevation?: number;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    onRightIconPress?: () => void;
    onLeftIconPress?: () => void;
}

const Input = ({
                   placeholder,
                   inputType = 'text',
                   backgroundColor = Colors.background.primary,
                   enableShadow = true,
                   shadowColor = Colors.primary[500],
                   shadowOpacity = 0.1,
                   shadowRadius = 12,
                   elevation = 5,
                   leftIcon,
                   rightIcon,
                   onRightIconPress,
                   onLeftIconPress,
                   ...restProps
               }: InputProps) => {
    const [secureText, setSecureText] = useState(inputType === 'password');

    const shadowStyles = enableShadow ? {
        shadowColor,
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity,
        shadowRadius,
        elevation,
    } : {};

    return (
        <View style={[styles.inputContainer, {
            backgroundColor,
            ...shadowStyles,
        }]}>
            {leftIcon && (
                <TouchableOpacity
                    style={styles.leftIconContainer}
                    onPress={onLeftIconPress}
                    disabled={!onLeftIconPress}
                >
                    {leftIcon}
                </TouchableOpacity>
            )}

            <TextInput
                style={[
                    styles.input,
                    { textAlign: "center" },
                    leftIcon ? { paddingLeft: 10 } : null,
                    rightIcon ? { paddingRight: 10 } : null,
                ]}
                placeholder={placeholder}
                placeholderTextColor={Colors.text.muted}
                secureTextEntry={secureText}
                keyboardType={
                    inputType === 'email' ? 'email-address' :
                    inputType === 'number' ? 'numeric' :
                    inputType === 'phone' ? 'phone-pad' : 'default'
                }
                {...restProps}
            />

            {rightIcon && (
                <TouchableOpacity
                    style={styles.rightIconContainer}
                    onPress={onRightIconPress || (inputType === 'password' ? () => setSecureText(!secureText) : undefined)}
                    disabled={!onRightIconPress && inputType !== 'password'}
                >
                    {rightIcon}
                </TouchableOpacity>
            )}
        </View>
    );
}

export default Input;

const styles = StyleSheet.create({
    inputContainer: {
        width: '100%',
        height: 60,
        borderRadius: 16,
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: 'transparent',
        position: 'relative',
    },
    input: {
        flex: 1,
        height: '100%',
        color: "black",
        textAlignVertical: "center",
        paddingVertical: 0,
        fontSize: 16,
        fontFamily: Fonts.roboto.regular,
    },
    leftIconContainer: {
        paddingLeft: 15,
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    rightIconContainer: {
        paddingRight: 15,
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
});