export interface MediaItem {
  uri: string;
  type: 'image' | 'video';
  width?: number;
  height?: number;
  duration?: number;
  fileName?: string;
  fileSize?: number;
}

export interface MediaData {
  url: string;
  type: 'image' | 'video';
  thumbnail?: string;
}

export interface MediaOverlayParams {
  media: string; // JSON stringified array of MediaData
  focusIndex: string; // Index of initially focused media
}

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'seen' | 'failed' | 'blocked';

export interface Message {
  id: string;
  text: string;
  timestamp: string;
  isOwn: boolean;
  type?: 'text' | 'wave' | 'media';
  media?: MediaItem[];
  status?: MessageStatus;
  readAt?: string; // Timestamp when message was read
  deliveredAt?: string; // Timestamp when message was delivered
}

export interface ThemeData {
  type: 'image' | 'gradient' | 'dashboard' | null;
  source?: string;
  colors?: [string, string, ...string[]];
}
