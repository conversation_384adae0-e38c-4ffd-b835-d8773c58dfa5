import { Colors } from './colors';

export interface ShadowOptions {
  shadowColor?: string;
  shadowOpacity?: number;
  shadowRadius?: number;
  elevation?: number;
  offsetX?: number;
  offsetY?: number;
}

export const createShadow = (options: ShadowOptions = {}) => {
  const {
    shadowColor = Colors.primary[500],
    shadowOpacity = 0.1,
    shadowRadius = 12,
    elevation = 5,
    offsetX = 0,
    offsetY = 4,
  } = options;

  return {
    shadowColor,
    shadowOffset: {
      width: offsetX,
      height: offsetY,
    },
    shadowOpacity,
    shadowRadius,
    elevation,
  };
};

// Predefined shadow presets
export const shadows = {
  small: createShadow({
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    offsetY: 2,
  }),
  medium: createShadow({
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    offsetY: 4,
  }),
  large: createShadow({
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    offsetY: 10,
  }),
  primary: createShadow({
    shadowColor: Colors.primary[500],
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    offsetY: 4,
  }),
};
