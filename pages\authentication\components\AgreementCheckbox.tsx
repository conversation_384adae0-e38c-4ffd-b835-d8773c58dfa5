import { AnimatedCheckbox } from '@/components/check-box/CheckBox';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

type AgreementCheckboxProps = {
  isChecked: boolean;
  setChecked: (checked: boolean) => void;
};

const AgreementCheckbox: React.FC<AgreementCheckboxProps> = ({ isChecked, setChecked }) => {
  return (
    <View className={'w-full flex-row'} style={styles.container}>
      <View style={styles.checkboxContainer}>
        <AnimatedCheckbox
          checked={isChecked}
          onPress={() => setChecked(!isChecked)}
          size={14}
          borderRadius={22}
          activeColor={Colors.primary[950]}
          inactiveColor="transparent"
          borderColor={Colors.text.muted}
          borderWidth={1}
          checkMarkColor="#ffffff"
          animationDuration={200}
          bounceEffect={true}
        />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.agreementText}>
          I have read and agreed with{' '}
          <Text
            style={styles.linkText}
            onPress={() => console.log('Terms of Use pressed')}>
            Terms of use
          </Text>{' '}
          and{' '}
          <Text
            style={styles.linkText}
            onPress={() => console.log('Privacy Policy pressed')}>
            Privacy Policy
          </Text>
        </Text>
      </View>
    </View>
  );
};

export default AgreementCheckbox;

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
  },
  checkboxContainer: {
    marginTop: 3,
  },
  textContainer: {
    flex: 1,
    marginLeft: 8,
  },
  agreementText: {
    color: Colors.text.muted,
    fontFamily: Fonts.roboto.regular,
    fontSize: 12,
  },
  linkText: {
    color: Colors.primary[500],
    fontFamily: Fonts.roboto.regular,
    fontSize: 12,
  },
  checkbox: {
    width: 12,
    height: 12,
    borderColor: Colors.primary[500],
    backgroundColor: 'white',
  },
});
