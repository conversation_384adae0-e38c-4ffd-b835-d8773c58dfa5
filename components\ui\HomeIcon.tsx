import Svg, { Path } from 'react-native-svg';
import { CustomSvgProps } from '../../types/svgTypes';

const HomeIcon = ({ size = 24, color = '#627FFF', ...props }: CustomSvgProps) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 70 73"
    fill="none"
    {...props}>
    <Path
      fill={color}
      fillRule="evenodd"
      d="M44.595 53.265H24.638a2.575 2.575 0 0 1 0-5.148h19.957a2.575 2.575 0 0 1 0 5.148ZM59.494 16.45c-1.246-1.098-2.663-2.34-4.352-3.902-.765-.618-1.603-1.325-2.492-2.073C47.64 6.25 40.776.464 34.52.464c-6.185 0-12.607 5.464-17.765 9.853-.954.81-1.846 1.572-2.756 2.31-1.6 1.483-3.017 2.728-4.266 3.83C1.541 23.667 0 25.56 0 42.38c0 30.157 8.72 30.157 34.615 30.157 25.891 0 34.615 0 34.615-30.157 0-16.824-1.54-18.715-9.736-25.929Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default HomeIcon;
