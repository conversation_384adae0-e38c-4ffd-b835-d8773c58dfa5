import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { ContentContainers, FlexLayouts, Spacing } from '@/utils/layoutStyles';
import { shadows } from '@/utils/shadows';
import React from 'react';
import { StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';

interface ShortcutButtonProps {
  icon: React.ReactNode;
  label: string;
  width?: number;
  height?: number;
  isNew?: boolean;
  isNotified?: boolean;
  onPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  testID?: string;
}

const ShortcutButton: React.FC<ShortcutButtonProps> = ({
  icon,
  label,
  width,
  height,
  isNew = false,
  isNotified = false,
  onPress,
  disabled = false,
  style,
  containerStyle,
  labelStyle,
  testID,
}) => {
  const handlePress = () => {
    if (!disabled && onPress) {
      onPress();
    }
  };

  return (
    <View style={[styles.shadowContainer, { width, height }, style]}>
      <TouchableOpacity
        style={[styles.touchable, disabled && styles.disabled]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.8}
        testID={testID}
        accessibilityRole="button"
        accessibilityLabel={label}
        accessibilityHint={isNew ? `${label}, new feature` : undefined}>
        <View
          style={[
            styles.container,
            FlexLayouts.rowStart,
            ContentContainers.padded,
            containerStyle,
          ]}>
          <View style={styles.iconContainer}>{icon}</View>
          <Text
            style={[styles.label, disabled && styles.labelDisabled, labelStyle]}
            numberOfLines={2}
            ellipsizeMode="tail">
            {label}
          </Text>

          {(isNew || isNotified) && (
            <View style={styles.badgeContainer}>
              {isNew && (
                <View style={styles.newTag}>
                  <Text style={styles.newText}>NEW</Text>
                </View>
              )}
              {isNotified && <View style={styles.notificationDot} />}
            </View>
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default ShortcutButton;

const styles = StyleSheet.create({
  shadowContainer: {
    borderRadius: 14,
    minHeight: 60,
    ...shadows.large,
  },
  touchable: {
    flex: 1,
    borderRadius: 14,
    backgroundColor: Colors.background.primary,
    overflow: 'hidden',
  },
  container: {
    flex: 1,
    gap: Spacing.horizontal.sm,
    position: 'relative',
  },
  disabled: {
    opacity: 0.5,
    backgroundColor: Colors.background.muted,
  },
  iconContainer: {
    flexShrink: 0,
  },
  label: {
    fontSize: 14,
    fontFamily: Fonts.roboto.medium,
    color: Colors.secondary[950],
    flex: 1,
  },
  labelDisabled: {
    color: Colors.text.muted,
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  newTag: {
    backgroundColor: Colors.primary[500],
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  newText: {
    color: 'white',
    fontSize: 9,
    fontFamily: Fonts.roboto.bold,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  notificationDot: {
    backgroundColor: Colors.error,
    borderRadius: 6,
    width: 12,
    height: 12,
    borderWidth: 2,
    borderColor: Colors.background.primary,
  },
});
