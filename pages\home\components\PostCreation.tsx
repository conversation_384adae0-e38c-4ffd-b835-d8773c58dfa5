import ButtonPrimary from '@/components/ui/ButtonPrimary';
import CameraIcon from '@/components/ui/CameraIcon';
import EyesIcon from '@/components/ui/EyesIcon';
import VideoLiveIcon from '@/components/ui/VideoLiveIcon';
import { shadows } from '@/utils/shadows';
import { StyleSheet, Text, View } from 'react-native';

const PostCreation = () => {
  return (
    <View className="h-44 bg-white p-[9]" style={[{ borderRadius: 16 }, shadows.large]}>
      <Text className="p-3 font-roboto-regular text-lg text-text-muted">What's on your mind?</Text>
      <View className="flex-1 flex-row items-end justify-between pb-3 px-3 gap-2">
        <ButtonPrimary bgColor="#F1F4F5" enableShadow={false} style={styles.button}>
          <CameraIcon />
          <Text className="font-roboto-medium text-text-muted2" style={styles.buttonText} numberOfLines={1}>
            Media
          </Text>
        </ButtonPrimary>
        <ButtonPrimary bgColor="#F1F4F5" enableShadow={false} style={styles.button}>
          <VideoLiveIcon />
          <Text className="font-roboto-medium text-text-muted2" style={styles.buttonText} numberOfLines={1}>
            Live video
          </Text>
        </ButtonPrimary>
        <ButtonPrimary bgColor="#F1F4F5" enableShadow={false} style={styles.button}>
          <EyesIcon />
          <Text className="font-roboto-medium text-text-muted2" style={styles.buttonText} numberOfLines={1}>
            Check in
          </Text>
        </ButtonPrimary>
      </View>
    </View>
  );
};

export default PostCreation;

const styles = StyleSheet.create({
  button: {
    flex: 1,
    minWidth: 80,
    maxWidth: 120,
    height: 35,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    paddingHorizontal: 8,
  },
  buttonText: {
    fontSize: 12,
    textAlign: 'center',
    flexShrink: 1,
  },
});
