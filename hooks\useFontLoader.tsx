import { useEffect, useState } from 'react';
import { useFonts } from '@/utils/fonts';
import * as SplashScreen from 'expo-splash-screen';

SplashScreen.preventAutoHideAsync();

/**
 * Custom hook to handle font loading across the application
 * @returns {boolean} Whether fonts have been loaded
 */
export default function useFontLoader(): boolean {
  const fontsLoaded = useFonts();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // When fonts are loaded, update state and hide splash screen
    if (fontsLoaded) {
      setIsReady(true);
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  return isReady;
}
