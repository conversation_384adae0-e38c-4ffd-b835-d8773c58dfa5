import Svg, { SvgProps, G, Path, Defs } from "react-native-svg"

interface LikeIconProps extends SvgProps {
  width?: number;
  height?: number;
  color?: string;
}

const LikeIcon = ({ width = 49, height = 46, color = "#EFF8FF", ...props }: LikeIconProps) => (
  <Svg
    width={width}
    height={height}
    fill="none"
    viewBox="0 0 49 46"
    {...props}
  >
    <G filter="url(#a)">
      <Path
        fill={color}
        d="M35.603 15.12h-8.9l1.945-4.854a3.81 3.81 0 0 0-.817-4.121 2.891 2.891 0 0 0-4.394.364l-5.208 7.27a24.538 24.538 0 0 1-2.578 3.046l-1.664 1.653a.809.809 0 0 0-.24.577v12.512a.809.809 0 0 0 .24.577l1.863 1.872a5.094 5.094 0 0 0 3.625 1.502h10.133a4.14 4.14 0 0 0 3.776-2.457l4.965-11.171a5.528 5.528 0 0 0 .48-2.265v-1.268a3.237 3.237 0 0 0-3.236-3.237h.01Z"
      />
    </G>
    <G filter="url(#b)">
      <Path
        fill={color}
        d="M8.166 17.391A2.937 2.937 0 0 0 5.23 20.34v11.058a2.937 2.937 0 0 0 5.875 0V20.34a2.937 2.937 0 0 0-2.938-2.937v-.01Z"
      />
    </G>
    <Defs></Defs>
  </Svg>
)

export default LikeIcon