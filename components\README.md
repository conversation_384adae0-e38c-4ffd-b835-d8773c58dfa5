# Components Folder

This folder contains reusable UI components that can be used across different screens and features.

## Purpose
- Houses all custom React Native components
- Promotes code reusability and consistency
- Contains atomic design elements like buttons, inputs, cards, etc.
- Components here should be generic and not tied to specific business logic

## Usage
Import components from this folder into your screens and features to maintain a consistent UI across the application.
