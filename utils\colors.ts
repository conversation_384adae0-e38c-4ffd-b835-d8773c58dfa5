export const Colors = {
    primary: {
        50: '#eff6ff',
        100: '#C3E4FF',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#627fff', // Main primary color
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
        950: "#627fff"
    },
    secondary: {
        50: '#F5FEFD',
        100: '#f1f5f9',
        200: '#e2e8f0',
        300: '#cbd5e1',
        400: '#94a3b8',
        500: '#64748b', // Main secondary color
        600: '#475569',
        700: '#334155',
        800: '#1e293b',
        900: '#0f172a',
        950: '#2D3F7B'
    },
    accent: '#10b981',
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    background: {
        primary: '#ffffff',
        secondary: '#f8fafc',
        muted: '#F1F4F5',
        gray: '#99a1be'
    },
    text: {
        primary: '#1f2937',
        secondary: '#6b7280',
        muted: '#99A1BE',
    },
    shadows: {
        basic: '0px 2px 8px 0px rgba(99, 99, 99, 0.2)',
        blue: '3px 18px 39px 0px rgba(155, 191, 248, 0.1), 11px 70px 71px 0px rgba(155, 191, 248, 0.09), 25px 158px 96px 0px rgba(155, 191, 248, 0.05), 45px 282px 114px 0px rgba(155, 191, 248, 0.01), 70px 440px 125px 0px rgba(155, 191, 248, 0)',
        glowBlue: '0px 16px 36px 0px rgba(24, 119, 242, 0.1), 0px 65px 65px 0px rgba(24, 119, 242, 0.09), 0px 147px 88px 0px rgba(24, 119, 242, 0.05), 0px 261px 104px 0px rgba(24, 119, 242, 0.01), 0px 408px 114px 0px rgba(24, 119, 242, 0)'
    }
} as const;

export type ColorKey = keyof typeof Colors;

// Utility function to convert hex to RGB
const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

// Calculate luminance of a color
const getLuminance = (r: number, g: number, b: number): number => {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
};

// Get contrast ratio between two colors
const getContrastRatio = (color1: string, color2: string): number => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 1;
  
  const lum1 = getLuminance(rgb1.r, rgb1.g, rgb1.b);
  const lum2 = getLuminance(rgb2.r, rgb2.g, rgb2.b);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

// Determine if a background color is dark or light
export const isColorDark = (color: string): boolean => {
  const rgb = hexToRgb(color);
  if (!rgb) return false;
  
  const luminance = getLuminance(rgb.r, rgb.g, rgb.b);
  return luminance < 0.5;
};

// Get appropriate text color for a given background color
export const getContrastTextColor = (backgroundColor: string): string => {
  return isColorDark(backgroundColor) ? '#ffffff' : Colors.text.primary;
};

// Extract primary color from theme data for contrast detection
export const getThemeBackgroundColor = async (themeData: any): Promise<string | null> => {
  if (!themeData) return null;
  
  if (themeData.type === 'gradient' && themeData.colors && themeData.colors.length > 0) {
    // Use the first color of the gradient as the primary color
    return themeData.colors[0];
  }
  
  if (themeData.type === 'image' && themeData.source) {
    try {
      // Use react-native-image-colors to extract dominant color
      const ImageColors = require('react-native-image-colors').default;
      const result = await ImageColors.getColors(themeData.source, {
        fallback: '#444444',
        cache: true,
        key: themeData.source,
      });

      // Return the dominant color based on platform
      if (result.platform === 'android') {
        return result.dominant || '#444444';
      } else if (result.platform === 'ios') {
        return result.background || '#444444';
      } else {
        return result.dominant || '#444444';
      }
    } catch (error) {
      console.warn('Failed to extract color from image:', error);
      // Fallback to dark color for images
      return '#444444';
    }
  }
  
  return null;
};

// Synchronous version for immediate use (keeps existing API)
export const getThemeBackgroundColorSync = (themeData: any): string | null => {
  if (!themeData) return null;
  
  if (themeData.type === 'gradient' && themeData.colors && themeData.colors.length > 0) {
    // Use the first color of the gradient as the primary color
    return themeData.colors[0];
  }
  
  if (themeData.type === 'image') {
    // For images, assume dark background as fallback
    return '#444444';
  }
  
  return null;
};
