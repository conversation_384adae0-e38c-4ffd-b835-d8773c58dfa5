import useFontLoader from '@/hooks/useFontLoader';
import { Colors } from '@/utils/colors';
import { Stack } from 'expo-router';
import { ActivityIndicator, StatusBar, StyleSheet } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import '../global.css';

export default function RootLayout() {
  const fontsLoaded = useFontLoader();
  // @@iconify-code-gen

  if (!fontsLoaded) {
    return (
      <GestureHandlerRootView style={styles.container}>
        <ActivityIndicator size="large" color={Colors.primary[500]} />
      </GestureHandlerRootView>
    );
  }

  return (
    <GestureHandlerRootView style={styles.flex}>
      <StatusBar translucent={true} backgroundColor="transparent" barStyle="dark-content" />
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="login"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="signup"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="verification"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="create-profile"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="account-recovery"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="recreate-password"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(tabs)"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="profile"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="create-post"
          options={{
            headerShown: false,
            animation: 'none',
          }} />
        <Stack.Screen
          name="search"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="search-message"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="new-message"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="detail-info"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="info-edit"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="chat"
          options={{
            headerShown: false,
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="comments-modal"
          options={{
            headerShown: false,
            presentation: 'containedTransparentModal',
            animation: 'none',
          }}
        />
        <Stack.Screen
          name="media-modal"
          options={{
            headerShown: false,
            presentation: 'containedTransparentModal',
            animation: 'none',
          }}
        />
      </Stack>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  flex: {
    flex: 1,
  },
});
