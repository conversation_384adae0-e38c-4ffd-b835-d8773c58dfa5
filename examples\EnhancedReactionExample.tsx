import ReactLikeButton from '@/components/ui/ReactLikeButton';
import { ScrollView, StyleSheet, Text, View } from 'react-native';

const EnhancedReactionExample = () => {
  const mockReactions = [
    {
      name: 'Like',
      id: 'like',
      source: require('../assets/images/icons/Like.png'),
    },
    {
      name: 'Heart',
      source: require('../assets/images/icons/Heart.png'),
      id: 'heart',
    },
    {
      name: 'Laugh',
      source: require('../assets/images/icons/Warm.png'),
      id: 'laugh',
    },
    {
      name: 'Sad',
      id: 'sad',
      emoji: '😢',
    },
    {
      name: 'Angry',
      id: 'angry',
      emoji: '😡',
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Enhanced Reaction UX Test</Text>
        <Text style={styles.subtitle}>
          • Long press button to show reaction picker{'\n'}
          • Picker auto-closes when you scroll{'\n'}
          • Picker auto-closes after 10 seconds{'\n'}
          • Tap outside picker to close{'\n'}
          • Single tap toggles reaction
        </Text>
        
        {/* Create multiple posts for scrolling test */}
        {Array.from({ length: 10 }, (_, index) => (
          <View key={index} style={styles.postCard}>
            <Text style={styles.postTitle}>Test Post {index + 1}</Text>
            <Text style={styles.postContent}>
              This is a test post to demonstrate the enhanced reaction picker UX. 
              Try long pressing the reaction button and then scrolling - the picker should auto-close!
            </Text>
            <View style={styles.reactionContainer}>
              <ReactLikeButton
                postId={index}
                reactions={mockReactions}
                onReactionChange={(postId, reactionType) => {
                  console.log(`Post ${postId} reaction:`, reactionType);
                }}
              />
            </View>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
    lineHeight: 20,
  },
  postCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  postTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  postContent: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 12,
  },
  reactionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default EnhancedReactionExample;
