module.exports = function (api) {
  api.cache(true);
  let plugins = [];

  return {
    presets: [['babel-preset-expo', { jsxImportSource: 'nativewind' }], 'nativewind/babel'],

    plugins: [
      [
        'react-native-iconify/babel',
        {
          icons: [
            'solar:gallery-linear',
            'solar:gallery-bold',
            'solar:video-library-bold-duotone',
            'solar:video-library-line-duotone',
            'solar:posts-carousel-vertical-bold',
            'solar:posts-carousel-vertical-linear',
            'solar:folder-favourite-star-bold',
            'solar:folder-favourite-star-linear'
            // Add more icons here
          ],
        },
      ],
      'react-native-reanimated/plugin', // This should be the last plugin
    ],
  };
};
