import { Colors } from '@/utils/colors';
import { StyleSheet, Text, View } from 'react-native';
import CollectionList from './CollectionList';

interface CollectionProps {
  collections?: { id: string; title: string; media: string[]; }[];
}

const Collection: React.FC<CollectionProps> = ({ collections = [] }) => {
  const collectionCount = collections?.length || 0;
  
  return (
    <>
      <View className="mb-4 mt-1 pl-4">
        <Text className="font-roboto-medium text-[18px]" style={styles.textTitle}>
          Collection{' '}
          <Text className="font-roboto-regular text-sm text-text-muted">({collectionCount} sets)</Text>
        </Text>
      </View>
      <CollectionList collections={collections} />
    </>
  );
};

export default Collection;

const styles = StyleSheet.create({
  textTitle: {
    color: Colors.secondary[950],
  },
});
