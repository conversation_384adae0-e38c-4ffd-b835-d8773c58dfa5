import { Colors } from '@/utils/colors';
import { FlexLayouts } from '@/utils/layoutStyles';
import { shadows } from '@/utils/shadows';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StyleSheet, Text, View } from 'react-native';

interface ContainerTopNavigationTitleProps {
  title: string;
  isBackButton?: boolean;
  sideButton?: React.ReactNode;
}

const ContainerTopNavigationTitle = ({
  title,
  isBackButton = true,
  sideButton,
}: ContainerTopNavigationTitleProps) => {
  return (
    <View style={[FlexLayouts.rowBetween, styles.container]}>
      <View className="item-center flex-row justify-center gap-2">
        {isBackButton && (
          <Ionicons
            name="arrow-back"
            size={24}
            color={Colors.primary['950']}
            onPress={() => {
              router.back();
            }}
          />
        )}
        <Text className="font-roboto-bold text-2xl color-primary-500">{title}</Text>
      </View>
      <View>{sideButton}</View>
    </View>
  );
};

export default ContainerTopNavigationTitle;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.primary,
    height: 70,
    paddingHorizontal: 16,
    borderRadius: 14,
    ...shadows.large,
  },
});
