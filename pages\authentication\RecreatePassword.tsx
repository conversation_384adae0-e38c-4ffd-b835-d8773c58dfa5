import {StyleSheet, Text, View} from "react-native";
import ViewContainer from "@/components/ui/ViewContainer";
import {Fonts} from "@/utils/fonts";
import Input from "@/components/authentication/Input";
import {Colors} from "@/utils/colors";
import ButtonPrimary from "@/components/ui/ButtonPrimary";
import {Ionicons} from "@expo/vector-icons";
import {useState} from "react";
import {router} from "expo-router";
import { usePasswordValidation } from "@/hooks/usePasswordValidation";

const RecreatePasswordPage = () => {
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [password, setPassword] = useState("");

    const { hasMinLength, hasSpecialChar, hasNumberAndUpper, isValid: isPasswordValid } = usePasswordValidation(password);

    return (
        <View className={"flex-1 bg-background-primary"}>
            <ViewContainer classNameCustom={"h-1/4 items-center justify-end gap-2"}>
                <Text style={styles.title}>Create New Password</Text>
                <Text style={styles.subtitle}>Create a new password for your account</Text>
            </ViewContainer>
            <ViewContainer classNameCustom={"items-start justify-start pt-3 gap-4"}>
                <Input
                    enableShadow={false}
                    backgroundColor={Colors.background.muted}
                    placeholder="Enter your new password"
                    placeholderTextColor={Colors.text.muted}
                    inputType="password"
                    value={password}
                    onChangeText={setPassword}
                    rightIcon={
                        <Ionicons
                            name={passwordVisible ? "eye" : "eye-off"}
                            color={Colors.text.muted}
                            size={20}
                        />
                    }
                    onRightIconPress={() => setPasswordVisible(!passwordVisible)}
                    secureTextEntry={!passwordVisible}
                />
                <View className={"gap-2 w-full"}>
                    <View className={"flex-row gap-2"}>
                        <Ionicons
                            name={"checkmark"}
                            color={hasMinLength ? Colors.success : Colors.text.muted}
                            size={14}
                        />
                        <Text className={"text-[12px] font-roboto-regular text-text-muted"}>Your password must be 8
                            characters long</Text>
                    </View>
                    <View className={"flex-row gap-2"}>
                        <Ionicons
                            name={"checkmark"}
                            color={hasSpecialChar ? Colors.success : Colors.text.muted}
                            size={14}
                        />
                        <Text className={"text-[12px] font-roboto-regular text-text-muted"}>Your password must contains
                            special characters such as *, @, #</Text>
                    </View>
                    <View className={"flex-row gap-2"}>
                        <Ionicons
                            name={"checkmark"}
                            color={hasNumberAndUpper ? Colors.success : Colors.text.muted}
                            size={14}
                        />
                        <Text className={"text-[12px] font-roboto-regular text-text-muted"}>Your password must have
                            numbers and uppercase letter</Text>
                    </View>
                </View>
            </ViewContainer>
            <ViewContainer classNameCustom={"items-center justify-end h-32"}>
                <ButtonPrimary
                    bgColor={isPasswordValid ? Colors.primary[500] : Colors.text.muted}
                    border={"0px solid"}
                    borderColor="transparent"
                    onPress={() => isPasswordValid && router.replace("/login")}
                    disabled={!isPasswordValid}
                >
                    <Text className={"text-white text-[16px]"}>Complete</Text>
                </ButtonPrimary>
            </ViewContainer>
            <ViewContainer classNameCustom={"flex-1 items-center justify-end"}>
                <ButtonPrimary
                    bgColor={Colors.background.primary}
                    border="1px solid"
                    borderColor={Colors.text.muted}
                    enableShadow={false}
                    onPress={() => router.back()}
                >
                    <Text className={"text-black text-[16px]"}>Back</Text>
                </ButtonPrimary>
            </ViewContainer>
        </View>
    );
}

export default RecreatePasswordPage;

const styles = StyleSheet.create({
    title: {
        fontFamily: Fonts.roboto.bold,
        fontSize: 20,
        lineHeight: 24
    },
    subtitle: {
        fontFamily: Fonts.roboto.regular,
        fontSize: 14,
        color: Colors.text.muted
    }
})
