import { Platform, StatusBar } from 'react-native';
import { Colors } from './colors';

/**
 * Common Layout Styles and Constants
 * 
 * This file contains reusable layout patterns extracted from the WConnect app
 * Use these constants for consistent styling across all pages and components
 */

// ===== MAIN CONTAINER LAYOUTS =====
export const MainContainers = {
  // Most common main page container
  page: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  // Secondary background page
  pageSecondary: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  
  // Tab pages with bottom padding for tab bar
  tabPage: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    paddingBottom: Platform.OS === 'ios' ? 110 : 90,
  },
  
  // Centered content page (for empty states, loading, etc.)
  centeredPage: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    padding: 20,
  },
  
  // Transparent container
  transparent: {
    flex: 1,
    backgroundColor: 'transparent',
  },
} as const;

// ===== CONTENT CONTAINERS =====
export const ContentContainers = {
  // Standard content with horizontal padding
  standard: {
    paddingHorizontal: 16,
    flex: 1,
  },
  
  // Content with both horizontal and vertical padding
  padded: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    flex: 1,
  },
  
  // ViewContainer equivalent (px-4 py-2)
  viewContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  
  // Extended ViewContainer with auto margins
  viewContainerExtended: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 'auto' as const,
  },
  
  // Bottom sections (like CreatePostPage)
  bottomSection: {
    height: 210,
    gap: 8,
    padding: 16,
    justifyContent: 'flex-end' as const,
  },
  
  // Flexible bottom section
  bottomSectionFlex: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 8,
  },
} as const;

// ===== SAFE AREA CONTAINERS =====
export const SafeAreaContainers = {
  // Android safe area with status bar
  android: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  
  // Top container with status bar padding
  topSafe: {
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) : 0,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  
  // Top safe with margin top
  topSafeMargin: {
    marginTop: 8,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
} as const;

// ===== SCROLL CONTAINERS =====
export const ScrollContainers = {
  // Standard scroll content
  content: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  
  // Scroll content with tab bar padding
  contentWithTab: {
    paddingBottom: Platform.OS === 'ios' ? 110 : 90,
  },
  
  // Scroll content with horizontal padding
  contentPadded: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
} as const;

// ===== FLEX LAYOUTS =====
export const FlexLayouts = {
  // Row layouts
  rowBetween: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },
  
  rowCenter: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  
  rowStart: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'flex-start' as const,
  },
  
  rowEnd: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'flex-end' as const,
  },
  
  // Column layouts
  columnCenter: {
    flexDirection: 'column' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  
  columnStart: {
    flexDirection: 'column' as const,
    alignItems: 'center' as const,
    justifyContent: 'flex-start' as const,
  },
  
  columnBetween: {
    flexDirection: 'column' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },
  columnEnd: {
    flexDirection: 'column' as const,
    alignItems: 'flex-end' as const,
    justifyContent: 'flex-end' as const,
  },
} as const;

// ===== SPACING CONSTANTS =====
export const Spacing = {
  // Horizontal padding variations
  horizontal: {
    xs: 4,    // px-1
    sm: 8,    // px-2
    md: 16,   // px-4 (most common)
    lg: 20,   // px-5
    xl: 24,   // px-6
    xxl: 32,  // px-8
  },
  
  // Vertical padding variations
  vertical: {
    xs: 4,    // py-1
    sm: 8,    // py-2 (most common)
    md: 12,   // py-3
    lg: 16,   // py-4
    xl: 20,   // py-5
    xxl: 24,  // py-6
  },
  
  // Gap utilities (for flex layouts)
  gap: {
    xs: 2,    // gap-0.5
    sm: 4,    // gap-1
    md: 8,    // gap-2 (most common)
    lg: 12,   // gap-3
    xl: 16,   // gap-4
    xxl: 20,  // gap-5
  },
  
  // Margin utilities
  margin: {
    xs: 4,    // m-1
    sm: 8,    // m-2
    md: 12,   // m-3
    lg: 16,   // m-4 (most common)
    xl: 20,   // m-5
    xxl: 24,  // m-6
  },
} as const;

// ===== CARD CONTAINERS =====
export const CardContainers = {
  // Standard card
  standard: {
    borderRadius: 16,
    backgroundColor: Colors.background.primary,
  },
  
  // Card with shadow
  withShadow: {
    borderRadius: 16,
    backgroundColor: Colors.background.primary,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  
  // Muted card
  muted: {
    backgroundColor: Colors.background.muted,
    borderRadius: 12,
  },
  
  // Small card
  small: {
    borderRadius: 8,
    backgroundColor: Colors.background.primary,
  },
} as const;

// ===== LAYOUT DIMENSIONS =====
export const LayoutDimensions = {
  // From layoutUtils.ts
  CONTAINER_HORIZONTAL_PADDING: 32, // 16px on each side
  GRID_COLUMNS: 3,
  MIN_GAP: 4,
  VIDEO_ASPECT_RATIO: 192 / 110,
  
  // Common dimensions
  TAB_BAR_HEIGHT_IOS: 110,
  TAB_BAR_HEIGHT_ANDROID: 90,
  HEADER_HEIGHT: 60,
  BUTTON_HEIGHT: 48,
  INPUT_HEIGHT: 50,
} as const;

// ===== UTILITY FUNCTIONS =====
export const LayoutUtils = {
  // Get tab bar bottom padding
  getTabBarPadding: () => Platform.OS === 'ios' ? 110 : 90,
  
  // Get status bar height
  getStatusBarHeight: () => Platform.OS === 'android' ? (StatusBar.currentHeight || 0) : 0,
  
  // Combine styles helper
  combineStyles: (...styles: any[]) => styles.filter(Boolean),
  
  // Get safe area padding for top
  getSafeAreaTop: () => ({
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  }),
  
  // Get container with horizontal padding
  getHorizontalContainer: (paddingSize: keyof typeof Spacing.horizontal = 'md') => ({
    paddingHorizontal: Spacing.horizontal[paddingSize],
  }),
  
  // Get container with vertical padding
  getVerticalContainer: (paddingSize: keyof typeof Spacing.vertical = 'sm') => ({
    paddingVertical: Spacing.vertical[paddingSize],
  }),
} as const;

// ===== COMMON COMBINATIONS =====
export const CommonLayouts = {
  // Most used page layout
  standardPage: {
    ...MainContainers.page,
    ...ContentContainers.standard,
  },
  
  // Safe page with content padding
  safePage: {
    ...MainContainers.page,
    ...SafeAreaContainers.topSafe,
  },
  
  // Tab page with content
  tabPageWithContent: {
    ...MainContainers.tabPage,
    ...ContentContainers.standard,
  },
  
  // Centered page for empty states
  emptyStatePage: {
    ...MainContainers.centeredPage,
  },
  
  // Full screen with padding
  fullScreenPadded: {
    ...MainContainers.page,
    ...ContentContainers.padded,
  },
} as const;

export default {
  MainContainers,
  ContentContainers,
  SafeAreaContainers,
  ScrollContainers,
  FlexLayouts,
  Spacing,
  CardContainers,
  LayoutDimensions,
  LayoutUtils,
  CommonLayouts,
};
