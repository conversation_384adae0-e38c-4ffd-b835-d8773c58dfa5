import ImageAdvanced from '@/components/ui/ImageAdvanced';
import VerifiedIcon from '@/components/ui/VerifiedIcon';
import { Colors } from '@/utils/colors';
import { shadows } from '@/utils/shadows';
import { Ionicons } from '@expo/vector-icons';
import { StyleSheet, Text, View } from 'react-native';

interface AvatarTitleProps {
  name: string;
  avatarUrl: string;
  isVerified?: boolean;
  subTitle?: string[];
  onPress?: () => void;
}
const AvatarTitle = ({ name, avatarUrl, isVerified, subTitle }: AvatarTitleProps) => {
  return (
    <View className="h-fit w-full items-center justify-center gap-1">
      <View
        style={{
          ...shadows.large,
        }}
        className="h-52 w-52 items-center justify-center overflow-hidden rounded-full bg-white">
        <ImageAdvanced
          source={avatarUrl}
          style={styles.avatarImage}
          contentFit="cover"
          showPlaceholder={false}
        />
      </View>
      <View className="h-fit w-fit flex-row items-center justify-center">
        <Text className="font-roboto-bold text-[24px] text-primary-950">{name}</Text>
        {isVerified == true && (
          <VerifiedIcon color={Colors.primary[950]}></VerifiedIcon>
        )}
      </View>
      {subTitle && (
        <View className="flex-row flex-wrap items-center justify-center px-4">
          {subTitle.map((text, index) => (
            <View key={index} className="flex-row items-center">
              <Text
                className="font-roboto-regular text-sm text-text-muted"
                numberOfLines={1}
                ellipsizeMode="tail">
                {text}
              </Text>
              {index < subTitle.length - 1 && (
                <Text className="mx-2 font-roboto-regular text-sm text-text-muted">|</Text>
              )}
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

export default AvatarTitle;

const styles = StyleSheet.create({
  avatarImage: {
    width: 170,
    height: 170,
    borderRadius: 9999,
  },
});
