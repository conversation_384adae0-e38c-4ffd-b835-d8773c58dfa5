import Svg, { SvgProps, Path } from "react-native-svg"

interface ShareIconProps extends SvgProps {
  width?: number;
  height?: number;
  color?: string;
}

const ShareIcon = ({
  width = 34,
  height = 31,
  color = "#1877F2",
  ...props
}: ShareIconProps) => (
  <Svg
    width={width}
    height={height}
    fill="none"
    viewBox="0 0 34 31"
    {...props}
  >
    <Path
      fill={color}
      d="M33.237 13.318 21.098.716a1.4 1.4 0 0 0-2.396.972v6.056h-.467A18.227 18.227 0 0 0 .03 25.95v2.802a1.382 1.382 0 0 0 1.089 1.331c.1.026.205.038.309.038a1.449 1.449 0 0 0 1.278-.8A15.316 15.316 0 0 1 16.483 20.8h2.218v6.096a1.4 1.4 0 0 0 2.397.973l12.137-12.603a1.401 1.401 0 0 0 .002-1.947Z"
    />
  </Svg>
)

export default ShareIcon