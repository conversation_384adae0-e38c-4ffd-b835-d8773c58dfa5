import { ShimmerEffect } from '@/components';
import Divider from '@/components/ui/Divider';
import TopNavigationTitle from '@/components/ui/TopNavigationTitle';
import chatData from '@/store/data/ChatData';
import HeaderImage from '@/store/data/HeaderImage';
import { Colors } from '@/utils/colors';
import { FlexLayouts, MainContainers, SafeAreaContainers, Spacing } from '@/utils/layoutStyles';
import { shadows } from '@/utils/shadows';
import { memo, useCallback, useMemo, useState } from 'react';
import { FlatList, Pressable, StyleSheet, Text, TextInput, View } from 'react-native';
import DynamicGradientBackground from './components/DynamicMessageBackground';
import PeopleThread from './components/PeopleThread';

const Background = memo(() => <DynamicGradientBackground imageUri={HeaderImage.uri} />);
Background.displayName = 'Background';

const Header = memo(() => (
    <View style={SafeAreaContainers.topSafeMargin}>
        <TopNavigationTitle title="New message" isAdditionButton={false} />
    </View>
));
Header.displayName = 'Header';

interface ClearButtonProps {
    onPress: () => void;
    visible: boolean;
}

const ClearButton = memo(({ onPress, visible }: ClearButtonProps) => {
    if (!visible) return null;

    return (
        <Pressable onPress={onPress} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>✕</Text>
        </Pressable>
    );
});
ClearButton.displayName = 'ClearButton';

interface SearchResultsHeaderProps {
    hasQuery: boolean;
}

const SearchResultsHeader = () => {

    return (
        <View style={styles.searchResultsHeader}>
            <Text style={styles.searchResultsText}>People</Text>
        </View>
    );
};
SearchResultsHeader.displayName = 'SearchResultsHeader';

interface NoResultsMessageProps {
    hasQuery: boolean;
    hasResults: boolean;
}

const NoResultsMessage = memo(({ hasQuery, hasResults }: NoResultsMessageProps) => {
    if (!hasQuery || hasResults) return null;

    return (
        <View style={styles.noResultsContainer}>
            <Text style={styles.noResultsText}>No people found</Text>
            <Text style={styles.noResultsSubtext}>Try searching with different names</Text>
        </View>
    );
});
NoResultsMessage.displayName = 'NoResultsMessage';

interface SearchBarProps {
    isActive: boolean;
    searchQuery: string;
    onPress: () => void;
    onChangeText: (text: string) => void;
    onBlur: () => void;
    onClear: () => void;
}

const SearchBar = memo(
    ({ isActive, searchQuery, onPress, onChangeText, onBlur, onClear }: SearchBarProps) => {
        const handleClear = useCallback(() => {
            onClear();
        }, [onClear]);

        if (isActive) {
            return (
                <View style={styles.searchBarActive}>
                    <Text style={styles.toText}>To: </Text>
                    <TextInput
                        style={styles.searchInput}
                        value={searchQuery}
                        onChangeText={onChangeText}
                        placeholderTextColor={Colors.text.muted}
                        autoFocus={true}
                        onBlur={onBlur}
                    />
                    <ClearButton onPress={handleClear} visible={searchQuery.length > 0} />
                </View>
            );
        }

        return (
            <Pressable style={styles.searchBar} onPress={onPress}>
                <Text style={styles.toText}>To: </Text>
                <Text style={styles.searchPlaceholder}>{searchQuery}</Text>
            </Pressable>
        );
    }
);
SearchBar.displayName = 'SearchBar';

const NewMessagePage = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [filteredData, setFilteredData] = useState(chatData);
    const [isSearchActive, setIsSearchActive] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const shimmerConfig = {
        duration: 800,
        shimmerColors: [
            Colors.background.secondary,
            Colors.background.primary,
            Colors.background.secondary,
        ],
        variant: 'shimmer' as const,
        direction: 'leftToRight' as const,
    };

    const handleSearch = useCallback((text: any) => {
        setSearchQuery(text);
        setIsLoading(true);
        if (text.trim() === '') {
            setFilteredData(chatData);
        } else {
            const filtered = chatData.filter((item) =>
                item.name.toLowerCase().includes(text.toLowerCase())
            );
            setFilteredData(filtered);
        }
        setIsLoading(false);
    }, []);

    const handleSearchBarPress = useCallback(() => {
        setIsSearchActive(true);
    }, []);

    const handleSearchBlur = useCallback(() => {
        // Keep search active if there's text, only deactivate if empty
        if (searchQuery.trim() === '') {
            setIsSearchActive(false);
        }
        // If there's text, keep the search active even when keyboard is dismissed
    }, [searchQuery]);

    const clearSearch = useCallback(() => {
        setSearchQuery('');
        setFilteredData(chatData);
        setIsSearchActive(false);
        setIsLoading(false);
    }, []);

    const handlePersonPress = useCallback((person: any) => {
        console.log('Selected person:', person);
        // Handle navigation to chat with selected person
    }, []);

    const renderItem = useCallback(({ item, index }: { item: any; index: number }) => (
        <View>
            <View style={styles.personContainer}>
                <PeopleThread
                    item={{
                        id: item.id,
                        name: item.name,
                        avatar: item.avatar
                    }}
                    onPress={handlePersonPress}
                />
            </View>
            {index < filteredData.length - 1 && (
                <View style={styles.dividerContainer}>
                    <Divider />
                </View>
            )}
        </View>
    ), [filteredData.length, handlePersonPress]);

    const renderShimmerItem = useCallback(({ index }: { index: number }) => (
        <View key={index}>
            <View style={styles.shimmerPersonContainer}>
                <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm, flex: 1 }]}>
                    {/* Avatar shimmer */}
                    <ShimmerEffect
                        isLoading={isLoading}
                        {...shimmerConfig}
                        style={styles.shimmerAvatar}
                    />

                    {/* Name shimmer */}
                    <View style={styles.shimmerNameContainer}>
                        <ShimmerEffect
                            isLoading={isLoading}
                            {...shimmerConfig}
                            style={styles.shimmerName}
                        />
                    </View>
                </View>
            </View>
            {index < 5 && (
                <View style={styles.dividerContainer}>
                    <Divider />
                </View>
            )}
        </View>
    ), [isLoading, shimmerConfig]);

    const keyExtractor = useCallback((item: any) => item.id, []);

    const hasQuery = useMemo(() => searchQuery.trim() !== '', [searchQuery]);
    const hasResults = useMemo(() => filteredData.length > 0, [filteredData.length]);

    const ListHeaderComponent = useMemo(
        () => (
            <View>
                <View style={styles.searchContainer}>
                    <SearchBar
                        isActive={isSearchActive}
                        searchQuery={searchQuery}
                        onPress={handleSearchBarPress}
                        onChangeText={handleSearch}
                        onBlur={handleSearchBlur}
                        onClear={clearSearch}
                    />
                </View>

                <SearchResultsHeader />

                <NoResultsMessage hasQuery={hasQuery} hasResults={hasResults} />
            </View>
        ),
        [
            isSearchActive,
            searchQuery,
            hasQuery,
            hasResults,
            handleSearchBarPress,
            handleSearch,
            handleSearchBlur,
            clearSearch,
        ]
    );

    return (
        <View style={MainContainers.page}>
            <Background />
            <Header />

            <FlatList
                data={isLoading ? Array(6).fill(null) : filteredData}
                keyExtractor={isLoading ? (_, index) => `shimmer-${index}` : keyExtractor}
                renderItem={isLoading ? renderShimmerItem : renderItem}
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={ListHeaderComponent}
                removeClippedSubviews={true}
                maxToRenderPerBatch={10}
                updateCellsBatchingPeriod={100}
                initialNumToRender={10}
                windowSize={10}
                keyboardShouldPersistTaps="handled"
                keyboardDismissMode="on-drag"
            />
        </View>
    );
};

export default memo(NewMessagePage);

const styles = StyleSheet.create({
    searchContainer: {
        marginTop: 18,
        marginHorizontal: 16,
        marginBottom: 16,
    },
    searchBar: {
        backgroundColor: Colors.background.primary,
        borderRadius: 9999,
        paddingHorizontal: 16,
        paddingVertical: 8,
        height: 53,
        ...FlexLayouts.rowStart,
        gap: Spacing.horizontal.sm,
        ...shadows.medium,
        alignItems: 'center',
    },
    searchBarActive: {
        backgroundColor: Colors.background.primary,
        borderRadius: 9999,
        paddingHorizontal: 16,
        paddingVertical: 8,
        height: 53,
        ...FlexLayouts.rowStart,
        gap: Spacing.horizontal.sm,
        ...shadows.medium,
        alignItems: 'center',
    },
    toText: {
        color: Colors.text.primary,
        fontSize: 16,
        fontWeight: '500',
    },
    searchInput: {
        flex: 1,
        fontSize: 16,
        color: Colors.text.primary,
        padding: 0,
    },
    searchPlaceholder: {
        flex: 1,
        color: Colors.text.muted,
        fontSize: 16,
    },
    clearButton: {
        padding: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    clearButtonText: {
        color: Colors.text.muted,
        fontSize: 16,
        fontWeight: 'bold',
    },
    searchResultsHeader: {
        paddingHorizontal: 16,
        paddingBottom: 8,
    },
    searchResultsText: {
        color: Colors.text.muted,
        fontSize: 14,
        fontWeight: '600',
    },
    noResultsContainer: {
        alignItems: 'center',
        paddingVertical: 32,
        paddingHorizontal: 16,
    },
    noResultsText: {
        color: Colors.text.primary,
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    noResultsSubtext: {
        color: Colors.text.muted,
        fontSize: 14,
        textAlign: 'center',
    },
    personContainer: {
        paddingHorizontal: 16,
        paddingVertical: 3,
    },
    dividerContainer: {
        paddingHorizontal: 16,
    },
    // Shimmer styles
    shimmerPersonContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
    },
    shimmerAvatar: {
        width: 56,
        height: 56,
        borderRadius: 28,
        backgroundColor: Colors.background.secondary,
        overflow: 'hidden',
    },
    shimmerNameContainer: {
        flex: 1,
    },
    shimmerName: {
        height: 16,
        backgroundColor: Colors.background.secondary,
        borderRadius: 4,
        width: '60%',
    },
});