import { Colors, getContrastTextColor } from '@/utils/colors';
import React from 'react';
import { StyleSheet, TextInput } from 'react-native';

interface PostTextInputProps {
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  backgroundColor?: string;
  hasThemeBackground?: boolean;
}

const PostTextInput: React.FC<PostTextInputProps> = ({
  value,
  onChangeText,
  placeholder = "What's on your mind?",
  backgroundColor,
  hasThemeBackground = false,
}) => {
  // Calculate text color based on background
  const textColor = backgroundColor 
    ? getContrastTextColor(backgroundColor) 
    : Colors.text.primary;
  
  // Calculate placeholder color (slightly more transparent)
  const placeholderColor = backgroundColor 
    ? (getContrastTextColor(backgroundColor) === '#ffffff' ? 'rgba(255, 255, 255, 0.7)' : Colors.text.muted)
    : Colors.text.muted;

  return (
    <TextInput
      multiline={true}
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      numberOfLines={10}
      style={[
        styles.textInput,
        hasThemeBackground && styles.textInputWithBackground,
        { color: textColor }
      ]}
      placeholderTextColor={placeholderColor}
    />
  );
};

const styles = StyleSheet.create({
  textInput: {
    flex: 1,
    textAlignVertical: 'top',
  },
  textInputWithBackground: {
    padding: 16,
    paddingTop: 16,
    paddingBottom: 16,
  },
});

export default PostTextInput;
