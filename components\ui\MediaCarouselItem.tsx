import { MediaData } from '@/types/media';
import { Ionicons } from '@expo/vector-icons';
import { useEvent } from 'expo';
import { Image } from 'expo-image';
import { useVideoPlayer, VideoView } from 'expo-video';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import {
  Gesture,
  GestureDetector,
} from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MediaCarouselItemProps {
  media: MediaData;
  isActive: boolean;
  onSingleTap: () => void;
}

const MediaCarouselItem: React.FC<MediaCarouselItemProps> = ({
  media,
  isActive,
  onSingleTap,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [videoError, setVideoError] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [showVideoControls, setShowVideoControls] = useState(false);

  // Video Player Setup
  const player = useVideoPlayer(media.type === 'video' ? media.url : null, (player) => {
    player.loop = true;
    player.muted = true; // Start muted by default
  });

  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player?.playing || false,
  });

  const timeUpdateData = useEvent(player, 'timeUpdate', {
    currentTime: player?.currentTime || 0,
    bufferedPosition: player?.bufferedPosition || 0,
    currentLiveTimestamp: player?.currentLiveTimestamp || null,
    currentOffsetFromLive: player?.currentOffsetFromLive || null,
  });

  // Get current time from the event data or fallback to player property
  const currentTime = timeUpdateData?.currentTime || player?.currentTime || 0;
  // Get duration separately since it's not part of timeUpdate payload
  const duration = player?.duration || 0;

  // Handle video autoplay when becoming active
  useEffect(() => {
    if (media.type === 'video' && player) {
      if (isActive) {
        player.play();
        // Show controls briefly when video becomes active
        setShowVideoControls(true);
        const timer = setTimeout(() => setShowVideoControls(false), 3000);
        return () => clearTimeout(timer);
      } else {
        player.pause();
      }
    }
  }, [isActive, media.type, player]);

  // Single Tap: Calls the onSingleTap prop or shows video controls
  const singleTap = Gesture.Tap()
    .numberOfTaps(1)
    .maxDuration(250)
    .onEnd(() => {
      if (media.type === 'video') {
        runOnJS(() => {
          setShowVideoControls(true);
          const timer = setTimeout(() => setShowVideoControls(false), 3000);
        })();
      } else {
        runOnJS(onSingleTap)();
      }
    });

  // Long Press: Pause/Play video
  const longPress = Gesture.LongPress()
    .minDuration(500)
    .onStart(() => {
      if (media.type === 'video' && player) {
        if (isPlaying) {
          runOnJS(() => player.pause())();
        } else {
          runOnJS(() => player.play())();
        }
      }
    });

  // Gesture Composition - only single tap and long press
  const composed = Gesture.Race(longPress, singleTap);

  // Event Handlers
  const handleLoad = useCallback(() => setIsLoading(false), []);
  
  const handleVideoError = useCallback(() => {
    setIsLoading(false);
    setVideoError(true);
  }, []);

  const toggleMute = useCallback(() => {
    if (player) {
      const newMutedState = !isMuted;
      player.muted = newMutedState;
      setIsMuted(newMutedState);
    }
  }, [player, isMuted]);

  const handleProgressPress = useCallback((event: any) => {
    if (player && duration > 0) {
      const { locationX } = event.nativeEvent;
      const progressBarWidth = SCREEN_WIDTH - 80; // Account for padding
      const progress = locationX / progressBarWidth;
      const newTime = progress * duration;
      player.currentTime = newTime;
    }
  }, [player, duration]);

  // Render Functions
  const renderImage = () => (
    <Image
      source={{ uri: media.url }}
      style={styles.media}
      contentFit="contain"
      transition={300}
      onLoad={handleLoad}
      placeholder={{ blurhash: 'LGF5?+00J:0.~VxaJEtl0{s8NHj]' }}
    />
  );

  const renderVideoControls = () => {
    if (!showVideoControls || videoError) return null;

    const progress = duration > 0 ? currentTime / duration : 0;
    const progressBarWidth = SCREEN_WIDTH - 80;

    return (
      <View style={styles.videoControlsOverlay}>
        <View style={styles.topVideoControls}>
          <TouchableOpacity
            style={styles.muteButton}
            onPress={toggleMute}
            activeOpacity={0.7}
          >
            <Ionicons 
              name={isMuted ? "volume-mute" : "volume-high"} 
              size={24} 
              color="#fff" 
            />
          </TouchableOpacity>
        </View>
        
        <View style={styles.bottomVideoControls}>
          <TouchableOpacity
            style={styles.progressBarContainer}
            onPress={handleProgressPress}
            activeOpacity={1}
          >
            <View style={styles.progressBarBackground}>
              <View 
                style={[
                  styles.progressBarFill, 
                  { width: `${progress * 100}%` }
                ]}
              />
            </View>
          </TouchableOpacity>
          
          <View style={styles.timeContainer}>
            <Text style={styles.timeText}>
              {Math.floor(currentTime / 60)}:{String(Math.floor(currentTime % 60)).padStart(2, '0')} / {Math.floor(duration / 60)}:{String(Math.floor(duration % 60)).padStart(2, '0')}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderVideo = () => (
    <View style={styles.videoContainer}>
      <VideoView
        player={player}
        style={styles.media}
        nativeControls={false}
        contentFit="contain"
        allowsFullscreen={false}
        allowsPictureInPicture={false}
        onFirstFrameRender={handleLoad}
      />
      
      {renderVideoControls()}
      
      {videoError && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#fff" />
          <Text style={styles.errorText}>Failed to load video</Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <GestureDetector gesture={composed}>
        <View style={styles.gestureContainer}>
          {media.type === 'video' ? renderVideo() : renderImage()}
        </View>
      </GestureDetector>

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gestureContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  },
  media: {
    flex: 1,
  },
  videoContainer: {
    flex: 1,
  },
  videoControlsOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'space-between',
    pointerEvents: 'box-none',
  },
  topVideoControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 20,
    paddingTop: 60,
  },
  bottomVideoControls: {
    padding: 20,
    paddingBottom: 40,
  },
  muteButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressBarContainer: {
    marginBottom: 10,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressBarFill: {
    height: 4,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  timeContainer: {
    alignItems: 'center',
  },
  timeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default React.memo(MediaCarouselItem);