import { MediaData } from '@/types/media';
import { Ionicons } from '@expo/vector-icons';
import { useEvent } from 'expo';
import { Image } from 'expo-image';
import { useVideoPlayer, VideoView } from 'expo-video';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MediaCarouselItemProps {
  media: MediaData;
  isActive: boolean;
  onSingleTap: () => void;
}

const MediaCarouselItem: React.FC<MediaCarouselItemProps> = ({
  media,
  isActive,
  onSingleTap,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showVideoControls, setShowVideoControls] = useState(false);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Video Player Setup - Following Expo docs best practices
  const player = useVideoPlayer(media.type === 'video' ? media.url : null, (player) => {
    if (player) {
      player.loop = true;
      player.muted = true;
      player.timeUpdateEventInterval = 0.5; // Update every 500ms for smooth progress
    }
  });

  // Event listeners following Expo docs patterns
  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player.playing
  });

  const { status, error } = useEvent(player, 'statusChange', {
    status: player.status,
    error: null
  });

  const { currentTime } = useEvent(player, 'timeUpdate', {
    currentTime: player.currentTime
  });

  // Get duration and muted state directly from player
  const duration = player?.duration || 0;
  const isMuted = player?.muted || false;

  // Handle video autoplay when becoming active
  useEffect(() => {
    if (media.type === 'video' && player) {
      if (isActive) {
        player.play();
        showControlsTemporarily();
      } else {
        player.pause();
        hideControls();
      }
    }
  }, [isActive, media.type, player]);

  // Handle player status changes for error handling
  useEffect(() => {
    if (status === 'error') {
      setIsLoading(false);
      console.error('Video player error:', error);
    } else if (status === 'readyToPlay') {
      setIsLoading(false);
    }
  }, [status, error]);

  // Clean up controls timeout on unmount
  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, []);

  // Helper functions for controls
  const showControlsTemporarily = useCallback(() => {
    setShowVideoControls(true);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    controlsTimeoutRef.current = setTimeout(() => {
      setShowVideoControls(false);
    }, 3000);
  }, []);

  const hideControls = useCallback(() => {
    setShowVideoControls(false);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
  }, []);

  // Event Handlers
  const handleLoad = useCallback(() => setIsLoading(false), []);

  const toggleMute = useCallback(() => {
    if (player) {
      player.muted = !player.muted;
    }
  }, [player]);

  const togglePlayPause = useCallback(() => {
    if (player) {
      if (isPlaying) {
        player.pause();
      } else {
        player.play();
      }
    }
  }, [player, isPlaying]);

  const handleProgressPress = useCallback((event: any) => {
    if (player && duration > 0) {
      const { locationX } = event.nativeEvent;
      const progressBarWidth = SCREEN_WIDTH - 80; // Account for padding
      const progress = Math.max(0, Math.min(1, locationX / progressBarWidth));
      const newTime = progress * duration;
      player.currentTime = newTime;
      showControlsTemporarily();
    }
  }, [player, duration, showControlsTemporarily]);

  // Touch handlers for video interaction
  const handleVideoPress = useCallback(() => {
    if (media.type === 'video') {
      showControlsTemporarily();
    } else {
      onSingleTap();
    }
  }, [media.type, showControlsTemporarily, onSingleTap]);

  const handleVideoLongPress = useCallback(() => {
    if (media.type === 'video') {
      togglePlayPause();
      showControlsTemporarily();
    }
  }, [media.type, togglePlayPause, showControlsTemporarily]);

  // Render Functions
  const renderImage = () => (
    <Image
      source={{ uri: media.url }}
      style={styles.media}
      contentFit="contain"
      transition={300}
      onLoad={handleLoad}
      placeholder={{ blurhash: 'LGF5?+00J:0.~VxaJEtl0{s8NHj]' }}
    />
  );

  const renderVideoControls = () => {
    if (!showVideoControls || status === 'error') return null;

    const progress = duration > 0 ? (currentTime / duration) : 0;
    const safeProgress = Math.max(0, Math.min(1, progress));

    const formatTime = (timeInSeconds: number) => {
      const minutes = Math.floor(timeInSeconds / 60);
      const seconds = Math.floor(timeInSeconds % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    return (
      <View style={styles.videoControlsOverlay}>
        <View style={styles.topVideoControls}>
          <TouchableOpacity
            style={styles.muteButton}
            onPress={toggleMute}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isMuted ? "volume-mute" : "volume-high"}
              size={24}
              color="#fff"
            />
          </TouchableOpacity>
        </View>

        <View style={styles.bottomVideoControls}>
          <TouchableOpacity
            style={styles.progressBarContainer}
            onPress={handleProgressPress}
            activeOpacity={1}
          >
            <View style={styles.progressBarBackground}>
              <View
                style={[
                  styles.progressBarFill,
                  { width: `${safeProgress * 100}%` }
                ]}
              />
            </View>
          </TouchableOpacity>

          <View style={styles.timeContainer}>
            <Text style={styles.timeText}>
              {formatTime(currentTime)} / {formatTime(duration)}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderVideo = () => (
    <View style={styles.videoContainer}>
      <VideoView
        player={player}
        style={styles.media}
        nativeControls={false}
        contentFit="contain"
        allowsFullscreen={false}
        allowsPictureInPicture={false}
        onFirstFrameRender={handleLoad}
      />
      
      {renderVideoControls()}
      
      {videoError && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#fff" />
          <Text style={styles.errorText}>Failed to load video</Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <GestureDetector gesture={composed}>
        <View style={styles.gestureContainer}>
          {media.type === 'video' ? renderVideo() : renderImage()}
        </View>
      </GestureDetector>

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gestureContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  },
  media: {
    flex: 1,
  },
  videoContainer: {
    flex: 1,
  },
  videoControlsOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'space-between',
    pointerEvents: 'box-none',
  },
  topVideoControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 20,
    paddingTop: 60,
  },
  bottomVideoControls: {
    padding: 20,
    paddingBottom: 40,
  },
  muteButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressBarContainer: {
    marginBottom: 10,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressBarFill: {
    height: 4,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  timeContainer: {
    alignItems: 'center',
  },
  timeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default React.memo(MediaCarouselItem);