import {
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    Platform,
    KeyboardAvoidingView,
    ScrollView
} from "react-native";
import ViewContainer from "@/components/ui/ViewContainer";
import {Fonts} from "@/utils/fonts";
import Input from "@/components/authentication/Input";
import {Colors} from "@/utils/colors";
import ButtonPrimary from "@/components/ui/ButtonPrimary";
import {Ionicons} from "@expo/vector-icons";
import {useState} from "react";
import {router} from "expo-router";
import {usePasswordValidation} from "@/hooks/usePasswordValidation";
import {useDateValidation} from "@/hooks/useDateValidation";
import DateTimePicker, {DateTimePickerEvent} from '@react-native-community/datetimepicker';

const CreateProfilePage = () => {
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [password, setPassword] = useState("");
    const [name, setName] = useState("");

    const [date, setDate] = useState<Date | undefined>(undefined);
    const [dateString, setDateString] = useState<string>("");
    const [showDatePicker, setShowDatePicker] = useState(false);

    const {hasMinLength, hasSpecialChar, hasNumberAndUpper, isValid: isPasswordValid} = usePasswordValidation(password);
    const {isValid: isDateValid, isAdult, errorMessage: dateError} = useDateValidation(dateString);

    const onChangeDate = (event: DateTimePickerEvent, selectedDate?: Date) => {
        setShowDatePicker(Platform.OS === 'ios');

        if (event.type === 'dismissed') {
            return;
        }

        if (selectedDate) {
            setDate(selectedDate);
            const formattedDate = formatDate(selectedDate);
            setDateString(selectedDate.toISOString().split('T')[0]);
        }
    };

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const isFormValid = isPasswordValid && isDateValid && isAdult && name.trim().length > 0;

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={{flex: 1}}
            keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 25}
        >
            <ScrollView
                contentContainerStyle={{flexGrow: 1}}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
            >
                <View className={"flex-1 bg-background-primary"}>
                    <ViewContainer classNameCustom={"h-[15%] items-center justify-end gap-2"}>
                        <Text style={styles.title}>Create Profile</Text>
                    </ViewContainer>
                    <ViewContainer classNameCustom={"h-[130px] items-center justify-center"}>
                        <View style={styles.avatarContainer}>
                            <Image
                                source={require('@/assets/images/Avatar placeholder.png')}
                                style={styles.avatar}
                                resizeMode="cover"
                            />
                            <TouchableOpacity style={styles.editButton} activeOpacity={0.8}>
                                <Ionicons name="pencil" color="white" size={16}/>
                            </TouchableOpacity>
                        </View>
                    </ViewContainer>
                    <ViewContainer classNameCustom={"items-start justify-start pt-3 gap-4 pb-5"}>
                        <Input
                            enableShadow={false}
                            backgroundColor={Colors.background.muted}
                            placeholder="Enter your name"
                            placeholderTextColor={Colors.text.muted}
                            inputType={"text"}
                            value={name}
                            onChangeText={setName}
                        />
                        <View className="w-full">
                            <TouchableOpacity onPress={() => setShowDatePicker(true)} activeOpacity={0.8}>
                                <Input
                                    enableShadow={false}
                                    backgroundColor={Colors.background.muted}
                                    placeholder="Select your birth date"
                                    placeholderTextColor={Colors.text.muted}
                                    inputType={"text"}
                                    rightIcon={<Ionicons name="calendar" color={Colors.text.muted} size={20}/>}
                                    value={date ? formatDate(date) : ""}
                                    editable={false}
                                />
                            </TouchableOpacity>

                            {dateError && (
                                <Text className="text-[12px] font-roboto-regular text-error mt-1 ml-2">
                                    {dateError}
                                </Text>
                            )}

                            {showDatePicker && (
                                <DateTimePicker
                                    value={date || new Date()}
                                    mode="date"
                                    display="default"
                                    onChange={onChangeDate}
                                    maximumDate={new Date()}
                                />
                            )}
                        </View>
                        <Input
                            enableShadow={false}
                            backgroundColor={Colors.background.muted}
                            placeholder="Enter your new password"
                            placeholderTextColor={Colors.text.muted}
                            inputType="password"
                            value={password}
                            onChangeText={setPassword}
                            rightIcon={
                                <Ionicons
                                    name={passwordVisible ? "eye" : "eye-off"}
                                    color={Colors.text.muted}
                                    size={20}
                                />
                            }
                            onRightIconPress={() => setPasswordVisible(!passwordVisible)}
                            secureTextEntry={!passwordVisible}
                        />
                        <View className={"gap-2 w-full"}>
                            <View className={"flex-row gap-2"}>
                                <Ionicons
                                    name={"checkmark"}
                                    color={hasMinLength ? Colors.success : Colors.text.muted}
                                    size={14}
                                />
                                <Text className={"text-[12px] font-roboto-regular text-text-muted"}>Your password must
                                    be 8
                                    characters long</Text>
                            </View>
                            <View className={"flex-row gap-2"}>
                                <Ionicons
                                    name={"checkmark"}
                                    color={hasSpecialChar ? Colors.success : Colors.text.muted}
                                    size={14}
                                />
                                <Text className={"text-[12px] font-roboto-regular text-text-muted"}>Your password must
                                    contains
                                    special characters such as *, @, #</Text>
                            </View>
                            <View className={"flex-row gap-2"}>
                                <Ionicons
                                    name={"checkmark"}
                                    color={hasNumberAndUpper ? Colors.success : Colors.text.muted}
                                    size={14}
                                />
                                <Text className={"text-[12px] font-roboto-regular text-text-muted"}>Your password must
                                    have
                                    numbers and uppercase letter</Text>
                            </View>
                        </View>
                    </ViewContainer>
                    <ViewContainer classNameCustom={"items-center justify-end h-32"}>
                        <ButtonPrimary
                            bgColor={isFormValid ? Colors.primary[500] : Colors.text.muted}
                            border={"0px solid"}
                            borderColor="transparent"
                            onPress={() => isFormValid && router.replace("/login")}
                            disabled={!isFormValid}
                        >
                            <Text className={"text-white text-[16px]"}>Create Profile</Text>
                        </ButtonPrimary>
                    </ViewContainer>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );

}

export default CreateProfilePage;

const styles = StyleSheet.create({
    title: {
        fontSize: 26,
        fontFamily: Fonts.roboto.regular,
        color: "black",
        textAlign: "center",
    },
    avatarContainer: {
        width: 128,
        height: 128,
        borderRadius: 45,
        overflow: "hidden",
        position: "relative",
    },
    avatar: {
        width: "100%",
        height: "100%",
        borderRadius: 45,
    },
    editButton: {
        position: "absolute",
        bottom: 10,
        right: 10,
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: Colors.primary["950"],
        alignItems: "center",
        justifyContent: "center",
    },
})