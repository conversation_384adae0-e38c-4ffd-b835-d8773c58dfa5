import ButtonPrimary from '@/components/ui/ButtonPrimary';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface ActionButtonProps {
  iconName: keyof typeof Ionicons.glyphMap;
  label: string;
  iconColor?: string;
  onPress?: () => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  iconName,
  label,
  iconColor = Colors.primary[400],
  onPress
}) => {
  return (
    <ButtonPrimary
      bgColor="#F1F4F5"
      enableShadow={false}
      style={styles.button}
      onPress={onPress}
    >
      <Ionicons name={iconName} size={16} color={iconColor} />
      <Text
        className="font-roboto-medium text-text-muted2"
        style={styles.buttonText}
        numberOfLines={1}
      >
        {label}
      </Text>
    </ButtonPrimary>
  );
};

interface PostActionsProps {
  onCheckIn?: () => void;
  onMusic?: () => void;
  onLinks?: () => void;
}

const PostActions: React.FC<PostActionsProps> = ({
  onCheckIn,
  onMusic,
  onLinks
}) => {
  return (
    <View style={styles.container}>
      <ActionButton
        iconName="eye"
        label="Check-in"
        onPress={onCheckIn}
      />
      <ActionButton
        iconName="musical-note"
        label="Music"
        iconColor="red"
        onPress={onMusic}
      />
      <ActionButton
        iconName="link"
        label="Links"
        onPress={onLinks}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  button: {
    flex: screenWidth < 350 ? 0 : 1, // No flex on very small screens
    minWidth: screenWidth < 350 ? (screenWidth - 48) / 3 : 80, // Divide available width by 3 on small screens
    maxWidth: screenWidth > 500 ? 120 : undefined, // Larger max width on tablets
    height: 35,
    borderRadius: 9999999,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    paddingHorizontal: screenWidth < 350 ? 6 : 8, // Less padding on small screens
  },
  buttonText: {
    fontSize: screenWidth < 350 ? 12 : 14, // Smaller text on small screens
    textAlign: 'center',
    flexShrink: 1,
    fontFamily: Fonts.roboto.bold,
  },
});

export default PostActions;