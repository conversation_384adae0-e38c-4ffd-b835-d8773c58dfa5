import { Colors } from "@/utils/colors";
import { FlexLayouts } from "@/utils/layoutStyles";
import { Ionicons } from "@expo/vector-icons";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface InfoInputProps {
    label: string;
    value: string;
    isTextArea?: boolean;
    isIcon?: boolean;
    icon?: keyof typeof Ionicons.glyphMap;
    onPress?: () => void;
    maxLines?: number;
    placeholder?: string;
}

const InfoInput = ({
    label = '',
    value = '',
    isTextArea = false,
    isIcon = false,
    icon = 'chevron-down',
    onPress,
    maxLines = 3,
    placeholder
}: InfoInputProps) => {
    const showIcon = isIcon || isTextArea;
    const iconName = isTextArea ? 'chevron-down' : icon;

    return (
        <TouchableOpacity
            style={[
                FlexLayouts.rowBetween,
                styles.container,
                isTextArea && styles.textAreaContainer
            ]}
            activeOpacity={0.8}
            onPress={onPress}
        >
            <View style={[styles.contentContainer, isTextArea && styles.textAreaContent]}>
                <Text style={styles.textLabel}>{label}</Text>
                <Text
                    style={[
                        styles.textValue,
                        isTextArea && styles.textAreaValue,
                        !value && isTextArea && styles.placeholderText
                    ]}
                    numberOfLines={isTextArea ? maxLines : 1}
                    ellipsizeMode="tail"
                >
                    {value || (isTextArea ? (placeholder || 'Tap to add description...') : 'Not specified')}
                </Text>
            </View>
            {showIcon && (
                <View style={styles.iconContainer}>
                    <Ionicons
                        name={iconName as keyof typeof Ionicons.glyphMap}
                        size={20}
                        color={Colors.text.secondary || '#666'}
                    />
                </View>
            )}
        </TouchableOpacity>
    )
}

export default InfoInput;

const styles = StyleSheet.create({
    container: {
        borderWidth: 1,
        borderColor: '#E5E5E5',
        borderRadius: 16,
        padding: 16,
        minHeight: 60,
    },
    textAreaContainer: {
        minHeight: 80,
        alignItems: 'flex-start',
        paddingVertical: 16,
    },
    contentContainer: {
        flex: 1,
        marginRight: 12,
    },
    textAreaContent: {
        marginRight: 8,
    },
    textLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: Colors.text?.secondary || '#666',
        marginBottom: 4,
        letterSpacing: 0.5,
    },
    textValue: {
        fontSize: 16,
        fontWeight: '400',
        color: Colors.text?.primary || '#000',
        lineHeight: 20,
    },
    textAreaValue: {
        fontSize: 15,
        lineHeight: 22,
        color: Colors.text?.primary || '#333',
        flexWrap: 'wrap',
        height: 80,
    },
    iconContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 8,
    },
    placeholderText: {
        color: Colors.text?.primary || '#999',
        fontStyle: 'italic',
    },
});