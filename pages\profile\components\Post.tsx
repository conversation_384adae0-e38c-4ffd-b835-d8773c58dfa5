import { AvatarGroup } from '@/components/avatar-group/AvatarGroup';
import CommentIcon from '@/components/ui/CommentIcon';
import Divider from '@/components/ui/Divider';
import ImageAdvanced from '@/components/ui/ImageAdvanced';
import ReactLikeButton from '@/components/ui/ReactLikeButton';
import ShareIcon from '@/components/ui/ShareIcon';
import VideoIcon from '@/components/ui/VideoIcon';
import { useMediaNavigation } from '@/hooks/useMediaNavigation';
import { Colors } from '@/utils/colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface PostProps {
  post: {
    id: number;
    name: string;
    avatar: string;
    privacyStatus: string;
    timestamp: string;
    content: string;
    media:
      | Array<{
          url: string;
          type?: 'image' | 'video'; // Optional - will be auto-detected if not provided
          thumbnail?: string;
        }>
      | string[]; // Support both new and legacy format
    interaction: {
      likes: number;
      hearts: number;
      sad: number;
      angry: number;
      comments: number;
      shares: number;
    };
    isInteracted: {
      liked: boolean;
      hearted: boolean;
      sad: boolean;
      angry: boolean;
    };
    comments: any[];
  };
  onOpenComments?: (post: any) => void; // Make optional for profile component
}

const Post: React.FC<PostProps> = ({ post, onOpenComments }) => {
  const { openMediaOverlay } = useMediaNavigation();

  // Utility function to detect media type from URL using regex
  const detectMediaType = (url: string): 'image' | 'video' => {
    // Video file extensions (case insensitive)
    const videoExtensions = /\.(mp4|mov|avi|mkv|webm|flv|wmv|m4v|3gp|ogv)(\?.*)?$/i;

    // Image file extensions (case insensitive)
    const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg|tiff|ico)(\?.*)?$/i;

    // Check for video extensions first
    if (videoExtensions.test(url)) {
      return 'video';
    }

    // Check for image extensions
    if (imageExtensions.test(url)) {
      return 'image';
    }

    // Check for common video streaming patterns
    const videoStreamingPatterns = [
      /youtube\.com\/watch/i,
      /vimeo\.com/i,
      /twitch\.tv/i,
      /dailymotion\.com/i,
      /facebook\.com.*\/videos/i,
      /instagram\.com.*\/p\/.*\/media/i,
      /tiktok\.com/i,
      /\.m3u8/i, // HLS streaming
      /\.mpd/i, // DASH streaming
    ];

    if (videoStreamingPatterns.some((pattern) => pattern.test(url))) {
      return 'video';
    }

    // Default to image if can't determine
    return 'image';
  };

  // Normalize media data to ensure consistent format
  const normalizeMediaData = (
    media: Array<{ url: string; type?: 'image' | 'video'; thumbnail?: string }> | string[]
  ): Array<{ url: string; type: 'image' | 'video'; thumbnail?: string }> => {
    return media.map((item) => {
      if (typeof item === 'string') {
        // Legacy format - just a URL string
        return {
          url: item,
          type: detectMediaType(item),
        };
      } else {
        // New format - object with optional type
        return {
          url: item.url,
          type: item.type || detectMediaType(item.url),
          thumbnail: item.thumbnail,
        };
      }
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const formatInteractionCount = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}k`;
    return count.toString();
  };

  const totalReactions =
    post.interaction.likes +
    post.interaction.hearts +
    post.interaction.sad +
    post.interaction.angry;

  const reactionTypes = [
    {
      name: 'Like',
      id: 'like',
      source: require('../../../assets/images/icons/Like.png'), // Default like icon
    },
    {
      name: 'Heart',
      source: require('../../../assets/images/icons/Heart.png'),
      id: 'heart',
    },
    {
      name: 'Laugh',
      source: require('../../../assets/images/icons/Warm.png'),
      id: 'laugh',
    },
    {
      name: 'Sad',
      id: 'sad',
      emoji: '😢',
    },
    {
      name: 'Angry',
      id: 'angry',
      emoji: '😡',
    },
  ];

  // Simplified video component - no video player, just thumbnail with play button
  const VideoComponent = ({
    mediaItem,
    index,
    style,
  }: {
    mediaItem: { url: string; type: 'image' | 'video'; thumbnail?: string };
    index: number;
    style: any;
  }) => {
    const handlePress = () => {
      console.log('Video clicked:', index);
      const normalizedMedia = normalizeMediaData(post.media);
      openMediaOverlay(normalizedMedia, index);
    };

    return (
      <TouchableOpacity style={[style, styles.videoContainer]} onPress={handlePress} activeOpacity={0.9}>
        {/* Video thumbnail */}
        <ImageAdvanced
          source={mediaItem.thumbnail || mediaItem.url}
          style={style}
          contentFit="cover"
          transition={300}
          showPlaceholder={false}
          placeholderText="Loading video..."
          placeholderColor="#f0f0f0"
        />

        {/* Play button overlay */}
        <View style={styles.videoOverlay}>
          <View style={styles.playButton}>
            <VideoIcon size={40} color="rgba(255, 255, 255, 0.9)" />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMediaItem = (
    mediaItem: { url: string; type: 'image' | 'video'; thumbnail?: string },
    index: number,
    style: any
  ) => {
    const handleImagePress = () => {
      const normalizedMedia = normalizeMediaData(post.media);
      openMediaOverlay(normalizedMedia, index);
    };

    if (mediaItem.type === 'video') {
      return <VideoComponent key={index} mediaItem={mediaItem} index={index} style={style} />;
    } else {
      return (
        <TouchableOpacity key={index} onPress={handleImagePress} activeOpacity={0.9}>
          <ImageAdvanced
            source={mediaItem.url}
            style={style}
            contentFit="cover"
            transition={300}
            showPlaceholder={false}
            placeholderText="Loading image..."
            placeholderColor="#f0f0f0"
          />
        </TouchableOpacity>
      );
    }
  };

  const renderMedia = () => {
    if (post.media.length === 0) return null;

    // Normalize media data for consistent handling
    const normalizedMedia = normalizeMediaData(post.media);

    if (normalizedMedia.length === 1) {
      return (
        <View className="container mx-auto py-2">
          {renderMediaItem(normalizedMedia[0], 0, { width: '100%', height: 240, borderRadius: 16 })}
        </View>
      );
    }

    return (
      <View className='container mx-auto py-2'>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: 8 }}>
          {normalizedMedia.map((mediaItem, index) =>
            renderMediaItem(mediaItem, index, { width: 180, height: 240, borderRadius: 16 })
          )}
        </ScrollView>
      </View>
    );
  };

  return (
    <View className="h-fit w-full bg-secondary-50" style={styles.container}>
      <View className="flex-row items-start justify-between">
        <View className="flex-1 flex-row items-start gap-3 pr-3">
          <ImageAdvanced
            source={post.avatar}
            style={styles.avatarImage}
            contentFit="cover"
            transition={300}
            showPlaceholder={false}
            placeholderText="Loading image..."
            placeholderColor="#f0f0f0"></ImageAdvanced>
          <View className="flex-1">
            <Text
              className="font-roboto-medium text-lg color-primary-950"
              numberOfLines={1}
              ellipsizeMode="tail">
              {post.name}
            </Text>
            <View className="flex-row items-center justify-start gap-1">
              <Ionicons name="earth" size={14} color={Colors.text.muted} />
              <Text className="font-roboto-regular text-sm text-text-muted">
                {formatTimestamp(post.timestamp)}
              </Text>
            </View>
          </View>
        </View>
        <View className="flex-shrink-0">
          <TouchableOpacity onPress={() => {}} style={styles.optionsButton} activeOpacity={0.7}>
            <Ionicons name="ellipsis-horizontal" size={28} color={Colors.text.muted} />
          </TouchableOpacity>
        </View>
      </View>
      <View className="pt-3">
        <Text className="text-justify font-roboto-regular text-[16px] text-base leading-5 tracking-normal text-text-primary">
          {post.content}
        </Text>
      </View>
      {renderMedia()}
      <View className="container mx-auto py-2">
        <Text className="font-roboto-regular text-[12px] text-text-muted">
          {formatInteractionCount(post.interaction.comments)} Comments.{' '}
          {formatInteractionCount(post.interaction.shares)} Shares
        </Text>
      </View>
      <View className="container mx-auto mt-[-5] flex-row items-center justify-between py-2">
        <View className="flex-row items-center gap-4">
          <ReactLikeButton
            postId={post.id}
            initialReaction={post.isInteracted.liked ? 'like' : null}
            reactions={reactionTypes}
            onReactionChange={(postId, reactionType) => {
              console.log(`Post ${postId} reaction changed to:`, reactionType);
              // TODO: Update post state or call API
            }}
          />
          <TouchableOpacity
            className="bg-gray-100"
            onPress={() => onOpenComments && onOpenComments(post)}
            style={styles.likeButton}
            activeOpacity={0.7}>
            <CommentIcon width={24} height={24} color={Colors.primary[950]} />
          </TouchableOpacity>
          <TouchableOpacity
            className="bg-gray-100"
            onPress={() => {}}
            style={styles.likeButton}
            activeOpacity={0.7}>
            <ShareIcon width={24} height={24} color={Colors.primary[950]} />
          </TouchableOpacity>
        </View>
        <View className="flex-1 flex-row items-center justify-end gap-2">
          <Text className="font-roboto-regular text-[10px] text-text-muted">
            {formatInteractionCount(totalReactions)} others
          </Text>
          <AvatarGroup
            avatars={reactionTypes}
            size={24}
            max={3}
            showBorder={false}
            overlap={10}></AvatarGroup>
        </View>
      </View>
      <Divider></Divider>
    </View>
  );
};

export default React.memo(Post);

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
  },
  avatarImage: {
    width: 45,
    height: 45,
    borderRadius: 100,
  },
  optionsButton: {
    width: 45,
    height: 45,
    borderRadius: 100,
    backgroundColor: Colors.background.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  likeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  videoContainer: {
    position: 'relative',
    overflow: 'hidden',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
