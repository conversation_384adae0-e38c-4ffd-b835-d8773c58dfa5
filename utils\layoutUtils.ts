// Layout calculation utilities for grid and video components

// Calculate dynamic layout based on screen width
export const calculateDynamicLayout = (screenWidth: number) => {
  const CONTAINER_HORIZONTAL_PADDING = 32; // 16px on each side
  const GRID_COLUMNS = 3;
  const availableWidth = screenWidth - CONTAINER_HORIZONTAL_PADDING;
  
  // Start with minimum gap of 4px
  const minGap = 4;
  const mediaItemSize = Math.floor((availableWidth - (GRID_COLUMNS - 1) * minGap) / GRID_COLUMNS);
  
  // Calculate actual gap to fill remaining space evenly
  const totalItemWidth = mediaItemSize * GRID_COLUMNS;
  const remainingSpace = availableWidth - totalItemWidth;
  const dynamicGap = Math.max(minGap, Math.floor(remainingSpace / (GRID_COLUMNS - 1)));
  
  // For video items (maintain aspect ratio)
  const videoAspectRatio = 192 / 110; // height / width from original
  const videoItemSize = Math.floor((availableWidth - (GRI<PERSON>_COLUMNS - 1) * dynamicGap) / GRID_COLUMNS);
  const videoHeight = Math.floor(videoItemSize * videoAspectRatio);
  
  return {
    MEDIA_ITEM_SIZE: mediaItemSize,
    VIDEO_ITEM_WIDTH: videoItemSize,
    VIDEO_ITEM_HEIGHT: videoHeight,
    GRID_GAP: dynamicGap,
  };
};

// Utility function for view count formatting
export const formatViewCount = (count: number): string => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M Views`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K Views`;
  }
  return `${count} Views`;
}; 