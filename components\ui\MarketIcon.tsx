import Svg, { SvgProps, Path, Defs, LinearGradient, Stop } from 'react-native-svg';

interface CustomSvgProps extends SvgProps {
  size?: number;
}

const MarketIcon = ({ size = 52, ...props }: CustomSvgProps) => (
  <Svg width={size} height={(size * 55) / 52} viewBox="0 0 52 55" fill="none" {...props}>
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M33.936 28.69h-.122a2.658 2.658 0 0 1 0-5.315c1.472 0 2.718 1.217 2.718 2.657 0 1.491-1.14 2.658-2.596 2.658ZM16.774 12.474a9.395 9.395 0 0 1 2.652-5.147 9.428 9.428 0 0 1 6.682-2.748h.046c4.686 0 8.563 3.423 9.326 7.894-2.742-.369-5.82-.573-9.353-.573-3.531 0-6.61.204-9.353.573Zm1.669 16.216h-.123a2.66 2.66 0 0 1-2.657-2.658 2.66 2.66 0 0 1 2.657-2.657c1.475 0 2.719 1.217 2.719 2.657 0 1.491-1.14 2.658-2.596 2.658Zm30.713-10.01c-1.966-2.564-5.144-4.344-9.614-5.444C39.104 6.2 33.298.594 26.162.594H26.1a13.38 13.38 0 0 0-9.48 3.903c-2.36 2.338-3.69 5.407-3.906 8.74-4.472 1.096-7.65 2.877-9.616 5.443C.414 22.185-.05 27.002 1.68 33.411c3.994 14.816 6.337 20.826 24.448 20.826 18.108 0 20.451-6.01 24.448-20.826 1.727-6.409 1.264-11.226-1.42-14.73Z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={26.126}
        x2={26.126}
        y1={-17.739}
        y2={65.943}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#D400F7" />
        <Stop offset={1} stopColor="#7407FF" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default MarketIcon;
