import { MediaItem, Message, MessageStatus } from "@/types/media";
import { MainContainers } from "@/utils/layoutStyles";
import { useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useState } from "react";
import { Platform } from "react-native";
import { KeyboardAvoidingView, KeyboardProvider } from 'react-native-keyboard-controller';
import MessageInput from "./components/MessageInput";
import MessageList from "./components/MessageList";
import NewMessagePrompt from "./components/NewMessagePrompt";
import TopBar from "./components/TopBar";

interface ChatPageProps {
    isNewMessage?: boolean;
    contactName?: string;
    contactAvatar?: string;
}

interface SearchParams {
    contactId?: string;
    contactName?: string;
    contactAvatar?: string;
    isOnline?: string;
    messages?: string; // JSON string
}

const ChatPage = ({ isNewMessage = false, contactName: propContactName = "Jack Rope", contactAvatar: propContactAvatar }: ChatPageProps) => {
    const params = useLocalSearchParams();
    
    // Parse navigation params
    const contactId = params?.contactId as string;
    const contactName = (params?.contactName as string) || propContactName;
    const contactAvatar = (params?.contactAvatar as string) || propContactAvatar;
    const isOnline = params?.isOnline === 'true';
    
    // Parse messages from JSON string
    let parsedMessages: Message[] = [];
    try {
        const messagesParam = Array.isArray(params?.messages) ? params.messages[0] : params?.messages;
        if (messagesParam) {
            parsedMessages = JSON.parse(messagesParam);
        }
    } catch (error) {
        console.error('Failed to parse messages:', error);
    }

    const initialMessages = parsedMessages.length > 0 ? parsedMessages.map(msg => ({
        ...msg,
        status: msg.isOwn ? 'seen' as MessageStatus : undefined
    })) : [
        {
            id: '1',
            text: '',
            timestamp: '09:25 AM',
            isOwn: true,
            type: 'wave',
            status: 'seen' as MessageStatus
        } as Message,
        {
            id: '2',
            text: 'I need you to help with my work! Are u free tonight?',
            timestamp: '09:30 AM',
            isOwn: false,
            type: 'text'
        } as Message
    ];

    const [messages, setMessages] = useState<Message[]>(initialMessages);
    const [inputText, setInputText] = useState('');
    const [visibleTimestampId, setVisibleTimestampId] = useState<string | null>(null);

    // Update messages when params change
    useEffect(() => {
        if (parsedMessages.length > 0) {
            setMessages(parsedMessages.map(msg => ({
                ...msg,
                status: msg.isOwn ? 'seen' as MessageStatus : undefined
            })));
        }
    }, [params?.messages]);

    // Simulate message status progression
    const updateMessageStatus = useCallback((messageId: string, newStatus: MessageStatus) => {
        setMessages(prev => prev.map(msg => {
            if (msg.id === messageId && msg.isOwn) {
                const now = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                const updates: Partial<Message> = { status: newStatus };
                
                if (newStatus === 'delivered') {
                    updates.deliveredAt = now;
                } else if (newStatus === 'seen') {
                    updates.readAt = now;
                    updates.deliveredAt = updates.deliveredAt || now;
                }
                
                return { ...msg, ...updates };
            }
            return msg;
        }));
    }, []);

    // Simulate realistic message status progression
    const simulateMessageDelivery = useCallback((messageId: string) => {
        // Simulate sending delay
        setTimeout(() => {
            updateMessageStatus(messageId, 'sent');
        }, 100);

        // Simulate delivery delay (only if contact is online)
        if (isOnline) {
            setTimeout(() => {
                updateMessageStatus(messageId, 'delivered');
            }, 1000);

            // Simulate read receipt (random chance if online)
            if (Math.random() > 0.3) { // 70% chance of being read
                setTimeout(() => {
                    updateMessageStatus(messageId, 'seen');
                }, 2000 + Math.random() * 3000); // 2-5 seconds after delivery
            }
        } else {
            // If contact is offline, message stays as "sent" until they come online
            setTimeout(() => {
                updateMessageStatus(messageId, 'sent');
            }, 500);
        }
    }, [isOnline, updateMessageStatus]);

    const toggleTimestamp = useCallback((messageId: string) => {
        setVisibleTimestampId(prev => prev === messageId ? null : messageId);
    }, []);

    const sendMessage = useCallback((messageText?: string, mediaItems?: MediaItem[]) => {
        const hasText = messageText && messageText.trim();
        const hasMedia = mediaItems && mediaItems.length > 0;
        
        if (hasText || hasMedia) {
            const newMessage: Message = {
                id: Date.now().toString(),
                text: hasText ? messageText.trim() : '',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                isOwn: true,
                type: hasMedia ? 'media' : 'text',
                media: mediaItems,
                status: 'sending'
            };
            
            setMessages(prev => [...prev, newMessage]);
            setInputText('');
            
            // Start message status progression
            simulateMessageDelivery(newMessage.id);
        }
    }, [simulateMessageDelivery]);

    return (
        <KeyboardProvider>
            <KeyboardAvoidingView
                style={MainContainers.page}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
            >
                <TopBar 
                    title={contactName} 
                    subtitle={isOnline ? 'Online' : 'Last seen recently'}
                    avatar={contactAvatar}
                />

                {isNewMessage ? (
                    <NewMessagePrompt contactAvatar={contactAvatar} />
                ) : (
                    <MessageList
                        messages={messages}
                        contactAvatar={contactAvatar}
                        visibleTimestampId={visibleTimestampId}
                        onToggleTimestamp={toggleTimestamp}
                    />
                )}

                <MessageInput
                    inputText={inputText}
                    onInputTextChange={setInputText}
                    onSendMessage={sendMessage}
                />
            </KeyboardAvoidingView>
        </KeyboardProvider>
    );
};

export default ChatPage;
