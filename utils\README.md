# Utils Folder

This folder contains utility functions and helper modules that provide common functionality across the application.

## Purpose
- Pure utility functions that don't depend on React or app state
- Data transformation and formatting helpers
- Validation functions
- Common algorithms and calculations
- String, date, and number manipulation utilities
- Constants and enums

## Examples
- `dateUtils.ts` - Date formatting and manipulation functions
- `stringUtils.ts` - String processing and validation
- `validations.ts` - Form validation functions
- `formatters.ts` - Data formatting utilities (currency, phone, etc.)
- `constants.ts` - App-wide constants and configuration values
- `helpers.ts` - General purpose helper functions

## Guidelines
- Functions should be pure (no side effects)
- Well documented with JSDoc comments
- Thoroughly tested with unit tests
- Exported as named exports for tree-shaking optimization
