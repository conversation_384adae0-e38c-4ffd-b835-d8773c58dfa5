import { useState, useEffect } from 'react';

interface DateValidationResult {
  isValid: boolean;
  isAdult: boolean;
  isFutureDate: boolean;
  errorMessage?: string;
}

export const useDateValidation = (dateString?: string): DateValidationResult => {
  const [isValid, setIsValid] = useState(false);
  const [isAdult, setIsAdult] = useState(false);
  const [isFutureDate, setIsFutureDate] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (!dateString) {
      setIsValid(false);
      setIsAdult(false);
      setIsFutureDate(false);
      setErrorMessage(undefined);
      return;
    }

    try {
      const selectedDate = new Date(dateString);
      const today = new Date();

      // Check if the date is valid
      if (isNaN(selectedDate.getTime())) {
        setIsValid(false);
        setErrorMessage('Invalid date format');
        return;
      }

      setIsValid(true);

      // Check if the date is in the future
      const isFuture = selectedDate > today;
      setIsFutureDate(isFuture);

      if (isFuture) {
        setIsAdult(false);
        setErrorMessage('Birth date cannot be in the future');
        return;
      }

      // Calculate age
      let age = today.getFullYear() - selectedDate.getFullYear();
      const monthDiff = today.getMonth() - selectedDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate())) {
        age--;
      }

      // Check if user is at least 18 years old
      const isUserAdult = age >= 18;
      setIsAdult(isUserAdult);

      if (!isUserAdult) {
        setErrorMessage('You must be at least 18 years old');
      } else {
        setErrorMessage(undefined);
      }

    } catch (error) {
      setIsValid(false);
      setIsAdult(false);
      setIsFutureDate(false);
      setErrorMessage('Invalid date');
    }
  }, [dateString]);

  return {
    isValid,
    isAdult,
    isFutureDate,
    errorMessage
  };
};
