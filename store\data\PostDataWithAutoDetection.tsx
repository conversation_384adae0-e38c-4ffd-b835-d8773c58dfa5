// Example showing automatic media type detection using regex
const PostDataWithAutoDetection = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1504150558240-0b4fd8946624?q=80&w=464&auto=format&fit=crop',
    privacyStatus: '1',
    timestamp: '2025-07-12T07:30:00Z',
    content: "Mixed media post with automatic type detection! 📸🎬 The system automatically detects videos and images based on file extensions.",
    // Legacy format - just URLs as strings (will be auto-detected)
    media: [
      'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', // Auto-detected as video
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=870&auto=format&fit=crop', // Auto-detected as image
      'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4' // Auto-detected as video
    ],
    interaction: {
      likes: 124,
      hearts: 32,
      sad: 1,
      angry: 0,
      comments: 18,
      shares: 7,
    },
    isInteracted: {
      liked: true,
      hearted: false,
      sad: false,
      angry: false,
    },
    comments: []
  },
  {
    id: 2,
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b1e05f1e?q=80&w=464&auto=format&fit=crop',
    privacyStatus: '1',
    timestamp: '2025-07-12T09:15:00Z',
    content: "Different video formats automatically detected! 🎥 Supporting MP4, MOV, AVI, and more.",
    // Mixed format - some with explicit types, some auto-detected
    media: [
      {
        url: 'https://sample-videos.com/zip/10/mp4/mp4/SampleVideo_1280x720_1mb.mp4',
        // No type specified - will be auto-detected as video
      },
      {
        url: 'https://images.unsplash.com/photo-1551782450-17144efb9c50?q=80&w=869&auto=format&fit=crop',
        type: 'image' as const // Explicitly specified
      },
      'https://filesamples.com/samples/video/mov/mp4/SampleVideo_176x144_1mb.mov' // Auto-detected as video (MOV format)
    ],
    interaction: {
      likes: 89,
      hearts: 21,
      sad: 0,
      angry: 0,
      comments: 12,
      shares: 4,
    },
    isInteracted: {
      liked: false,
      hearted: true,
      sad: false,
      angry: false,
    },
    comments: []
  },
  {
    id: 3,
    name: 'Mike Wilson',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=464&auto=format&fit=crop',
    privacyStatus: '1',
    timestamp: '2025-07-12T11:45:00Z',
    content: "Image gallery with various formats! 📷 JPEG, PNG, WebP - all automatically detected.",
    media: [
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?format=webp&q=80&w=870', // WebP format
      'https://via.placeholder.com/600x400.png', // PNG format
      'https://via.placeholder.com/600x400.jpg', // JPEG format
      'https://images.unsplash.com/photo-1517649763962-0c623066013b?q=80&w=870&auto=format&fit=crop' // Auto-format
    ],
    interaction: {
      likes: 156,
      hearts: 45,
      sad: 0,
      angry: 1,
      comments: 23,
      shares: 11,
    },
    isInteracted: {
      liked: false,
      hearted: false,
      sad: false,
      angry: false,
    },
    comments: []
  },
  {
    id: 4,
    name: 'Emma Davis',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=464&auto=format&fit=crop',
    privacyStatus: '1',
    timestamp: '2025-07-12T14:20:00Z',
    content: "Streaming and web videos also supported! 🌐 YouTube, Vimeo, and direct streaming URLs.",
    media: [
      {
        url: 'https://player.vimeo.com/external/372246021.sd.mp4?s=a23d6e5e6b8b8e6e4f1c2d3e4f5a6b7c&profile_id=164',
        // Will be auto-detected as video due to .mp4 extension
      },
      {
        url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
        thumbnail: 'https://images.unsplash.com/photo-1536431311719-398b6704d4cc?q=80&w=400'
      }
    ],
    interaction: {
      likes: 203,
      hearts: 67,
      sad: 2,
      angry: 0,
      comments: 34,
      shares: 19,
    },
    isInteracted: {
      liked: true,
      hearted: true,
      sad: false,
      angry: false,
    },
    comments: []
  }
];

export default PostDataWithAutoDetection;

/*
Supported file extensions for auto-detection:

VIDEO:
- .mp4, .mov, .avi, .mkv, .webm
- .flv, .wmv, .m4v, .3gp, .ogv
- .m3u8 (HLS streaming)
- .mpd (DASH streaming)

IMAGE:
- .jpg, .jpeg, .png, .gif, .bmp
- .webp, .svg, .tiff, .ico

STREAMING PATTERNS:
- YouTube URLs
- Vimeo URLs
- Twitch URLs
- Dailymotion URLs
- Facebook video URLs
- Instagram media URLs
- TikTok URLs

The regex patterns are case-insensitive and handle query parameters.
*/
