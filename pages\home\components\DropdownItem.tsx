import { Colors } from '@/utils/colors';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

interface DropDownItemProps {
  data?: { label: string; value: string }[];
  backgroundColor?: string;
  gradientColors?: [string, string, ...string[]];
  placeholder?: string;
  iconName?: keyof typeof Ionicons.glyphMap;
  onSelectionChange?: (value: string) => void;
}

const DropDownItem: React.FC<DropDownItemProps> = ({
  data = [
    { label: 'Public', value: '1' },
    { label: 'Friends', value: '2' },
    { label: 'Private', value: '3' },
  ],
  backgroundColor = Colors.primary[500],
  gradientColors,
  placeholder = 'Select Privacy',
  iconName = 'earth',
  onSelectionChange,
}) => {
  const [value, setValue] = useState<string | null>(null);
  const [isFocus, setIsFocus] = useState(false);

  const displayLabel =
    !isFocus && value === null
      ? placeholder
      : data.find(item => item.value === value)?.label || placeholder;

  const dropdownComponent = (
    <Dropdown
      style={styles.dropdownInner}
      placeholderStyle={styles.placeholderStyle}
      selectedTextStyle={styles.selectedTextStyle}
      data={data}
      maxHeight={300}
      labelField="label"
      valueField="value"
      placeholder={displayLabel}
      value={value}
      onFocus={() => setIsFocus(true)}
      onBlur={() => setIsFocus(false)}
      onChange={item => {
        setValue(item.value);
        setIsFocus(false);
        onSelectionChange?.(item.value);
      }}
      renderLeftIcon={() => (
        <View style={{ marginRight: 8 }}>
          <Ionicons name={iconName} size={16} color="white" />
        </View>
      )}
      renderRightIcon={() => (
        <Ionicons
          name={value === null ? 'chevron-down' : 'chevron-up'}
          size={16}
          color="white"
        />
      )}
    />
  );

  return (
    <View>
      {gradientColors ? (
        <LinearGradient
          colors={gradientColors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.dropdown}
        >
          {dropdownComponent}
        </LinearGradient>
      ) : (
        <View style={[styles.dropdown, { backgroundColor }]}> 
          {dropdownComponent}
        </View>
      )}
    </View>
  );
};

export default DropDownItem;

const styles = StyleSheet.create({
  dropdown: {
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 8,
    width: 120,
    gap: 8,
  },
  dropdownInner: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  icon: {
    marginRight: 5,
  },
  label: {
    position: 'absolute',
    backgroundColor: 'white',
    left: 22,
    top: 8,
    zIndex: 999,
    paddingHorizontal: 8,
    fontSize: 14,
  },
  placeholderStyle: {
    fontSize: 14,
    color: 'white',
  },
  selectedTextStyle: {
    fontSize: 14,
    color: 'white',
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
