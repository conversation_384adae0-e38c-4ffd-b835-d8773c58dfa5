import React, { useState } from 'react';
import ImageAdvanced from '@/components/ui/ImageAdvanced';
import ProfileData from '@/store/data/ProfileData';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { Ionicons } from '@expo/vector-icons';
import { 
  Image, 
  Pressable, 
  StyleSheet, 
  Text, 
  View, 
  Animated,
  Platform,
  Dimensions
} from 'react-native';

interface OnlineUserItemProps {
  item?: {
    id: string;
    name: string;
    avatar: string;
    isOnline: boolean;
    hasStory: boolean;
    lastSeen?: string;
    unreadCount?: number;
  };
  isCreateStory?: boolean;
  onPress: () => void;
  onLongPress?: () => void;
  size?: 'small' | 'medium' | 'large';
  showUnreadBadge?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

const SIZES = {
  small: { avatar: 50, container: 60 },
  medium: { avatar: 60, container: 70 },
  large: { avatar: 70, container: 80 },
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginRight: 8,
  },
  pressable: {
    alignItems: 'center',
    borderRadius: 16,
    paddingTop: 16,
    backgroundColor: 'transparent',
  },
  pressablePressed: {
    backgroundColor: Colors.background?.secondary || 'rgba(0,0,0,0.05)',
    transform: [{ scale: 0.95 }],
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 6,
  },
  createStoryContainer: {
    borderRadius: 999,
    backgroundColor: Colors.background?.primary || '#ffffff',
    borderWidth: 2,
    borderColor: Colors.primary?.[200] || '#e5e7eb',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative'
  },
  userAvatarContainer: {
    borderRadius: 999,
    position: 'relative'
  },
  avatar: {
    borderRadius: 999,
  },
  avatarWithStory: {
    borderWidth: 3,
    borderColor: 'white',
    borderRadius: 999,
  },
  storyBorder: {
    position: 'absolute',
    top: -3,
    left: -3,
    right: -3,
    bottom: -3,
    borderRadius: 999,
    borderWidth: 3,
    borderColor: Colors.primary?.[500] || '#3b82f6',
  },
  storyGradientBorder: {
    position: 'absolute',
    top: -3,
    left: -3,
    right: -3,
    bottom: -3,
    borderRadius: 999,
    borderWidth: 2,
    // Gradient colors - would need react-native-linear-gradient for true gradient
    borderColor: Colors.primary?.[500] || '#3b82f6',
  },
  createButton: {
    position: 'absolute',
    bottom: -3,
    right: -3,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary?.[500] || '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 3,
    right: 3,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: Colors.success || '#10b981',
    borderWidth: 3,
    borderColor: 'white'
  },
  offlineIndicator: {
    position: 'absolute',
    bottom: 3,
    right: 3,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: Colors.text?.muted || '#9ca3af',
    borderWidth: 3,
    borderColor: 'white',
  },
  nameText: {
    marginTop: 2,
    color: Colors.text?.primary || '#111827',
    textAlign: 'center',
    fontSize: 12,
    fontFamily: Fonts.roboto?.medium || 'System',
    fontWeight: '500',
  },
  createText: {
    marginTop: 2,
    color: Colors.text?.secondary || '#6b7280',
    textAlign: 'center',
    fontSize: 12,
    fontFamily: Fonts.roboto?.regular || 'System',
    fontWeight: '400',
    lineHeight: 14,
  },
  lastSeenText: {
    marginTop: 1,
    color: Colors.text?.muted || '#9ca3af',
    textAlign: 'center',
    fontSize: 10,
    fontFamily: Fonts.roboto?.regular || 'System',
    fontWeight: '400',
  },
  chatBubble: {
    position: 'absolute',
    top: -12,
    right: -12,
    backgroundColor: Colors.secondary[50] || '#3b82f6',
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 8,
    height: 30,
    width: 80, // Adjusted for dynamic width
  },
  chatBubbleText: {
    color: Colors.text.muted,
    fontSize: 8,
    fontFamily: Fonts.roboto?.medium || 'System',
    fontWeight: '500',
    textAlign: 'center',
  },
  chatBubbleTail: {
    position: 'absolute',
    bottom: -5,
    left: 16,
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: Colors.secondary[50] || '#3b82f6',
  },
  unreadBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: Colors.error || '#ef4444',
    borderRadius: 12,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white'
  },
  unreadBadgeText: {
    color: 'white',
    fontSize: 10,
    fontFamily: Fonts.roboto?.bold || 'System',
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 12,
  },
  pulseAnimation: {
    position: 'absolute',
    borderRadius: 999,
    backgroundColor: Colors.success || '#10b981',
    opacity: 0.6,
  },
});

const OnlineUserItem: React.FC<OnlineUserItemProps> = ({ 
  item, 
  isCreateStory = false, 
  onPress,
  onLongPress,
  size = 'medium',
  showUnreadBadge = true
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [pulseAnim] = useState(new Animated.Value(1));

  const avatarSize = SIZES[size].avatar;
  const containerWidth = SIZES[size].container;

  // Pulse animation for online indicator
  React.useEffect(() => {
    if (item?.isOnline) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.3,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [item?.isOnline, pulseAnim]);

  const dynamicStyles = {
    container: {
      ...styles.container,
      width: containerWidth,
    },
    createStoryContainer: {
      ...styles.createStoryContainer,
      width: avatarSize,
      height: avatarSize,
    },
    userAvatarContainer: {
      ...styles.userAvatarContainer,
      width: avatarSize,
      height: avatarSize,
    },
    avatar: {
      ...styles.avatar,
      width: avatarSize,
      height: avatarSize,
    },
    avatarWithStory: {
      ...styles.avatarWithStory,
      width: avatarSize,
      height: avatarSize,
      borderWidth: 2,
    },
    nameText: {
      ...styles.nameText,
      maxWidth: containerWidth + 10,
    },
    createText: {
      ...styles.createText,
      maxWidth: containerWidth + 10,
    },
  };

  const renderCreateStory = () => (
    <View style={dynamicStyles.createStoryContainer}>
      <ImageAdvanced
        source={ProfileData.profile.avatar}
        style={dynamicStyles.avatar}
        contentFit="cover"
      />
      <View style={styles.createButton}>
        <Ionicons name="add" size={14} color="white" />
      </View>
      <View style={styles.chatBubble}>
        <Text style={styles.chatBubbleText}>Share your day</Text>
        <View style={styles.chatBubbleTail} />
      </View>
    </View>
  );

  const renderUserAvatar = () => {
    if (!item) return null;

    return (
      <View style={dynamicStyles.userAvatarContainer}>
        {item.hasStory && <View style={styles.storyBorder} />}
        <Image
          source={{ uri: item.avatar }}
          style={item.hasStory ? dynamicStyles.avatarWithStory : dynamicStyles.avatar}
          resizeMode="cover"
        />
        
        {/* Online/Offline Indicator with Pulse Animation */}
        {item.isOnline ? (
          <>
            <Animated.View
              style={[
                styles.pulseAnimation,
                styles.onlineIndicator,
                {
                  transform: [{ scale: pulseAnim }],
                },
              ]}
            />
            <View style={styles.onlineIndicator} />
          </>
        ) : (
          <View style={styles.offlineIndicator} />
        )}

        {/* Unread Messages Badge */}
        {showUnreadBadge && item.unreadCount && item.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadBadgeText}>
              {item.unreadCount > 99 ? '99+' : item.unreadCount}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const handlePressIn = () => setIsPressed(true);
  const handlePressOut = () => setIsPressed(false);

  return (
    <View style={dynamicStyles.container}>
      <Pressable 
        style={[
          styles.pressable,
          isPressed && styles.pressablePressed
        ]}
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        accessibilityRole="button"
        accessibilityLabel={
          isCreateStory 
            ? "Create your story" 
            : `${item?.name}${item?.isOnline ? ', online' : ', offline'}${item?.unreadCount ? `, ${item.unreadCount} unread messages` : ''}`
        }
        accessibilityHint={
          isCreateStory 
            ? "Tap to create a new story" 
            : "Tap to open chat"
        }
      >
        <View style={styles.avatarContainer}>
          {isCreateStory ? renderCreateStory() : renderUserAvatar()}
        </View>
        
        <View style={{ alignItems: 'center' }}>
          <Text
            style={isCreateStory ? dynamicStyles.createText : dynamicStyles.nameText}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {isCreateStory ? 'Create story' : item?.name}
          </Text>
          
          {/* Last Seen Text */}
          {!isCreateStory && item?.lastSeen && !item.isOnline && (
            <Text style={styles.lastSeenText} numberOfLines={1}>
              {item.lastSeen}
            </Text>
          )}
        </View>
      </Pressable>
    </View>
  );
};

export default OnlineUserItem;