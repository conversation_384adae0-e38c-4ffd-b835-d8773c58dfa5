import Svg, { SvgProps, Path, Defs, LinearGradient, Stop } from 'react-native-svg';

interface CustomSvgProps extends SvgProps {
  size?: number;
}

const MemoryIcon = ({ size = 52, ...props }: CustomSvgProps) => (
  <Svg
    width={size}
    height={(size * 53) / 52}
    viewBox="0 0 52 53"
    fill="none"
    {...props}
  >
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M36.745 32.795a1.992 1.992 0 0 1-2.731.694l-9.01-5.376a1.995 1.995 0 0 1-.973-1.71v-11.59a1.993 1.993 0 0 1 3.986 0V25.27l8.037 4.794a1.993 1.993 0 0 1 .691 2.731ZM26.027.51C6.897.51.12 7.287.12 26.416c0 19.125 6.778 25.906 25.906 25.906 19.129 0 25.907-6.78 25.907-25.907C51.933 7.288 45.155.51 26.026.51Z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={26.026}
        x2={26.026}
        y1={0.509}
        y2={52.322}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#65F40D" />
        <Stop offset={1} stopColor="#15B138" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default MemoryIcon;
