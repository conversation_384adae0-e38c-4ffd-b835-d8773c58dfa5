/**
 * Utility functions for testing media type detection
 * These can be used for debugging or testing the regex patterns
 */

// Same detection function as in Post component
export const detectMediaType = (url: string): 'image' | 'video' => {
  // Video file extensions (case insensitive)
  const videoExtensions = /\.(mp4|mov|avi|mkv|webm|flv|wmv|m4v|3gp|ogv)(\?.*)?$/i;
  
  // Image file extensions (case insensitive)
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp|svg|tiff|ico)(\?.*)?$/i;
  
  // Check for video extensions first
  if (videoExtensions.test(url)) {
    return 'video';
  }
  
  // Check for image extensions
  if (imageExtensions.test(url)) {
    return 'image';
  }
  
  // Check for common video streaming patterns
  const videoStreamingPatterns = [
    /youtube\.com\/watch/i,
    /vimeo\.com/i,
    /twitch\.tv/i,
    /dailymotion\.com/i,
    /facebook\.com.*\/videos/i,
    /instagram\.com.*\/p\/.*\/media/i,
    /tiktok\.com/i,
    /\.m3u8/i, // HLS streaming
    /\.mpd/i   // DASH streaming
  ];
  
  if (videoStreamingPatterns.some(pattern => pattern.test(url))) {
    return 'video';
  }
  
  // Default to image if can't determine
  return 'image';
};

// Test cases for validation
export const testCases = [
  // Video files
  { url: 'https://example.com/video.mp4', expected: 'video' },
  { url: 'https://example.com/video.MP4', expected: 'video' },
  { url: 'https://example.com/video.mp4?v=123', expected: 'video' },
  { url: 'https://example.com/movie.mov', expected: 'video' },
  { url: 'https://example.com/clip.avi', expected: 'video' },
  { url: 'https://example.com/video.webm', expected: 'video' },
  { url: 'https://example.com/stream.m3u8', expected: 'video' },
  { url: 'https://example.com/manifest.mpd', expected: 'video' },
  
  // Image files
  { url: 'https://example.com/image.jpg', expected: 'image' },
  { url: 'https://example.com/image.JPEG', expected: 'image' },
  { url: 'https://example.com/image.png?w=500', expected: 'image' },
  { url: 'https://example.com/photo.gif', expected: 'image' },
  { url: 'https://example.com/picture.webp', expected: 'image' },
  { url: 'https://example.com/icon.svg', expected: 'image' },
  
  // Streaming URLs
  { url: 'https://youtube.com/watch?v=dQw4w9WgXcQ', expected: 'video' },
  { url: 'https://vimeo.com/123456789', expected: 'video' },
  { url: 'https://twitch.tv/streamer', expected: 'video' },
  { url: 'https://facebook.com/user/videos/123456', expected: 'video' },
  
  // Ambiguous cases (should default to image)
  { url: 'https://example.com/unknown', expected: 'image' },
  { url: 'https://example.com/file.pdf', expected: 'image' },
];

// Run tests and return results
export const runTests = (): { passed: number; failed: number; results: Array<{url: string; expected: string; actual: string; passed: boolean}> } => {
  const results = testCases.map(testCase => {
    const actual = detectMediaType(testCase.url);
    const passed = actual === testCase.expected;
    return {
      url: testCase.url,
      expected: testCase.expected,
      actual,
      passed
    };
  });
  
  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;
  
  return { passed, failed, results };
};

// Console logger for test results
export const logTestResults = () => {
  const { passed, failed, results } = runTests();
  
  console.log('🧪 Media Type Detection Tests');
  console.log('============================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${results.length}`);
  console.log('');
  
  if (failed > 0) {
    console.log('Failed Tests:');
    results.filter(r => !r.passed).forEach(result => {
      console.log(`❌ ${result.url}`);
      console.log(`   Expected: ${result.expected}, Got: ${result.actual}`);
    });
  }
  
  return { passed, failed, results };
};

// Usage example:
// import { logTestResults } from '@/utils/mediaTypeDetection';
// logTestResults(); // Run this in your app to test the detection
