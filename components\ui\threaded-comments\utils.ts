// Utility functions for threaded comments
import { Comment, FlattenedComment } from './types';

// Flatten nested comments for FlatList
export const flattenComments = (comments: Comment[], depth = 0): FlattenedComment[] => {
  let result: FlattenedComment[] = [];
  comments.forEach((comment: Comment) => {
    result.push({ comment, depth, id: `${comment.id}-${depth}` });
    if (comment.replies && comment.replies.length > 0) {
      result = result.concat(flattenComments(comment.replies, depth + 1));
    }
  });
  return result;
};

// Update likes for a specific comment
export const updateCommentLikes = (comments: Comment[], commentId: number): Comment[] => {
  return comments.map((comment: Comment) => {
    if (comment.id === commentId) {
      return { ...comment, likes: comment.likes + 1 };
    }
    if (comment.replies) {
      return { ...comment, replies: updateCommentLikes(comment.replies, commentId) };
    }
    return comment;
  });
};

// Add reply to a specific comment
export const addReplyToComment = (comments: Comment[], parentId: number, newComment: Comment): Comment[] => {
  return comments.map((c: Comment) => {
    if (c.id === parentId) {
      return { ...c, replies: [...c.replies, newComment] };
    }
    if (c.replies) {
      return { ...c, replies: addReplyToComment(c.replies, parentId, newComment) };
    }
    return c;
  });
};

// Parse mention from comment text
export interface MentionParseResult {
  hasMention: boolean;
  mentionText?: string;
  messageText?: string;
  fullText: string;
}

export const parseMention = (text: string): MentionParseResult => {
  // Early return if text is undefined, null, or doesn't start with @
  if (!text || !text.startsWith('@')) {
    return {
      hasMention: false,
      fullText: text || ''
    };
  }

  // For mentions with full names, we need to find where the actual message starts
  // Look for patterns like "@John Doe Hello there" where we want "John Doe" as mention
  
  // Find the mention end by looking for common patterns:
  // 1. Double space (less reliable)
  // 2. Punctuation followed by space
  // 3. Pattern recognition for name + message
  
  // Simple approach: assume mention ends at first occurrence of double space or 
  // after reasonable name length, then find next logical break
  let mentionEnd = -1;
  
  // Look for double spaces first (if user typed @Name  Message)
  const doubleSpaceIndex = text.indexOf('  ', 1);
  if (doubleSpaceIndex !== -1) {
    mentionEnd = doubleSpaceIndex;
  } else {
    // Look for single space after what looks like a complete name
    // Split by spaces and try to identify where name ends and message begins
    const words = text.substring(1).split(' '); // Remove @ and split
    
    if (words.length < 2) {
      // Only one word after @, treat as simple username
      return {
        hasMention: true,
        mentionText: text,
        messageText: '',
        fullText: text
      };
    }
    
    // For now, assume first two words are the name (e.g., "John Doe")
    // This can be made more sophisticated based on your needs
    const nameWords = words.slice(0, 2);
    const nameLength = nameWords.join(' ').length;
    mentionEnd = nameLength + 1; // +1 for the @ symbol
  }
  
  if (mentionEnd === -1) {
    return {
      hasMention: false,
      fullText: text
    };
  }

  const mentionPart = text.substring(0, mentionEnd);
  const messagePart = text.substring(mentionEnd);
  
  return {
    hasMention: true,
    mentionText: mentionPart,
    messageText: messagePart,
    fullText: text
  };
};
