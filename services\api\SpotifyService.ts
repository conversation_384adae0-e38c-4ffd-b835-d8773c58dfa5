import { SpotifySearchResponse, SpotifyTokenResponse } from '@/types/spotify';

const CLIENT_ID = '958543a9acb040ea8d5ae679eb235489'; // Replace with your Spotify Client ID
const CLIENT_SECRET = '37e3e1583d2a4f3295322f0863b99c1d'; // Replace with your Spotify Client Secret

class SpotifyService {
  private accessToken: string | null = null;
  private tokenExpirationTime: number = 0;

  private async getAccessToken(): Promise<string> {
    const currentTime = Date.now();
    
    // Check if token is still valid
    if (this.accessToken && currentTime < this.tokenExpirationTime) {
      return this.accessToken;
    }

    try {
      const response = await fetch('https://accounts.spotify.com/api/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${btoa(`${CLIENT_ID}:${CLIENT_SECRET}`)}`,
        },
        body: 'grant_type=client_credentials',
      });

      if (!response.ok) {
        throw new Error('Failed to get access token');
      }

      const data: SpotifyTokenResponse = await response.json();
      this.accessToken = data.access_token;
      this.tokenExpirationTime = currentTime + (data.expires_in * 1000) - 60000; // Subtract 1 minute for safety

      return this.accessToken;
    } catch (error) {
      console.error('Error getting Spotify access token:', error);
      throw error;
    }
  }

  async searchTracks(query: string, limit: number = 20): Promise<SpotifySearchResponse> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await fetch(
        `https://api.spotify.com/v1/search?q=${encodeURIComponent(query)}&type=track&limit=${limit}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to search tracks');
      }

      const data: SpotifySearchResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error searching tracks:', error);
      throw error;
    }
  }

  async getTrackById(trackId: string): Promise<any> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await fetch(
        `https://api.spotify.com/v1/tracks/${trackId}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to get track');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting track:', error);
      throw error;
    }
  }
}

export default new SpotifyService();
