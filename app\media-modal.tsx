import MediaOverlay from '@/components/ui/MediaOverlay';
import { MediaData } from '@/types/media';
import { useLocalSearchParams, useRouter } from 'expo-router';

const MediaModal = () => {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Parse the media array from params
  const mediaList: MediaData[] = params.media ? JSON.parse(params.media as string) : [];
  const initialIndex = params.focusIndex ? parseInt(params.focusIndex as string, 10) : 0;

  const handleClose = () => {
    router.back();
  };

  return (
    <MediaOverlay
      media={mediaList}
      initialIndex={initialIndex}
      onClose={handleClose}
    />
  );
};

export default MediaModal;
