import ViewContainer from '@/components/ui/ViewContainer';
import HeaderBackground from '@/pages/home/<USER>/HeaderBackground';
import PostData from '@/store/data/PostData';
import { safeNavigate } from '@/utils/navigationUtils';
import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import { useCallback, useRef, useState } from 'react';
import {
  Animated,
  Easing,
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  Text,
  View,
} from 'react-native';
import Post from './components/Post';
import PostCreation from './components/PostCreation';
import Story from './components/Story';
import TopBar from './components/TopBar';

const NewsFeedPage = () => {
  const scrollY = useRef(new Animated.Value(0)).current;
  const lastScrollY = useRef(0);
  const topBarAnimatedValue = useRef(new Animated.Value(0)).current;
  const [isAtTop, setIsAtTop] = useState(true);
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);
  const scrollDirectionRef = useRef<'up' | 'down' | null>(null);
  const isAnimatingRef = useRef(false);
  const router = useRouter();

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const currentScrollY = event.nativeEvent.contentOffset.y;
      const isAtTopPosition = currentScrollY <= 10;
      const scrollDelta = currentScrollY - lastScrollY.current;
      const threshold = 8; // Increased threshold for less sensitivity

      setIsAtTop(isAtTopPosition);

      // Always keep top bar visible when at top
      if (isAtTopPosition) {
        if (animationRef.current) {
          animationRef.current.stop();
        }

        if (!isAnimatingRef.current) {
          isAnimatingRef.current = true;
          animationRef.current = Animated.timing(topBarAnimatedValue, {
            toValue: 0,
            duration: 200,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          });
          animationRef.current.start(() => {
            isAnimatingRef.current = false;
          });
        }
        scrollDirectionRef.current = null;
      } else {
        // Only trigger animation if scroll delta exceeds threshold
        if (Math.abs(scrollDelta) > threshold) {
          const newDirection = scrollDelta < 0 ? 'up' : 'down';

          // Only animate if direction changed or no animation is running
          if (scrollDirectionRef.current !== newDirection && !isAnimatingRef.current) {
            scrollDirectionRef.current = newDirection;

            if (animationRef.current) {
              animationRef.current.stop();
            }

            isAnimatingRef.current = true;

            if (newDirection === 'up') {
              // Scrolling up - show top bar
              animationRef.current = Animated.timing(topBarAnimatedValue, {
                toValue: 0,
                duration: 250,
                easing: Easing.out(Easing.cubic),
                useNativeDriver: true,
              });
            } else {
              // Scrolling down - hide top bar
              animationRef.current = Animated.timing(topBarAnimatedValue, {
                toValue: -120, // Slightly less than before for smoother transition
                duration: 200,
                easing: Easing.in(Easing.cubic),
                useNativeDriver: true,
              });
            }

            animationRef.current.start(() => {
              isAnimatingRef.current = false;
            });
          }
        }
      }

      lastScrollY.current = currentScrollY;
      scrollY.setValue(currentScrollY);
    },
    [topBarAnimatedValue]
  );

  const handleOpenComments = useCallback((post: any) => {
    router.push({
      pathname: '/comments-modal',
      params: {
        postId: post.id.toString(),
        initialComments: JSON.stringify(post.comments),
      },
    });
  }, [router]);

  const renderPost = useCallback(
    ({ item }: { item: any }) => (
      <View className="px-4">
        <Post post={item} onOpenComments={handleOpenComments} />
      </View>
    ),
    [handleOpenComments]
  );

  const renderItemSeparator = useCallback(() => <View style={{ height: 16 }} />, []);

  const ListHeaderComponent = () => (
    <View className={'flex-1 bg-background-primary'}>
      <HeaderBackground />
      <View className="h-[95] pt-5">{/* Space reserved for TopBar */}</View>
      <ViewContainer classNameCustom="items-start justify-end h-[18%]">
        <Text className="font-roboto-medium text-[24px] text-white">Good morning, Lim!</Text>
        <Text className="font-roboto-regular text-[12px] text-white">
          You got 36 unread messages from your friends. Go and check it out!
        </Text>
      </ViewContainer>
      <ViewContainer classNameCustom="pt-4">
        <Pressable
        onPress={()=>{
          safeNavigate('/create-post');
        }}>
          <PostCreation></PostCreation>
        </Pressable>
      </ViewContainer>
      <View className="container mx-auto flex-1 pb-20 pt-4">
        <Story />
      </View>
    </View>
  );

  return (
    <View className={'flex-1 bg-background-primary'}>
      <FlatList
        data={PostData}
        renderItem={renderPost}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={ListHeaderComponent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: Platform.OS === 'ios' ? 110 : 90 }}
        ItemSeparatorComponent={renderItemSeparator}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        bounces={true}
        bouncesZoom={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        windowSize={10}
        initialNumToRender={3}
        updateCellsBatchingPeriod={50}
        nestedScrollEnabled={true}
      />

      {/* Floating TopBar */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 100,
          transform: [
            {
              translateY: topBarAnimatedValue,
            },
          ],
        }}>
        {isAtTop ? (
          <View className="bg-transparent">
            <ViewContainer classNameCustom="pt-5">
              <TopBar />
            </ViewContainer>
          </View>
        ) : (
          <View>
            <BlurView
              experimentalBlurMethod="dimezisBlurView"
              intensity={50}
              tint="systemMaterialLight"
              style={{
                flex: 1,
                borderBottomLeftRadius: 10,
                borderBottomRightRadius: 10,
                overflow: 'hidden',
                height: 100,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }}>
              <ViewContainer classNameCustom="pt-5">
                <TopBar />
              </ViewContainer>
            </BlurView>
          </View>
        )}
      </Animated.View>
    </View>
  );
};

export default NewsFeedPage;
