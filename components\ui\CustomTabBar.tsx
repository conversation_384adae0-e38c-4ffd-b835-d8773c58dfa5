import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import ChatIcon from './ChatIcon';
import DashboardIcon from './DashboardIcon';
import HomeIcon from './HomeIcon';
import VideoIcon from './VideoIcon';

interface CustomTabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

const CustomTabBar: React.FC<CustomTabBarProps> = ({
  state,
  descriptors,
  navigation,
}: CustomTabBarProps) => {
  const getTabIcon = (routeName: string, focused: boolean) => {
    const iconSize = 26;
    const color = focused ? Colors.primary[500] : Colors.text.muted;

    switch (routeName) {
      case 'newsfeed':
        return <HomeIcon size={iconSize} color={color} />;
      case 'explore':
        return <VideoIcon size={iconSize} color={color} />;
      case 'messages':
        return <ChatIcon size={iconSize} color={color} />;
      case 'dashboard':
        return <DashboardIcon size={iconSize} color={color} />;
      default:
        return <Ionicons name="ellipse" size={iconSize} color={color} />;
    }
  };

  const getTabLabel = (routeName: string) => {
    switch (routeName) {
      case 'newsfeed':
        return 'Home';
      case 'explore':
        return 'Explore';
      case 'messages':
        return 'Messages';
      case 'notifications':
        return 'Notifications';
      default:
        return routeName;
    }
  };

  return (
    <View style={styles.tabBarContainer}>
      <BlurView
        intensity={100}
        tint="systemMaterialLight"
        style={styles.blurView}
        experimentalBlurMethod="dimezisBlurView">
        <View style={styles.tabBar}>
          {state.routes.map((route: any, index: number) => {
            const { options } = descriptors[route.key];
            const isFocused = state.index === index;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            const onLongPress = () => {
              navigation.emit({
                type: 'tabLongPress',
                target: route.key,
              });
            };

            return (
              <TouchableOpacity
                key={route.key}
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                onLongPress={onLongPress}
                style={styles.tabItem}
                activeOpacity={0.7}>
                <View style={[styles.tabContent]}>
                  {getTabIcon(route.name, isFocused)}
                  {/* <Text
                    style={[
                      styles.tabLabel,
                      { color: isFocused ? Colors.primary[950] : Colors.text.muted },
                    ]}>
                    {getTabLabel(route.name)}
                  </Text> */}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </BlurView>
    </View>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  blurView: {
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabBar: {
    flexDirection: 'row',
    height: Platform.OS === 'ios' ? 90 : 80,
    paddingBottom: Platform.OS === 'ios' ? 25 : 16,
    paddingTop: 12,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    minWidth: 65,
    minHeight: 50,
  },
  tabLabel: {
    fontSize: 11,
    fontFamily: Fonts.roboto.medium,
    marginTop: 2,
    textAlign: 'center',
    fontWeight: '600',
  },
});

export default CustomTabBar;
