import { AvatarGroup } from '@/components/avatar-group/AvatarGroup';
import { Colors } from '@/utils/colors';
import { StyleSheet, Text, View } from 'react-native';

interface FriendsProps {
  friends?: any[];
}

const Friends: React.FC<FriendsProps> = ({ friends = [] }) => {
  // Fallback data if no friends provided
  const defaultFriends = [
    {
      name: 'Heart',
      uri: 'https://images.unsplash.com/photo-1602233158242-3ba0ac4d2167?q=80&w=736&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '1',
    },
    {
      name: 'Like',
      uri:'https://plus.unsplash.com/premium_photo-1664464229780-5d4f367dd754?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '2',
    },
    {
      name: 'Laugh',
      uri:'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '3',
    },
    {
      name: 'Laugh5',
      uri:'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '4',
    },
    {
      name: 'Laugh4',
      uri:'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '5',
    }
  ];

  const displayFriends = friends.length > 0 ? friends : defaultFriends;
  const friendCount = friends.length || 3024; // Use actual count or fallback

  return (
    <View>
      <Text className="font-roboto-medium text-[18px]" style={styles.textTitle}>
        Friends <Text className="font-roboto-regular text-sm text-text-muted">({friendCount} mutuals)</Text>
      </Text>
      <View className="mt-4 flex-row items-center justify-between">
        <AvatarGroup
          avatars={displayFriends}
          size={60}
          max={5}
          overlap={20}></AvatarGroup>
        <Text className="font-roboto-bold text-sm" style={{color: Colors.primary[950]}}>See all</Text>
      </View>
    </View>
  );
};

export default Friends;

const styles = StyleSheet.create({
  textTitle: {
    color: Colors.secondary[950],
  },
});
