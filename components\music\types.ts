import { SelectedTrack, SpotifyTrack } from '@/types/spotify';

// Type definitions for the music selection feature
export interface MusicSelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectTrack: (track: SelectedTrack) => void;
  selectedTrack?: SelectedTrack | null;
}

export interface TrackItemProps {
  track: SpotifyTrack;
  onSelect: (track: SelectedTrack) => void;
  isPlaying: boolean;
  onPlayPause: (track: SpotifyTrack) => void;
  isSelected: boolean;
}

export interface MusicPlayerState {
  currentTrack: string | null;
  isPlaying: boolean;
  duration: number;
  position: number;
}

export interface MusicSearchState {
  query: string;
  results: SpotifyTrack[];
  isLoading: boolean;
  hasMore: boolean;
}

export * from '../../types/spotify';

