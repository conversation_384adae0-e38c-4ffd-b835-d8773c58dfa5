# Pages Folder

This folder contains full-screen components organized by major app sections or user flows.

## Structure
- `authentication/` - Login, signup, password reset screens
- `home/` - Main dashboard and home-related screens

## Purpose
- Groups screens by major app sections or user journeys
- Contains complete screen implementations
- Handles specific user flows and navigation logic
- May include screen-specific components and logic

## Difference from App Folder
While the `app/` folder uses Expo Router's file-based routing, this `pages/` folder can contain more complex screen compositions and multi-step flows that might not map directly to routes.
