import ImageAdvanced from '@/components/ui/ImageAdvanced';
import { StyleSheet, View, Text } from 'react-native';

const AvatarFriends = () => {
  return (
    <View style={styles.container}>
      <ImageAdvanced
        source={
          'https://images.unsplash.com/photo-1504150558240-0b4fd8946624?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
        }
        style={styles.avatar}
        contentFit="cover"></ImageAdvanced>
      <Text className="text-[14px] font-roboto-bold">Jimmy</Text>
    </View>
  );
};

export default AvatarFriends;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 99999,
  },
});
