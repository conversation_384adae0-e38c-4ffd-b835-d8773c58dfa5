# UI Services

This folder contains UI-related services and utilities that help manage user interface concerns.

## Purpose
- Theme and styling services
- UI state management utilities
- Toast/notification display services
- Modal and overlay management
- Navigation helpers and utilities
- UI animation and transition services

## Examples
- `theme.ts` - Theme configuration and switching
- `toast.ts` - Toast notification service
- `modal.ts` - Modal management service
- `navigation.ts` - Navigation utilities and helpers
- `animations.ts` - Common UI animations
- `haptics.ts` - Haptic feedback service
