import { MediaItem } from '@/types/media';
import * as ImagePicker from 'expo-image-picker';
import { useState } from 'react';
import { Alert } from 'react-native';

export const useMediaPicker = () => {
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera roll permissions to select media!',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const pickSingleMedia = async (mediaTypes: ('images' | 'videos')[] = ['images', 'videos']) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return null;

    setIsLoading(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const assetType = asset.type === 'video' ? 'video' : 'image';
        const mediaItem: MediaItem = {
          uri: asset.uri,
          type: assetType,
          width: asset.width,
          height: asset.height,
          duration: asset.duration || undefined,
          fileName: asset.fileName || undefined,
          fileSize: asset.fileSize,
        };
        return mediaItem;
      }
    } catch (error) {
      console.error('Error picking media:', error);
      Alert.alert('Error', 'Failed to pick media from gallery');
    } finally {
      setIsLoading(false);
    }
    return null;
  };

  const pickMultipleMedia = async (mediaTypes: ('images' | 'videos')[] = ['images', 'videos']) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return [];

    setIsLoading(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes,
        allowsMultipleSelection: true,
        selectionLimit: 10, // Limit to 10 media items
        quality: 0.8,
      });

      if (!result.canceled && result.assets) {
        const mediaItems: MediaItem[] = result.assets.map((asset) => {
          const assetType = asset.type === 'video' ? 'video' : 'image';
          return {
            uri: asset.uri,
            type: assetType,
            width: asset.width,
            height: asset.height,
            duration: asset.duration || undefined,
            fileName: asset.fileName || undefined,
            fileSize: asset.fileSize,
          };
        });
        return mediaItems;
      }
    } catch (error) {
      console.error('Error picking multiple media:', error);
      Alert.alert('Error', 'Failed to pick media from gallery');
    } finally {
      setIsLoading(false);
    }
    return [];
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera permissions to take photos!',
        [{ text: 'OK' }]
      );
      return null;
    }

    setIsLoading(true);
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const mediaItem: MediaItem = {
          uri: asset.uri,
          type: 'image',
          width: asset.width,
          height: asset.height,
          fileName: asset.fileName || undefined,
          fileSize: asset.fileSize,
        };
        return mediaItem;
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    } finally {
      setIsLoading(false);
    }
    return null;
  };

  const addMediaItem = (mediaItem: MediaItem) => {
    setSelectedMedia(prev => [...prev, mediaItem]);
  };

  const addMediaItems = (mediaItems: MediaItem[]) => {
    setSelectedMedia(prev => [...prev, ...mediaItems]);
  };

  const removeMediaItem = (index: number) => {
    setSelectedMedia(prev => prev.filter((_, i) => i !== index));
  };

  const clearAllMedia = () => {
    setSelectedMedia([]);
  };

  const showMediaOptions = () => {
    Alert.alert(
      'Select Media',
      'Choose how you want to add media',
      [
        {
          text: 'Camera',
          onPress: async () => {
            const photo = await takePhoto();
            if (photo) addMediaItem(photo);
          },
        },
        {
          text: 'Gallery',
          onPress: async () => {
            const media = await pickMultipleMedia();
            if (media.length > 0) addMediaItems(media);
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  return {
    selectedMedia,
    isLoading,
    pickSingleMedia,
    pickMultipleMedia,
    takePhoto,
    addMediaItem,
    addMediaItems,
    removeMediaItem,
    clearAllMedia,
    showMediaOptions,
  };
};
