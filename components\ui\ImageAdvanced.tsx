import { Image, ImageProps, ImageSource } from 'expo-image';
import React, { useEffect, useState } from 'react';
import { ImageStyle, StyleProp, View } from 'react-native';
import { ShimmerEffect } from '../shimmer/Shimmer';

interface ImageAdvancedProps extends Omit<ImageProps, 'source'> {
  source: string | ImageSource | number;
  fallbackSource?: ImageSource | number;
  showPlaceholder?: boolean;
  placeholderColor?: string;
  placeholderText?: string;
  style?: StyleProp<ImageStyle>;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
  loadingIndicatorSize?: 'small' | 'large';
  loadingIndicatorColor?: string;
  cache?: 'none' | 'disk' | 'memory' | 'memory-disk';
  priority?: 'low' | 'normal' | 'high';
  placeholder?: string | ImageSource | number;
  contentFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  transition?: number;
  children?: React.ReactNode;
  useShimmer?: boolean;
  shimmerColors?: string[];
  shimmerDuration?: number;
}

export const ImageAdvanced: React.FC<ImageAdvancedProps> = ({
  source,
  fallbackSource,
  showPlaceholder = true,
  placeholderColor = '#f0f0f0',
  placeholderText = 'Loading...',
  style,
  onLoadStart,
  onLoadEnd,
  onError,
  loadingIndicatorSize = 'small',
  loadingIndicatorColor = '#666',
  cache = 'memory-disk',
  priority = 'normal',
  placeholder,
  contentFit = 'cover',
  transition = 200,
  children,
  useShimmer = true,
  shimmerColors = ['rgba(240, 240, 240, 0.8)', 'rgba(255, 255, 255, 0.4)', 'rgba(240, 240, 240, 0.8)'],
  shimmerDuration = 1000,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [imageSource, setImageSource] = useState<ImageSource | number | null>(null);

  useEffect(() => {
    processImageSource();
  }, [source]);

  const processImageSource = () => {
    // Reset states for new image
    setHasError(false);
    setIsLoaded(false);
    setIsLoading(true);

    try {
      if (typeof source === 'string') {
        // Handle empty or invalid strings
        if (!source || source.trim() === '') {
          setHasError(true);
          setIsLoading(false);
          return;
        }

        // Handle remote URLs
        if (source.startsWith('http://') || source.startsWith('https://')) {
          setImageSource({ uri: source });
        }
        // Handle local file paths or base64
        else if (source.startsWith('file://') || source.startsWith('data:')) {
          setImageSource({ uri: source });
        }
        // Handle other string sources
        else {
          setImageSource({ uri: source });
        }
      }
      // Handle require() statements (numbers) or ImageSource objects
      else if (typeof source === 'number' || (source && typeof source === 'object')) {
        setImageSource(source);
      } else {
        console.warn('ImageAdvanced: Invalid image source type:', typeof source);
        setHasError(true);
        setIsLoading(false);
        setImageSource(null);
      }
    } catch (error) {
      console.warn('ImageAdvanced: Error processing image source:', error);
      setHasError(true);
      setIsLoading(false);
      setImageSource(null);
    }
  };

  const handleLoadStart = () => {
    setIsLoading(true);
    setIsLoaded(false);
    setHasError(false);
    onLoadStart?.();
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
    setIsLoaded(true);
    setHasError(false);
    onLoadEnd?.();
  };

  const handleError = (error: any) => {
    setIsLoading(false);
    setIsLoaded(false);
    setHasError(true);
    onError?.(error);

    // Try fallback source if available and not already tried
    if (fallbackSource && imageSource !== fallbackSource) {
      setTimeout(() => {
        setImageSource(fallbackSource);
        setHasError(false);
        setIsLoading(true);
      }, 100);
    }
  };

  const shouldShowPlaceholder = () => {
    if (!showPlaceholder) return false;

    // Show placeholder if:
    // 1. Currently loading, OR
    // 2. Has error, OR
    // 3. Not loaded yet and has no error
    return isLoading || hasError || (!isLoaded && !hasError);
  };

  const renderPlaceholder = () => {
    if (!shouldShowPlaceholder()) return null;

    const placeholderStyle = {
      backgroundColor: placeholderColor,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1,
      ...(style as any),
    };

    // Always use shimmer effect when placeholder should be shown
    // This provides a consistent loading experience and avoids text-based placeholders
    return (
      <ShimmerEffect
        isLoading={true}
        shimmerColors={shimmerColors}
        duration={shimmerDuration}
        variant="shimmer"
        direction="leftToRight"
        style={placeholderStyle}
      />
    );
  };

  const renderImage = () => {
    if (!imageSource) return null;

    return (
      <Image
        {...props}
        source={imageSource}
        style={[style, { zIndex: 0 }]}
        onLoadStart={handleLoadStart}
        onLoad={handleLoadEnd}
        onError={handleError}
        contentFit={contentFit}
        transition={transition}
        cachePolicy={cache}
        priority={priority}
        placeholder={placeholder}
      />
    );
  };

  return (
    <View style={[style, { position: 'relative' }]}>
      {renderImage()}
      {renderPlaceholder()}
      {children && (
        <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 2 }}>
          {children}
        </View>
      )}
    </View>
  );
};

// Helper function to determine if a source is local or remote
export const isRemoteSource = (source: string | ImageSource | number): boolean => {
  if (typeof source === 'string') {
    return source.startsWith('http://') || source.startsWith('https://');
  }
  if (typeof source === 'object' && source?.uri) {
    return source.uri.startsWith('http://') || source.uri.startsWith('https://');
  }
  return false;
};

// Helper function to create image source from string
export const createImageSource = (source: string): ImageSource => {
  if (
    source.startsWith('http://') ||
    source.startsWith('https://') ||
    source.startsWith('file://') ||
    source.startsWith('data:')
  ) {
    return { uri: source };
  }
  return { uri: source };
};

export default ImageAdvanced;
