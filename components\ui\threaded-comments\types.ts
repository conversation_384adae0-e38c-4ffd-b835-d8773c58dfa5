// Type definitions for threaded comments
export interface Comment {
  id: number;
  author: string;
  avatar: string;
  text: string;
  timestamp: string;
  likes: number;
  replies: Comment[];
}

export interface FlattenedComment {
  comment: Comment;
  depth: number;
  id: string;
}

export interface CommentItemProps {
  item: FlattenedComment;
  onReply: (commentId: number, authorName: string) => void;
  onLike: (commentId: number) => void;
  getMentionDisplayName?: (username: string) => string;
}

export interface ReplyingTo {
  id: number;
  author: string;
}

export interface ThreadedCommentsPopupProps {
  visible: boolean;
  onClose: () => void;
  postId: number;
  initialComments?: Comment[];
}

export interface CommentInputProps {
  newComment: string;
  replyingTo: ReplyingTo | null;
  onCommentChange: (text: string) => void;
  onSubmit: () => void;
  onCancelReply: () => void;
  onInputPress?: () => void;
  isExpanded?: boolean;
  textInputRef: React.RefObject<any>;
}
