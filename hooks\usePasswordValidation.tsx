import { useState, useEffect } from 'react';

interface PasswordValidationResult {
  hasMinLength: boolean;
  hasSpecialChar: boolean;
  hasNumberAndUpper: boolean;
  isValid: boolean;
}

export const usePasswordValidation = (password: string): PasswordValidationResult => {
  const [hasMinLength, setHasMinLength] = useState(false);
  const [hasSpecialChar, setHasSpecialChar] = useState(false);
  const [hasNumberAndUpper, setHasNumberAndUpper] = useState(false);

  useEffect(() => {
    // Check minimum length of 8 characters
    setHasMinLength(password.length >= 8);

    // Check for special characters
    setHasSpecialChar(/[*@#$%^&+=]/.test(password));

    // Check for numbers and uppercase letters
    setHasNumberAndUpper(/[0-9]/.test(password) && /[A-Z]/.test(password));
  }, [password]);

  // Check if all requirements are met
  const isValid = hasMinLength && hasSpecial<PERSON>har && hasNumberAndUpper;

  return {
    hasMinLength,
    hasSpecial<PERSON>har,
    hasNumberAndUpper,
    isValid
  };
};
