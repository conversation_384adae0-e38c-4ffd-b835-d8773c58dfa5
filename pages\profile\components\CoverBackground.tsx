import { LinearGradient } from 'expo-linear-gradient';
import { Image, View, StyleSheet } from 'react-native';

const CoverBackground = () => {
  return (
    <View style={styles.container}>
      {/* Background Image */}
      <Image
        source={{
          uri: 'https://images.samsung.com/vn/smartphones/galaxy-z-flip7/images/galaxy-z-flip7-features-selfie.jpg?imbypass=true',
        }}
        className={'absolute h-full w-full'}
        resizeMode="cover"
      />

      {/* Dark Overlay */}
      {/* <View className="absolute z-10 h-full w-full bg-black opacity-15" /> */}

      {/* Bottom Gradient */}
      <LinearGradient
        colors={[
          'rgba(255, 255, 255, 0)', // 100% - transparent
          'rgba(255, 255, 255, 0.2)', // 80%
          'rgba(255, 255, 255, 0.5)', // 60%
          'rgba(255, 255, 255, 0.85)', // 35%
          'rgba(255, 255, 255, 0.95)', // 20%
          'rgba(255, 255, 255, 1)', // 10%
          'rgba(255, 255, 255, 1)', // 0%
        ]}
        locations={[0, 0.2, 0.4, 0.65, 0.8, 0.9, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        className={'absolute bottom-0 left-0 z-20 h-1/4 w-full'}
      />
    </View>
  );
};

export default CoverBackground;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: 400,
    width: '100%',
    overflow: 'hidden',
  },
});
