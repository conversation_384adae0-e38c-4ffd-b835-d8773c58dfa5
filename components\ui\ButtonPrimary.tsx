import { Colors } from '@/utils/colors';
import { createShadow, ShadowOptions } from '@/utils/shadows';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';

interface ButtonPrimaryProps {
  bgColor?: string;
  border?: string;
  borderColor?: string;
  children: React.ReactNode;
  onPress?: () => void;
  style?: any;
  disabled?: boolean;
  enableShadow?: boolean;
  shadowOptions?: ShadowOptions;
}

const ButtonPrimary: React.FunctionComponent<ButtonPrimaryProps> = ({
  children,
  bgColor,
  border,
  borderColor,
  style,
  onPress,
  disabled = false,
  enableShadow = true,
  shadowOptions,
  ...props
}) => {
  const shadowStyles = enableShadow
    ? createShadow(shadowOptions)
    : {};

  return (
    <TouchableOpacity
      style={[
        styles.button,
        {
          backgroundColor: bgColor || Colors.primary[500],
          borderWidth: border ? 1 : 0,
          borderColor: borderColor,
          opacity: disabled ? 0.7 : 1,
          ...shadowStyles,
        },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={disabled}
      {...props}>
      {typeof children === 'string' ? <Text style={styles.text}>{children}</Text> : children}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 60,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    width: '100%',
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ButtonPrimary;
