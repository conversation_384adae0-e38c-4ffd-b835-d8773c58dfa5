import { useCallback, useRef, useState } from 'react';
import { Animated, Dimensions } from 'react-native';
import { State } from 'react-native-gesture-handler';
import { CLOSE_THRESHOLD, SNAP_POINT_DEFAULT, SNAP_POINT_EXPANDED } from './constants';

const { height } = Dimensions.get('window');

export const useBottomSheetGesture = (onClose: () => void) => {
  const sheetHeight = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;
  const [currentSnapPoint, setCurrentSnapPoint] = useState(SNAP_POINT_DEFAULT);
  const [isExpanded, setIsExpanded] = useState(false);

  const openSheet = useCallback(() => {
    Animated.parallel([
      Animated.timing(sheetHeight, {
        toValue: SNAP_POINT_DEFAULT,
        duration: 350,
        useNativeDriver: false, // Height animation requires layout
      }),
      Animated.timing(backgroundOpacity, {
        toValue: 0.5,
        duration: 350,
        useNativeDriver: true,
      }),
    ]).start();
    setCurrentSnapPoint(SNAP_POINT_DEFAULT);
    setIsExpanded(false);
  }, []);

  const closeSheet = useCallback(() => {
    Animated.parallel([
      Animated.timing(sheetHeight, {
        toValue: 0,
        duration: 350,
        useNativeDriver: false,
      }),
      Animated.timing(backgroundOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  }, [onClose]);

  const onGestureEvent = useCallback(
    (event: any) => {
      const { translationY } = event.nativeEvent;
      const newHeight = Math.max(
        SNAP_POINT_DEFAULT,
        Math.min(SNAP_POINT_EXPANDED, currentSnapPoint - translationY)
      );
      sheetHeight.setValue(newHeight);
    },
    [currentSnapPoint]
  );

  const onHandlerStateChange = useCallback(
    (event: any) => {
      if (event.nativeEvent.state === State.END) {
        const { translationY, velocityY } = event.nativeEvent;
        const currentHeight = currentSnapPoint - translationY;

        let targetSnapPoint: number;

        if (velocityY > 1000 || currentHeight < CLOSE_THRESHOLD) {
          closeSheet();
          return;
        } else if (translationY < -100 || velocityY < -1000) {
          targetSnapPoint = SNAP_POINT_EXPANDED;
          setIsExpanded(true);
        } else if (translationY > 100 || velocityY > 500) {
          targetSnapPoint = SNAP_POINT_DEFAULT;
          setIsExpanded(false);
        } else {
          targetSnapPoint = currentSnapPoint;
        }

        setCurrentSnapPoint(targetSnapPoint);

        Animated.spring(sheetHeight, {
          toValue: targetSnapPoint,
          useNativeDriver: false,
          tension: 120,
          friction: 10,
          velocity: -velocityY, // Invert velocity for height animation
        }).start();
      }
    },
    [currentSnapPoint, closeSheet]
  );

  const expandToFull = useCallback((callback?: () => void) => {
    Animated.spring(sheetHeight, {
      toValue: SNAP_POINT_EXPANDED,
      useNativeDriver: false,
      tension: 120,
      friction: 10,
    }).start((finished) => {
      if (finished && callback) {
        // Small delay to ensure layout has settled
        setTimeout(callback, 100);
      }
    });
    setCurrentSnapPoint(SNAP_POINT_EXPANDED);
    setIsExpanded(true);
  }, []);

  return {
    sheetHeight,
    backgroundOpacity,
    openSheet,
    closeSheet,
    expandToFull,
    isExpanded,
    onGestureEvent,
    onHandlerStateChange,
  };
};
