# Shimmer Effect Usage Guide

This guide explains how to implement and use the shimmer effect component throughout the WConnect application for smooth loading states.

## Table of Contents
- [Overview](#overview)
- [Basic Usage](#basic-usage)
- [Configuration Options](#configuration-options)
- [Component Examples](#component-examples)
- [Best Practices](#best-practices)
- [Common Patterns](#common-patterns)

## Overview

The `ShimmerEffect` component provides smooth loading animations that enhance user experience during data fetching. It supports various animation styles and directions.

## Basic Usage

### Import the Component

```tsx
import { ShimmerEffect } from '@/components';
```

### Basic Implementation

```tsx
const MyComponent = () => {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <ShimmerEffect
      isLoading={isLoading}
      style={{ width: 100, height: 20, borderRadius: 4 }}
    />
  );
};
```

## Configuration Options

### Props Interface

```tsx
interface ShimmerEffectProps {
  isLoading?: boolean;                    // Controls when shimmer is active
  shimmerColors?: string[];              // Array of colors for gradient
  duration?: number;                     // Animation duration in ms
  className?: string;                    // Tailwind CSS classes (optional)
  style?: ViewStyle;                     // React Native styles
  variant?: 'shimmer' | 'pulse';         // Animation type
  direction?: ShimmerDirection;          // Animation direction
}

type ShimmerDirection = 
  | 'leftToRight' 
  | 'rightToLeft' 
  | 'topToBottom' 
  | 'bottomToTop';
```

### Default Configuration

```tsx
const defaultShimmerConfig = {
  duration: 800,
  shimmerColors: [
    Colors.background.secondary,
    Colors.background.primary,
    Colors.background.secondary,
  ],
  variant: 'shimmer' as const,
  direction: 'leftToRight' as const,
};
```

## Component Examples

### 1. Chat Thread Shimmer

Perfect for message lists and chat interfaces:

```tsx
const ChatThreadShimmer = ({ isLoading }) => {
  const shimmerConfig = {
    duration: 800,
    shimmerColors: [
      Colors.background.secondary,
      Colors.background.primary,
      Colors.background.secondary,
    ],
    variant: 'shimmer' as const,
    direction: 'leftToRight' as const,
  };

  return (
    <View style={styles.shimmerChatContainer}>
      <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm, flex: 1 }]}>
        {/* Avatar shimmer - Circular */}
        <ShimmerEffect
          isLoading={isLoading}
          {...shimmerConfig}
          style={styles.shimmerAvatar}
        />

        {/* Message content */}
        <View style={styles.shimmerMessageContainer}>
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerName}
          />
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerMessage}
          />
        </View>

        {/* Time stamp */}
        <View style={styles.shimmerRightContainer}>
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerTime}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  shimmerChatContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background.primary,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    ...shadows.medium,
  },
  shimmerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    overflow: 'hidden', // Important for circular shapes
  },
  shimmerMessageContainer: {
    flex: 1,
    gap: 8,
  },
  shimmerName: {
    height: 16,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    width: '60%',
  },
  shimmerMessage: {
    height: 14,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    width: '80%',
  },
  shimmerRightContainer: {
    alignItems: 'flex-end',
  },
  shimmerTime: {
    height: 12,
    width: 40,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
  },
});
```

### 2. User Avatar List Shimmer

For horizontal user lists:

```tsx
const UserAvatarShimmer = ({ isLoading }) => {
  const shimmerConfig = {
    duration: 800,
    shimmerColors: [
      Colors.background.secondary,
      Colors.background.primary,
      Colors.background.secondary,
    ],
    variant: 'shimmer' as const,
    direction: 'leftToRight' as const,
  };

  return (
    <View style={styles.shimmerUserContainer}>
      <ShimmerEffect
        isLoading={isLoading}
        {...shimmerConfig}
        style={styles.shimmerUserAvatar}
      />
      <ShimmerEffect
        isLoading={isLoading}
        {...shimmerConfig}
        style={styles.shimmerUserName}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  shimmerUserContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 70,
  },
  shimmerUserAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.background.secondary,
    marginBottom: 8,
    overflow: 'hidden',
  },
  shimmerUserName: {
    height: 12,
    width: '80%',
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
  },
});
```

## Best Practices

### 1. Use Consistent Colors

Always use your app's color scheme for shimmer effects:

```tsx
const shimmerConfig = {
  shimmerColors: [
    Colors.background.secondary,  // Darker shade
    Colors.background.primary,    // Lighter shade
    Colors.background.secondary,  // Back to darker
  ],
};
```

### 2. Match Component Dimensions

Shimmer placeholders should match the actual content dimensions:

```tsx
// If your text is 16px height
const shimmerText = {
  height: 16,
  backgroundColor: Colors.background.secondary,
  borderRadius: 4,
};

// If your avatar is 40x40 circular
const shimmerAvatar = {
  width: 40,
  height: 40,
  borderRadius: 20,
  overflow: 'hidden', // Critical for circular shapes
};
```

### 3. Use Proper Loading States

Control shimmer with actual loading states:

```tsx
const MyComponent = () => {
  const [isLoading, setIsLoading] = useState(false);
  
  const fetchData = async () => {
    setIsLoading(true);
    try {
      const data = await api.getData();
      // Handle data
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FlatList
      data={isLoading ? Array(6).fill(null) : actualData}
      renderItem={isLoading ? renderShimmerItem : renderItem}
      keyExtractor={isLoading ? (_, index) => `shimmer-${index}` : keyExtractor}
    />
  );
};
```

## Common Patterns

### 1. FlatList with Shimmer

```tsx
<FlatList
  data={isLoading ? Array(numberOfShimmerItems).fill(null) : actualData}
  keyExtractor={isLoading ? (_, index) => `shimmer-${index}` : normalKeyExtractor}
  renderItem={isLoading ? renderShimmerItem : renderActualItem}
  // ... other props
/>
```

### 2. Image with Shimmer

```tsx
const ImageWithShimmer = ({ source, style }) => {
  return (
    <ImageAdvanced
      source={source}
      style={style}
      useShimmer={true}
      shimmerColors={[
        'rgba(240, 240, 240, 0.8)',
        'rgba(255, 255, 255, 0.4)', 
        'rgba(240, 240, 240, 0.8)'
      ]}
      shimmerDuration={1000}
    />
  );
};
```

### 3. Card Layout Shimmer

```tsx
const CardShimmer = ({ isLoading }) => (
  <View style={styles.cardContainer}>
    {/* Header */}
    <View style={styles.cardHeader}>
      <ShimmerEffect
        isLoading={isLoading}
        style={styles.shimmerAvatar}
      />
      <View style={styles.cardHeaderText}>
        <ShimmerEffect
          isLoading={isLoading}
          style={styles.shimmerTitle}
        />
        <ShimmerEffect
          isLoading={isLoading}
          style={styles.shimmerSubtitle}
        />
      </View>
    </View>
    
    {/* Content */}
    <ShimmerEffect
      isLoading={isLoading}
      style={styles.shimmerContent}
    />
    
    {/* Footer */}
    <View style={styles.cardFooter}>
      {[1, 2, 3].map((_, index) => (
        <ShimmerEffect
          key={index}
          isLoading={isLoading}
          style={styles.shimmerButton}
        />
      ))}
    </View>
  </View>
);
```

### 4. Different Animation Variants

```tsx
// Shimmer effect (sliding gradient)
<ShimmerEffect
  isLoading={true}
  variant="shimmer"
  direction="leftToRight"
  style={myStyle}
/>

// Pulse effect (opacity animation)
<ShimmerEffect
  isLoading={true}
  variant="pulse"
  style={myStyle}
/>
```

### 5. Custom Animation Directions

```tsx
// Horizontal animations
<ShimmerEffect direction="leftToRight" />
<ShimmerEffect direction="rightToLeft" />

// Vertical animations  
<ShimmerEffect direction="topToBottom" />
<ShimmerEffect direction="bottomToTop" />
```

## Tips for Better UX

1. **Duration**: Use 800-1200ms for smooth, not-too-fast animations
2. **Color Contrast**: Ensure sufficient contrast between shimmer colors
3. **Shape Matching**: Always match the shape of actual content (circular avatars, rounded cards)
4. **Consistent Timing**: Use the same duration across similar components
5. **Overflow Hidden**: Always use `overflow: 'hidden'` for circular/rounded shapes

## Integration with Server API

When integrating with your server, control the loading state based on actual API calls:

```tsx
const useApiData = () => {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/data');
      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return { data, isLoading, fetchData };
};
```

This guide should help you implement consistent and smooth shimmer effects throughout your WConnect application!
