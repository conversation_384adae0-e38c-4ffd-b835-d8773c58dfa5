import { ExpandableButton } from '@/components/button/Button';
import { SearchBar } from '@/components/searchbar/children/SearchBar';
import Divider from '@/components/ui/Divider';
import ImageAdvanced from '@/components/ui/ImageAdvanced';
import SpotifyService from '@/services/api/SpotifyService';
import ProfileData from '@/store/data/ProfileData';
import { SelectedTrack, SpotifyTrack } from '@/types/spotify';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { SafeAreaContainers } from '@/utils/layoutStyles';
import { safeNavigate } from '@/utils/navigationUtils';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetFlatList } from '@gorhom/bottom-sheet';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import LottieView from 'lottie-react-native';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AvatarTitle from './components/AvatarTitle';
import ChipTabs from './components/ChipTabs';
import Collection from './components/Collection';
import CoverBackground from './components/CoverBackground';
import Friends from './components/Friends';
import Information from './components/Information';

const { width: screenWidth } = Dimensions.get('window');

const ProfilePage = () => {
  // Calculate dynamic button widths based on screen size
  const buttonWidth = Math.min(165, (screenWidth - 32 - 49 - 16) / 2); // 32 = horizontal padding, 49 = options button, 16 = gaps
  const buttonFullWidth = screenWidth - 32; // Full width button calculation

  // State for music selection
  const [isMusicBottomSheetVisible, setIsMusicBottomSheetVisible] = useState(false);
  const [selectedTrack, setSelectedTrack] = useState<SelectedTrack | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [tracks, setTracks] = useState<SpotifyTrack[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const bottomSheetRef = useRef<BottomSheet>(null);

  const openBottomSheet = () => {
    setIsMusicBottomSheetVisible(true);
    bottomSheetRef.current?.snapToIndex(0);
  };

  const closeBottomSheet = () => {
    bottomSheetRef.current?.close();
  };

  const handleSelectTrack = (track: SelectedTrack) => {
    setSelectedTrack(track);
    setIsMusicBottomSheetVisible(false);
  };

  const handleCloseMusicBottomSheet = () => {
    setIsMusicBottomSheetVisible(false);
  };

  const handleOpenComments = useCallback((post: any) => {
    router.push({
      pathname: '/comments-modal',
      params: {
        postId: post.id.toString(),
        initialComments: JSON.stringify(post.comments),
      },
    });
  }, []);

  const searchTracks = async (query: string) => {
    if (!query.trim()) {
      setTracks([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await SpotifyService.searchTracks(query, 20);
      setTracks(response.tracks.items);
    } catch (error) {
      console.error('Error searching tracks:', error);
      Alert.alert('Error', 'Failed to search tracks. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    // Simple debounce
    const timeoutId = setTimeout(() => {
      searchTracks(text);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleTrackSelect = (track: SpotifyTrack) => {
    const artistNames = track.artists.map((artist) => artist.name).join(', ');
    const albumArt = track.album.images[0]?.url || '';

    const selectedTrack: SelectedTrack = {
      id: track.id,
      name: track.name,
      artist: artistNames,
      albumArt,
      previewUrl: track.preview_url,
    };

    setSelectedTrack(selectedTrack);
  };

  const renderTrackItem = ({ item }: { item: SpotifyTrack }) => {
    const artistNames = item.artists.map((artist) => artist.name).join(', ');
    const albumArt = item.album.images[0]?.url || '';
    const isSelected = selectedTrack?.id === item.id;

    return (
      <TouchableOpacity
        style={[styles.trackItem, isSelected && styles.selectedTrackItem]}
        onPress={() => handleTrackSelect(item)}
        activeOpacity={0.7}>
        <View style={styles.trackContent}>
          <ImageAdvanced
            source={albumArt}
            style={styles.albumArt}
            contentFit="cover"
            showPlaceholder={false}
          />

          <View style={styles.trackInfo}>
            <Text style={styles.trackName} numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={styles.artistName} numberOfLines={1}>
              {artistNames}
            </Text>
          </View>

          {isSelected && (
            <View style={styles.selectedIcon}>
              <Ionicons name="checkmark-circle" size={24} color={Colors.primary[500]} />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View className={'flex-1 bg-background-primary'}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          nestedScrollEnabled={true}>
          <CoverBackground />

          <View
            style={SafeAreaContainers.topSafeMargin}
            className="flex-row items-center justify-start px-4 py-2">
            <Pressable
              onPress={() => {
                router.back();
              }}
              className="h-11 w-11 items-center justify-center rounded-full bg-white">
              <Ionicons name="arrow-back" size={24} color={Colors.primary['950']} />
            </Pressable>
            <Pressable onPress={() => openBottomSheet()} className="ml-3 max-w-52 flex-1">
              <BlurView
                className="h-11 flex-row items-center justify-between gap-2 rounded-full px-2"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  overflow: 'hidden',
                }}
                experimentalBlurMethod="dimezisBlurView"
                intensity={25}
                tint="dark">
                <View className="flex-1 flex-row items-center justify-start gap-2">
                  <ImageAdvanced
                    source={
                      selectedTrack?.albumArt ||
                      'https://upload.wikimedia.org/wikipedia/vi/5/52/Ros%C3%A9_and_Bruno_Mars_-_Apt..png'
                    }
                    className="h-8 w-8"
                    contentFit="cover"
                    showPlaceholder={false}
                    style={{ borderRadius: 16, width: 32, height: 32 }}
                  />
                  <View className="flex-1 items-start justify-center">
                    <Text className="font-roboto-medium text-[10px] text-white" numberOfLines={1}>
                      {selectedTrack?.name || 'APT'}
                    </Text>
                    <Text className="font-roboto-regular text-[8px] text-white" numberOfLines={1}>
                      {selectedTrack?.artist || 'Rose ft Bruno Mars'}
                    </Text>
                  </View>
                </View>
                <View className="h-6 w-6 items-center justify-center">
                  <LottieView
                    source={require('../../assets/lotties/Recording.json')}
                    style={{ width: 30, height: 30 }}
                    autoPlay
                    loop
                  />
                </View>
              </BlurView>
            </Pressable>
          </View>

          <View className="flex-1 justify-end px-4 py-2 pt-44">
            <AvatarTitle
              name={ProfileData.profile?.name || 'User'}
              isVerified={true}
              avatarUrl={ProfileData.profile?.avatar || ''}
              subTitle={
                ProfileData.profile?.career ? [ProfileData.profile.career] : ['User']
              }></AvatarTitle>
          </View>

          <View
            className="flex-1 flex-row items-center justify-between px-4 py-2"
            style={styles.buttonContainer}>
            <ExpandableButton
              title={'Add a post'}
              icon="plus"
              withPressAnimation={true}
              width={buttonWidth}
              textStyle={{
                fontFamily: Fonts.roboto.regular,
                fontSize: 14,
              }}
              isLoading={false}
              onPress={function (): void {
                console.log('Add a post pressed');
              }}
            />
            <ExpandableButton
              title={'Subscribe'}
              width={buttonWidth}
              withPressAnimation={true}
              textStyle={{
                fontFamily: Fonts.roboto.regular,
                fontSize: 14,
                color: Colors.primary[950],
              }}
              style={{
                backgroundColor: 'transparent',
                borderColor: Colors.primary[500],
                borderWidth: 1,
              }}
              isLoading={false}
              onPress={function (): void {
                console.log('Subscribe pressed');
              }}
            />
            <TouchableOpacity onPress={() => {}} style={styles.optionsButton} activeOpacity={0.7}>
              <Ionicons name="ellipsis-horizontal" size={28} color={Colors.text.muted} />
            </TouchableOpacity>
          </View>

          <Divider></Divider>
          <View className="px-4">
            <Information
              socialLinks={[
                {
                  platform: 'GitHub',
                  username: 'limgolden',
                  url: 'github.com/limgolden',
                },
              ]}
              otherInfo={['More information about me']}
              career="Software Engineer & Gamer"
              location="Los Angeles, CA"></Information>
            <ExpandableButton
              title={'Edit your information'}
              width={buttonFullWidth}
              isLoading={false}
              backgroundColor={Colors.background.muted}
              textColor={Colors.text.primary}
              onPress={function (): void {
                safeNavigate('/detail-info');
              }}
            />
          </View>
          <Divider></Divider>
          <View className="container mx-auto py-2">
            <Collection collections={ProfileData.profile?.collections} />
          </View>
          <View className="container mx-auto px-4 py-2">
            <ExpandableButton
              title={'Edit your Collection'}
              width={buttonFullWidth}
              isLoading={false}
              backgroundColor={Colors.background.muted}
              textColor={Colors.text.primary}
              onPress={function (): void {
                console.log('View more information pressed');
              }}
            />
          </View>
          <Divider></Divider>
          <View className="container mx-auto px-4 py-2">
            <Friends friends={ProfileData.profile?.friends} />
          </View>
          <Divider></Divider>
          <ChipTabs posts={ProfileData.posts} onOpenComments={handleOpenComments}></ChipTabs>
        </ScrollView>
        <BottomSheet
          ref={bottomSheetRef}
          index={isMusicBottomSheetVisible ? 0 : -1}
          snapPoints={useMemo(() => ['50%', '90%'], [])}
          onChange={(index) => {
            if (index === -1) setIsMusicBottomSheetVisible(false);
          }}
          enablePanDownToClose>
          <BottomSheetFlatList
            data={tracks}
            keyExtractor={(item) => item.id}
            renderItem={renderTrackItem}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={[
              styles.tracksList,
              { paddingBottom: 16, paddingHorizontal: 16 },
            ]}
            keyboardShouldPersistTaps="handled"
            ListHeaderComponent={
              <View>
                <View style={styles.searchContainer}>
                  <SearchBar
                    placeholder="Type to search music..."
                    tint="#31d3f7"
                    iconCenterOffset={2.9}
                    textCenterOffset={2.65}
                    renderLeadingIcons={() => (
                      <Ionicons name="search" size={20} color={Colors.text.muted} />
                    )}
                    onSearch={handleSearchChange}
                  />
                </View>

                {isLoading && (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={Colors.primary[500]} />
                    <Text style={styles.loadingText}>Searching tracks...</Text>
                  </View>
                )}

                {!isLoading && searchQuery.trim() && tracks.length === 0 && (
                  <View style={styles.emptyContainer}>
                    <Ionicons name="musical-notes-outline" size={48} color={Colors.text.muted} />
                    <Text style={styles.emptyText}>No tracks found</Text>
                  </View>
                )}

                {!searchQuery.trim() && tracks.length === 0 && (
                  <View style={styles.emptyContainer}>
                    <Ionicons name="search-outline" size={48} color={Colors.text.muted} />
                    <Text style={styles.emptyText}>Search for Music</Text>
                  </View>
                )}
              </View>
            }
          />
        </BottomSheet>
      </View>
    </GestureHandlerRootView>
  );
};

export default ProfilePage;

const styles = StyleSheet.create({
  buttonContainer: {
    gap: 8, // Add consistent gap between buttons
  },
  optionsButton: {
    width: 49,
    height: 49,
    borderRadius: 100,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.text.muted,
    justifyContent: 'center',
    alignItems: 'center',
    flexShrink: 0, // Prevent shrinking
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20, // Add some bottom padding for better UX
  },
  // Music search styles
  sectionTitle: {
    fontSize: 18,
    fontFamily: Fonts.roboto.medium,
    color: Colors.text.primary,
    marginBottom: 16,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.muted,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: Fonts.roboto.regular,
    color: Colors.text.primary,
  },
  clearButton: {
    padding: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
    gap: 12,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: Fonts.roboto.regular,
    color: Colors.text.secondary,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
    gap: 12,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: Fonts.roboto.regular,
    color: Colors.text.secondary,
  },
  tracksList: {
    paddingBottom: 16,
  },
  trackItem: {
    backgroundColor: Colors.background.primary,
    borderRadius: 12,
    marginBottom: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.background.muted,
  },
  selectedTrackItem: {
    borderColor: Colors.primary[500],
    backgroundColor: Colors.primary[50],
  },
  trackContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  albumArt: {
    width: 50,
    height: 50,
    borderRadius: 8,
  },
  trackInfo: {
    flex: 1,
    gap: 4,
  },
  trackName: {
    fontSize: 16,
    fontFamily: Fonts.roboto.medium,
    color: Colors.text.primary,
  },
  artistName: {
    fontSize: 14,
    fontFamily: Fonts.roboto.regular,
    color: Colors.text.secondary,
  },
  selectedIcon: {
    marginLeft: 8,
  },
});
