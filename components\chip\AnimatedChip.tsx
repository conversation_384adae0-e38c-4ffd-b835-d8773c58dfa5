import { Colors } from "@/utils/colors";
import { Ionicons } from "@expo/vector-icons";
import { useEffect } from "react";
import { Pressable, StyleSheet, ViewStyle } from "react-native";
import { Iconify } from "react-native-iconify";
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import type { AnimatedChipProps } from "./Chip.types";

export const AnimatedChip = ({
  label,
  icon,
  isActive,
  onPress,
  activeIcon,
  activeColor,
  iconType = 'ionicon',
}: AnimatedChipProps) => {
  const progress = useSharedValue<number>(isActive ? 1 : 0);
  const iconOpacity = useSharedValue<number>(isActive ? 1 : 0);
  
  useEffect(() => {
    progress.value = withTiming<number>(isActive ? 1 : 0, { duration: 500 });
    iconOpacity.value = withTiming<number>(isActive ? 1 : 0.5, {
      duration: 500,
    });
  }, [isActive]);

  const renderIcon = () => {
    const currentIcon = isActive && activeIcon ? activeIcon : icon;
    const iconColor = isActive ? "#FFFFFF" : "#AAAAAA";

    switch (iconType) {
      case 'ionicon':
        return (
          <Ionicons
            name={currentIcon as any}
            size={18}
            color={iconColor}
          />
        );
      case 'iconify':
        return (
          <Iconify
            icon={currentIcon as string}
            size={18}
            color={iconColor}
          />
        );
      case 'svg':
        if (typeof currentIcon === 'function') {
          const SvgComponent = currentIcon;
          return <SvgComponent width={18} height={18} fill={iconColor} />;
        }
        return null;
      case 'symbol':
      default:
        // Fallback to ionicon for any symbol usage
        return (
          <Ionicons
            name="ellipse" // Generic fallback icon
            size={18}
            color={iconColor}
          />
        );
    }
  };

  const animatedContainerStyle = useAnimatedStyle<ViewStyle>(() => {
    return {
      width: withSpring(isActive ? 140 : 50, {
        damping: 90,
        velocity: 2,
        stiffness: 180,
      }),

      backgroundColor: interpolateColor(
        progress.value,
        [0, 1],
        [Colors.background.muted, activeColor!],
      ),
    };
  });

  const animatedIconOpacityStyle = useAnimatedStyle(() => {
    return {
      opacity: iconOpacity.value,
    };
  });

  const animatedTextStyle = useAnimatedStyle(() => {
    return {
      opacity: progress.value,
      transform: [
        {
          translateX: withTiming(isActive ? 0 : -8, { duration: 200 }),
        },
      ],
    };
  });

  return (
    <Pressable onPress={onPress}>
      <Animated.View style={[styles.chip, animatedContainerStyle]}>
        <Animated.View style={[animatedIconOpacityStyle]}>
          {renderIcon()}
        </Animated.View>
        {isActive && (
          <Animated.View>
            <Animated.View style={[styles.labelWrapper, animatedTextStyle]}>
              <Animated.Text style={styles.label}>{label}</Animated.Text>
            </Animated.View>
          </Animated.View>
        )}
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    gap: 8,
    // padding: 12,
  },
  chip: {
    height: 40,
    borderRadius: 100,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  labelWrapper: {
    marginLeft: 8,
    minWidth: 60,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    color: "#FFFFFF",
  },
});
