import { ImageAdvanced } from "@/components";
import { ExpandableButton } from "@/components/button/Button";
import Divider from "@/components/ui/Divider";
import TopNavigationTitle from "@/components/ui/TopNavigationTitle";
import { useMediaPicker } from "@/hooks/useMediaPicker";
import ProfileEditData, { FIELD_CONFIG, ProfileEditInfo } from "@/store/data/ProfileEditData";
import { Colors } from "@/utils/colors";
import { Fonts } from "@/utils/fonts";
import { FlexLayouts, SafeAreaContainers, Spacing } from "@/utils/layoutStyles";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from 'expo-image-picker';
import { useNavigation, useRouter } from "expo-router";
import { useState } from "react";
import { Alert, Dimensions, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import InfoInput from "./components/InfoInput";

const DetailInformationPage = () => {
    const buttonWidth = Dimensions.get('window').width - 30;
    const navigation = useNavigation();
    const router = useRouter();
    const { pickSingleMedia, isLoading } = useMediaPicker();

    // State for selected media
    const [selectedAvatar, setSelectedAvatar] = useState<string>(ProfileEditData.avatar);
    const [selectedCoverImage, setSelectedCoverImage] = useState<string>(ProfileEditData.coverImage);
    const [isAvatarSaving, setIsAvatarSaving] = useState(false);
    const [isCoverSaving, setIsCoverSaving] = useState(false);

    // Get the profile data
    const profileData: ProfileEditInfo = ProfileEditData;

    // Helper function to get field value
    const getFieldValue = (fieldName: keyof ProfileEditInfo): string => {
        const value = profileData[fieldName];
        if (Array.isArray(value)) {
            return value.join(', ');
        }
        return String(value || '');
    };

    // Helper function to navigate to DetailEditPage
    const navigateToEdit = (fieldName: keyof typeof FIELD_CONFIG) => {
        const config = FIELD_CONFIG[fieldName];
        const value = getFieldValue(fieldName as keyof ProfileEditInfo);

        router.push({
            pathname: '/info-edit' as any,
            params: {
                title: value ? `Edit ${config.label}` : `Add ${config.label}`,
                isAdditionButton: 'true',
                isTextArea: config.isTextArea.toString(),
                value: value,
                guideText: config.guideText,
                privacyField: 'true',
                placeholder: config.placeholder,
                label: config.label,
                isDropdown: config.isDropdown.toString(),
                dropdownData: config.isDropdown && 'options' in config ?
                    JSON.stringify(config.options) : undefined
            }
        });
    };

    // Helper function to render field (InfoInput or ExpandableButton)
    const renderField = (fieldName: keyof typeof FIELD_CONFIG) => {
        const config = FIELD_CONFIG[fieldName];
        const value = getFieldValue(fieldName as keyof ProfileEditInfo);
        const isEmpty = !value || value.trim() === '';

        // Always show bio as InfoInput (even if empty)
        if (fieldName === 'bio' || !isEmpty) {
            return (
                <InfoInput
                    key={fieldName}
                    label={config.label}
                    value={value}
                    isTextArea={config.isTextArea}
                    isIcon={true}
                    placeholder={config.placeholder}
                    onPress={() => navigateToEdit(fieldName)}
                />
            );
        }

        // Show ExpandableButton for empty fields (except bio)
        return (
            <ExpandableButton
                key={fieldName}
                title={`Add ${config.label.toLowerCase()}`}
                withPressAnimation={true}
                width={buttonWidth}
                textStyle={{
                    fontFamily: Fonts.roboto.regular,
                    fontSize: 14,
                }}
                borderRadius={15}
                isLoading={false}
                onPress={() => navigateToEdit(fieldName)}
            />
        );
    };

    // Helper function to check if website exists in social links
    const hasWebsite = profileData.socialLinks.some(link =>
        link.platform.toLowerCase() === 'website' ||
        link.platform.toLowerCase() === 'personal website'
    );

    // Media picker functions
    const handleAvatarSelection = () => {
        Alert.alert(
            'Select Avatar',
            'Choose how you want to update your profile picture',
            [
                {
                    text: 'Camera',
                    onPress: async () => {
                        const { status } = await ImagePicker.requestCameraPermissionsAsync();
                        if (status !== 'granted') {
                            Alert.alert('Permission Required', 'Camera permission is needed to take photos');
                            return;
                        }

                        const result = await ImagePicker.launchCameraAsync({
                            mediaTypes: ImagePicker.MediaTypeOptions.Images,
                            allowsEditing: true,
                            aspect: [1, 1], // Square aspect ratio for avatar
                            quality: 0.8,
                        });

                        if (!result.canceled && result.assets[0]) {
                            const newAvatar = result.assets[0].uri;
                            setSelectedAvatar(newAvatar);
                            setIsAvatarSaving(true);

                            // Auto-save immediately
                            try {
                                // Simulate API call delay
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Here you would typically save to your backend/database
                                // await saveProfileMedia({ avatar: newAvatar });

                                // Brief success feedback without interrupting flow
                                console.log('Profile picture updated successfully!');
                            } catch (error) {
                                Alert.alert('Error', 'Failed to update profile picture. Please try again.');
                                setSelectedAvatar(ProfileEditData.avatar); // Revert on error
                            } finally {
                                setIsAvatarSaving(false);
                            }
                        }
                    },
                },
                {
                    text: 'Gallery',
                    onPress: async () => {
                        const media = await pickSingleMedia(ImagePicker.MediaTypeOptions.Images);
                        if (media) {
                            setSelectedAvatar(media.uri);
                            setIsAvatarSaving(true);

                            // Auto-save immediately
                            try {
                                // Simulate API call delay
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Here you would typically save to your backend/database
                                // await saveProfileMedia({ avatar: media.uri });

                                // Brief success feedback without interrupting flow
                                console.log('Profile picture updated successfully!');
                            } catch (error) {
                                Alert.alert('Error', 'Failed to update profile picture. Please try again.');
                                setSelectedAvatar(ProfileEditData.avatar); // Revert on error
                            } finally {
                                setIsAvatarSaving(false);
                            }
                        }
                    },
                },
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
            ]
        );
    };

    const handleCoverImageSelection = () => {
        Alert.alert(
            'Select Cover Image',
            'Choose how you want to update your cover photo',
            [
                {
                    text: 'Camera',
                    onPress: async () => {
                        const { status } = await ImagePicker.requestCameraPermissionsAsync();
                        if (status !== 'granted') {
                            Alert.alert('Permission Required', 'Camera permission is needed to take photos');
                            return;
                        }

                        const result = await ImagePicker.launchCameraAsync({
                            mediaTypes: ImagePicker.MediaTypeOptions.Images,
                            allowsEditing: true,
                            aspect: [16, 9], // Wide aspect ratio for cover image
                            quality: 0.8,
                        });

                        if (!result.canceled && result.assets[0]) {
                            const newCoverImage = result.assets[0].uri;
                            setSelectedCoverImage(newCoverImage);
                            setIsCoverSaving(true);

                            // Auto-save immediately
                            try {
                                // Simulate API call delay
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Here you would typically save to your backend/database
                                // await saveProfileMedia({ coverImage: newCoverImage });

                                // Brief success feedback without interrupting flow
                                console.log('Cover photo updated successfully!');
                            } catch (error) {
                                Alert.alert('Error', 'Failed to update cover photo. Please try again.');
                                setSelectedCoverImage(ProfileEditData.coverImage); // Revert on error
                            } finally {
                                setIsCoverSaving(false);
                            }
                        }
                    },
                },
                {
                    text: 'Gallery',
                    onPress: async () => {
                        const media = await pickSingleMedia(ImagePicker.MediaTypeOptions.Images);
                        if (media) {
                            setSelectedCoverImage(media.uri);
                            setIsCoverSaving(true);

                            // Auto-save immediately
                            try {
                                // Simulate API call delay
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Here you would typically save to your backend/database
                                // await saveProfileMedia({ coverImage: media.uri });

                                // Brief success feedback without interrupting flow
                                console.log('Cover photo updated successfully!');
                            } catch (error) {
                                Alert.alert('Error', 'Failed to update cover photo. Please try again.');
                                setSelectedCoverImage(ProfileEditData.coverImage); // Revert on error
                            } finally {
                                setIsCoverSaving(false);
                            }
                        }
                    },
                },
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
            ]
        );
    };

    return (
        <ScrollView
            contentContainerStyle={styles.scrollContent}
            style={[SafeAreaContainers.topSafe, { backgroundColor: Colors.background.primary }]}
            horizontal={false}
            showsVerticalScrollIndicator={false}
        >
            <TopNavigationTitle title="Edit profile" isAdditionButton={false} />

            {/* Profile Media */}
            <View style={styles.infoContainer}>
                <Text style={styles.textTitle}>Profile Media</Text>
                <View style={[FlexLayouts.rowBetween, styles.mediaRow]}>
                    <View style={styles.mediaItemContainer}>
                        <View style={[styles.avatarContainer, isAvatarSaving && styles.savingOpacity]}>
                            <ImageAdvanced
                                source={selectedAvatar}
                                style={styles.avatarImage}
                                contentFit="cover"
                            >
                            </ImageAdvanced>
                        </View>
                        <TouchableOpacity
                            style={styles.editButton}
                            activeOpacity={0.8}
                            onPress={handleAvatarSelection}
                            disabled={isLoading || isAvatarSaving}
                        >
                            <Ionicons
                                name={isAvatarSaving ? "hourglass" : "pencil"}
                                color="white"
                                size={16}
                            />
                        </TouchableOpacity>
                    </View>
                    <View style={styles.coverContainer}>
                        <View style={[styles.coverImageContainer, isCoverSaving && styles.savingOpacity]}>
                            <ImageAdvanced
                                source={selectedCoverImage}
                                style={styles.coverImage}
                                contentFit="cover"
                            >

                            </ImageAdvanced>
                        </View>
                        <TouchableOpacity
                            style={styles.editButton}
                            activeOpacity={0.8}
                            onPress={handleCoverImageSelection}
                            disabled={isLoading || isCoverSaving}
                        >
                            <Ionicons
                                name={isCoverSaving ? "hourglass" : "pencil"}
                                color="white"
                                size={16}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>

            <Divider />

            {/* Information */}
            <View style={styles.infoContainer}>
                <Text style={styles.textTitle}>Information</Text>
                {renderField('name')}
                {renderField('gender')}
                {renderField('birthday')}
                {renderField('hometown')}
                {renderField('bio')}
            </View>

            <Divider />

            {/* Subscriptions */}
            <View style={styles.infoContainer}>
                <Text style={styles.textTitle}>Subscriptions</Text>
                <Text style={styles.textSubTitle}>Pricing</Text>
                {renderField('monthlyPrice')}
                {renderField('yearlyPrice')}
                <Text style={styles.textSubTitle}>Custom</Text>
                <ExpandableButton
                    title={'Sales and coupons'}
                    withPressAnimation={true}
                    width={buttonWidth}
                    textStyle={{
                        fontFamily: Fonts.roboto.regular,
                        fontSize: 14,
                    }}
                    borderRadius={15}
                    isLoading={false}
                    onPress={() => console.log('Sales and coupons pressed')}
                />
                <ExpandableButton
                    title={'Remove subscription'}
                    withPressAnimation={true}
                    width={buttonWidth}
                    textStyle={{
                        fontFamily: Fonts.roboto.regular,
                        fontSize: 14,
                    }}
                    backgroundColor={Colors.error}
                    borderRadius={15}
                    isLoading={false}
                    onPress={() => console.log('Remove subscription pressed')}
                />
            </View>

            {/* Details */}
            <View style={styles.infoContainer}>
                <Text style={styles.textTitle}>Details</Text>
                <Text style={styles.textSubTitle}>General</Text>
                {renderField('pronouns')}
                <Text style={styles.textSubTitle}>Work</Text>
                {renderField('career')}
                {renderField('companies')}
                <Text style={styles.textSubTitle}>Education</Text>
                {renderField('education')}
                <Text style={styles.textSubTitle}>Relationship</Text>
                {renderField('relationship')}
            </View>

            <Divider />

            {/* Links */}
            <View style={styles.infoContainer}>
                <Text style={styles.textTitle}>Links</Text>

                {/* Render existing social links */}
                {profileData.socialLinks.map((link, index) => (
                    <InfoInput
                        key={`${link.platform}-${index}`}
                        label={link.platform}
                        value={link.url}
                        isIcon={true}
                        onPress={() => {
                            router.push({
                                pathname: '/info-edit' as any,
                                params: {
                                    title: `Edit ${link.platform} Link`,
                                    isAdditionButton: 'true',
                                    isTextArea: 'false',
                                    value: link.url,
                                    guideText: FIELD_CONFIG.socialLink.guideText,
                                    privacyField: 'true',
                                    placeholder: `Enter your ${link.platform.toLowerCase()} URL`,
                                    label: `${link.platform} URL`,
                                    isDropdown: 'false'
                                }
                            });
                        }}
                    />
                ))}

                {/* Add website button if no website exists yet */}
                {!hasWebsite && (
                    <ExpandableButton
                        title="Add website"
                        withPressAnimation={true}
                        width={buttonWidth}
                        textStyle={{
                            fontFamily: Fonts.roboto.regular,
                            fontSize: 14,
                        }}
                        borderRadius={15}
                        isLoading={false}
                        onPress={() => navigateToEdit('website')}
                    />
                )}

                <ExpandableButton
                    title={'Add more links'}
                    withPressAnimation={true}
                    width={buttonWidth}
                    textStyle={{
                        fontFamily: Fonts.roboto.regular,
                        fontSize: 14,
                    }}
                    borderRadius={15}
                    isLoading={false}
                    onPress={() => console.log('Add more links pressed')}
                />
            </View>
        </ScrollView>
    );
}

export default DetailInformationPage;

const styles = StyleSheet.create({
    scrollContent: {
        flexGrow: 1,
        paddingBottom: 50,
        marginTop: 8,
    },
    editButton: {
        position: "absolute",
        bottom: 10,
        right: 10,
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: Colors.primary["950"],
        alignItems: "center",
        justifyContent: "center",
    },
    infoContainer: {
        paddingVertical: 10,
        gap: Spacing.gap.xl
    },
    textTitle: {
        fontSize: 18,
        fontFamily: Fonts.roboto.bold,
        color: Colors.text.primary,
    },
    textSubTitle: {
        fontSize: 14,
        fontFamily: Fonts.roboto.regular,
        color: Colors.text.secondary,
        marginTop: -10,
    },
    avatarContainer: {
        width: 120,
        height: 120,
        borderRadius: 80,
        overflow: 'hidden',
    },
    coverImageContainer: {
        width: '100%',
        height: 120,
        borderRadius: 15,
        overflow: 'hidden',
    },
    mediaRow: {
        gap: Spacing.gap.md,
    },
    mediaItemContainer: {
        alignItems: 'center',
    },
    coverContainer: {
        flex: 1,
    },
    mediaLabel: {
        fontSize: 14,
        fontFamily: Fonts.roboto.regular,
        color: Colors.text.secondary,
        marginBottom: Spacing.gap.sm,
    },
    avatarImage: {
        width: '100%',
        height: '100%',
    },
    coverImage: {
        width: '100%',
        height: '100%',
    },
    savingOpacity: {
        opacity: 0.7, // Adjust as needed for saving effect
    },
});
