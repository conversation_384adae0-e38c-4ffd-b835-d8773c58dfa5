import { Message } from "@/types/media";

export interface ChatItem {
  id: string;
  name: string;
  message: string;
  time: string;
  avatar: string;
  unreadCount: number;
  isOnline: boolean;
  isRead: boolean;
  messages: Message[];
}

const detailedChatData: ChatItem[] = [
    {
        id: '1',
        name: '<PERSON>',
        message: 'Happy Birthday, my bro',
        time: '9:25 AM',
        avatar: 'https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        unreadCount: 2,
        isOnline: true,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'Hey! How are you doing?',
                timestamp: '8:45 AM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'I\'m good! Thanks for asking',
                timestamp: '8:47 AM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '8:47 AM',
                readAt: '8:48 AM'
            },
            {
                id: 'msg3',
                text: '',
                timestamp: '9:15 AM',
                isOwn: true,
                type: 'wave',
                status: 'seen',
                deliveredAt: '9:15 AM',
                readAt: '9:16 AM'
            },
            {
                id: 'msg4',
                text: 'Happy Birthday, my bro',
                timestamp: '9:25 AM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg5',
                text: 'Thank you so much! 🎉',
                timestamp: '9:26 AM',
                isOwn: true,
                type: 'text',
                status: 'delivered',
                deliveredAt: '9:26 AM'
            }
        ]
    },
    {
        id: '2',
        name: 'Jay Golden',
        message: 'Oh my god. What are you doing?',
        time: '9:25 AM',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        unreadCount: 1,
        isOnline: false,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'Did you see the news today?',
                timestamp: '8:30 AM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'No, what happened?',
                timestamp: '8:45 AM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            },
            {
                id: 'msg3',
                text: 'Oh my god. What are you doing?',
                timestamp: '9:25 AM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '3',
        name: 'Sarah Connor',
        message: 'I\'ll be back.',
        time: '10:15 AM',
        avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 0,
        isOnline: true,
        isRead: true,
        messages: [
            {
                id: 'msg1',
                text: 'Meeting in 10 minutes',
                timestamp: '9:50 AM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'On my way!',
                timestamp: '9:51 AM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '9:51 AM',
                readAt: '9:52 AM'
            },
            {
                id: 'msg3',
                text: 'I\'ll be back.',
                timestamp: '10:15 AM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '4',
        name: 'John Doe',
        message: 'Let\'s catch up soon!',
        time: '11:00 AM',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 3,
        isOnline: false,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'It\'s been a while since we talked',
                timestamp: '10:30 AM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'Yes! How have you been?',
                timestamp: '10:45 AM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            },
            {
                id: 'msg3',
                text: 'Great! Working on some exciting projects',
                timestamp: '10:50 AM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg4',
                text: 'Let\'s catch up soon!',
                timestamp: '11:00 AM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '5',
        name: 'Jane Smith',
        message: 'Can you send me the files?',
        time: '12:30 PM',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 0,
        isOnline: true,
        isRead: true,
        messages: [
            {
                id: 'msg1',
                text: 'Hi! Hope you\'re having a good day',
                timestamp: '12:00 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'Thanks! You too 😊',
                timestamp: '12:15 PM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '12:15 PM',
                readAt: '12:16 PM'
            },
            {
                id: 'msg3',
                text: 'Can you send me the files?',
                timestamp: '12:30 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg4',
                text: 'Sure! Sending them now',
                timestamp: '12:31 PM',
                isOwn: true,
                type: 'text',
                status: 'delivered',
                deliveredAt: '12:31 PM'
            }
        ]
    },
    {
        id: '6',
        name: 'Michael Scott',
        message: 'That\'s what she said!',
        time: '1:45 PM',
        avatar: 'https://images.unsplash.com/photo-1511367461989-f85a21fda167?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 5,
        isOnline: false,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'This presentation is going to be hard to finish',
                timestamp: '1:30 PM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            },
            {
                id: 'msg2',
                text: 'That\'s what she said!',
                timestamp: '1:45 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg3',
                text: '😂😂😂',
                timestamp: '1:46 PM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            }
        ]
    },
    {
        id: '7',
        name: 'Dwight Schrute',
        message: 'Bears. Beets. Battlestar Galactica.',
        time: '2:10 PM',
        avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 0,
        isOnline: true,
        isRead: true,
        messages: [
            {
                id: 'msg1',
                text: 'What are the three main things to remember?',
                timestamp: '2:00 PM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '2:00 PM',
                readAt: '2:01 PM'
            },
            {
                id: 'msg2',
                text: 'Bears. Beets. Battlestar Galactica.',
                timestamp: '2:10 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg3',
                text: 'Classic Dwight! 🐻',
                timestamp: '2:11 PM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '2:11 PM',
                readAt: '2:12 PM'
            }
        ]
    },
    {
        id: '8',
        name: 'Pam Beesly',
        message: 'Let\'s grab lunch tomorrow.',
        time: '3:00 PM',
        avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 2,
        isOnline: false,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'How was your art class?',
                timestamp: '2:30 PM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            },
            {
                id: 'msg2',
                text: 'It was amazing! I learned watercolors',
                timestamp: '2:45 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg3',
                text: 'Let\'s grab lunch tomorrow.',
                timestamp: '3:00 PM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '9',
        name: 'Jim Halpert',
        message: 'Pranking Dwight again!',
        time: '4:20 PM',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 1,
        isOnline: true,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'You won\'t believe what I just did to Dwight',
                timestamp: '4:10 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'What did you do this time? 😄',
                timestamp: '4:15 PM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '4:15 PM',
                readAt: '4:16 PM'
            },
            {
                id: 'msg3',
                text: 'Pranking Dwight again!',
                timestamp: '4:20 PM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '10',
        name: 'Andy Bernard',
        message: 'I miss Cornell.',
        time: '5:15 PM',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 0,
        isOnline: false,
        isRead: true,
        messages: [
            {
                id: 'msg1',
                text: 'Did I ever tell you I went to Cornell?',
                timestamp: '5:00 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'Only a few hundred times 😅',
                timestamp: '5:10 PM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            },
            {
                id: 'msg3',
                text: 'I miss Cornell.',
                timestamp: '5:15 PM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '11',
        name: 'Angela Martin',
        message: 'The cat is fine.',
        time: '6:00 PM',
        avatar: 'https://images.unsplash.com/photo-1511367461989-f85a21fda167?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 4,
        isOnline: true,
        isRead: false,
        messages: [
            {
                id: 'msg1',
                text: 'How is Princess Lady doing?',
                timestamp: '5:45 PM',
                isOwn: true,
                type: 'text',
                status: 'seen',
                deliveredAt: '5:45 PM',
                readAt: '5:59 PM'
            },
            {
                id: 'msg2',
                text: 'The cat is fine.',
                timestamp: '6:00 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg3',
                text: 'She\'s been eating well and playing',
                timestamp: '6:01 PM',
                isOwn: false,
                type: 'text'
            }
        ]
    },
    {
        id: '12',
        name: 'Oscar Martinez',
        message: 'Let\'s discuss the budget.',
        time: '7:30 PM',
        avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0',
        unreadCount: 0,
        isOnline: false,
        isRead: true,
        messages: [
            {
                id: 'msg1',
                text: 'I\'ve reviewed the quarterly numbers',
                timestamp: '7:15 PM',
                isOwn: false,
                type: 'text'
            },
            {
                id: 'msg2',
                text: 'How do they look?',
                timestamp: '7:25 PM',
                isOwn: true,
                type: 'text',
                status: 'sent'
            },
            {
                id: 'msg3',
                text: 'Let\'s discuss the budget.',
                timestamp: '7:30 PM',
                isOwn: false,
                type: 'text'
            }
        ]
    }
];

// Legacy export for backward compatibility
const chatData = detailedChatData.map(item => ({
    id: item.id,
    name: item.name,
    message: item.message,
    time: item.time,
    avatar: item.avatar,
    unreadCount: item.unreadCount,
    isOnline: item.isOnline,
    isRead: item.isRead
}));

export { detailedChatData };
export default chatData;