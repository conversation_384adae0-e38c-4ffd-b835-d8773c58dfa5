# Animated Gradient System

A dynamic gradient background system that extracts colors from images and creates beautiful animated gradients with fallback to blue colors.

## Features

- 🎨 **Smart Color Extraction**: Intelligently determines colors based on image URLs and heuristics
- 🌈 **Dynamic Gradients**: Creates animated gradients based on extracted color themes
- 🔄 **Smooth Animations**: Multiple layers with different animation patterns
- 🔵 **Fallback Colors**: Falls back to blue color scheme if needed
- ⚡ **Performance Optimized**: Uses caching and React Native's native driver for smooth animations
- 🎛️ **Customizable**: Configurable height, border radius, and animation parameters
- 🚀 **Expo Compatible**: Works with Expo managed workflow without native modules

## Components

### `DynamicGradientBackground`

The main component that renders an animated gradient background.

```tsx
import DynamicGradientBackground from '@/pages/message/components/DynamicMessageBackground';

// Basic usage
<DynamicGradientBackground />

// With custom props
<DynamicGradientBackground 
  imageUri="https://example.com/image.jpg"
  height={400}
  borderRadius={15}
/>
```

**Props:**
- `imageUri?: string` - The image to extract colors from (defaults to header image)
- `height?: number` - Height of the gradient container (default: 500)
- `borderRadius?: number` - Border radius for the container (default: 20)

### `HeaderColorProvider`

Context provider for sharing extracted colors across components.

```tsx
import { HeaderColorProvider, useHeaderColors } from '@/contexts/HeaderColorContext';

// Wrap your app or screen
<HeaderColorProvider initialImageUri="https://example.com/image.jpg">
  <YourComponent />
</HeaderColorProvider>

// Use in components
const { colors, updateImageUri, isLoading } = useHeaderColors();
```

## Hooks

### `useAnimatedGradient`

Hook for creating animated gradient effects.

```tsx
import { useAnimatedGradient } from '@/hooks/useAnimatedGradient';

const { extractedColors, animatedValue, animatedValue2 } = useAnimatedGradient(imageUri);
```

## Utilities

### Color Extraction

```tsx
import { getCachedColors, extractColorsFromImage, addAlphaToColor, generateGradientColors } from '@/utils/colorExtraction';

// Extract colors from an image (cached automatically)
const colors = await getCachedColors('https://example.com/image.jpg');

// Direct extraction without caching
const colors = await extractColorsFromImage('https://example.com/image.jpg');

// Add alpha to a color
const transparentColor = addAlphaToColor('#627fff', 0.5);

// Generate gradient steps
const gradientColors = generateGradientColors('#627fff', 5);
```

## Animation Layers

The system uses three animated layers:

1. **Primary Layer**: Main gradient with primary and secondary colors
2. **Secondary Layer**: Complementary gradient with detail and background colors  
3. **Accent Layer**: Rotating circular gradient for subtle movement

Each layer has different:
- Animation speeds (4s, 6s)
- Transform properties (translate, scale, rotate)
- Opacity variations
- Color interpolations

## Dependencies

- `expo-linear-gradient` - For gradient rendering
- `react-native-reanimated` - For smooth animations

## Color Extraction Method

This system uses a smart color extraction method that works with Expo managed workflow:

1. **URL-based hashing** - Consistently maps image URLs to color palettes
2. **Keyword detection** - Analyzes URLs for color-related keywords (nature, ocean, sunset, etc.)
3. **Predefined palettes** - Uses carefully crafted color schemes (blue, green, purple, orange, pink, teal)
4. **Brightness variations** - Adds subtle variations to make each image feel unique
5. **Caching** - Caches extracted colors to improve performance

## Installation

No additional dependencies required! Works out of the box with Expo.

## Usage Examples

### Basic Message Background

```tsx
<View style={{ flex: 1 }}>
  <DynamicGradientBackground />
  <YourContent />
</View>
```

### With Custom Image

```tsx
<DynamicGradientBackground 
  imageUri="https://your-image-url.com/image.jpg"
  height={300}
  borderRadius={25}
/>
```

### Using Context for Shared Colors

```tsx
// App level
<HeaderColorProvider>
  <NavigationContainer>
    <YourApp />
  </NavigationContainer>
</HeaderColorProvider>

// In components
const MyComponent = () => {
  const { colors } = useHeaderColors();
  
  return (
    <View style={{ backgroundColor: colors.primary }}>
      <Text style={{ color: colors.background }}>
        Dynamic colored text!
      </Text>
    </View>
  );
};
```

## Performance Notes

- Color extraction is cached automatically using `getCachedColors()`
- Smart heuristics avoid heavy image processing
- Animations use native driver where possible
- Components are optimized for 60 FPS performance
- URL-based color mapping is instant after first calculation

## Fallback Behavior

The system gracefully handles various scenarios:
- Uses predefined color palettes based on URL analysis
- Falls back to blue color scheme from `@/utils/colors` if needed
- Logs warnings to console for debugging
- No visual errors or crashes
- Consistent colors for the same image URL
