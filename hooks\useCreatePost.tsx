import { MediaItem, ThemeData } from '@/types/media';
import { useState } from 'react';

export interface PostData {
  text: string;
  audience: string;
  exclusivity: string;
  selectedTheme: string | null;
  selectedThemeData: ThemeData | null;
  mediaItems: MediaItem[];
  backgroundImage?: string;
  showMediaPreview: boolean;
}

export const useCreatePost = () => {
  const [postData, setPostData] = useState<PostData>({
    text: '',
    audience: '1', // Public by default
    exclusivity: '1', // Normal by default
    selectedTheme: null,
    selectedThemeData: null,
    mediaItems: [],
    showMediaPreview: false,
  });

  const updateText = (text: string) => {
    setPostData(prev => ({ ...prev, text }));
  };

  const updateAudience = (audience: string) => {
    setPostData(prev => ({ ...prev, audience }));
  };

  const updateExclusivity = (exclusivity: string) => {
    setPostData(prev => ({ ...prev, exclusivity }));
  };

  const updateTheme = (themeId: string | null) => {
    let themeData: ThemeData | null = null;
    
    if (themeId) {
      // Define theme data based on themeId
      switch (themeId) {
        case 'sunset':
          themeData = {
            type: 'gradient',
            colors: ['#FF6B6B', '#FFE66D'],
          };
          break;
        case 'ocean':
          themeData = {
            type: 'gradient',
            colors: ['#4ECDC4', '#44A08D'],
          };
          break;
        case 'purple':
          themeData = {
            type: 'gradient',
            colors: ['#667eea', '#764ba2'],
          };
          break;
        case 'fire':
          themeData = {
            type: 'gradient',
            colors: ['#ff9a9e', '#fad0c4'],
          };
          break;
        case 'forest':
          themeData = {
            type: 'gradient',
            colors: ['#56ab2f', '#a8e6cf'],
          };
          break;
        case 'night':
          themeData = {
            type: 'gradient',
            colors: ['#2c3e50', '#3498db'],
          };
          break;
        case 'winter':
          themeData = {
            type: 'gradient',
            colors: ['#74b9ff', '#0984e3'],
          };
          break;
        case 'dashboard':
          themeData = {
            type: 'dashboard',
          };
          break;
        case 'nature':
          themeData = {
            type: 'image',
            source: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=500&auto=format&fit=crop',
          };
          break;
        case 'city':
          themeData = {
            type: 'gradient',
            colors: ['#ffffff', '#ffffff'],
          };
          break;
        default:
          themeData = null;
      }
    }
    
    setPostData(prev => ({ 
      ...prev, 
      selectedTheme: themeId,
      selectedThemeData: themeData,
      // Clear media items when a theme is selected (themes and media are mutually exclusive)
      mediaItems: themeData ? [] : prev.mediaItems,
      showMediaPreview: themeData ? false : prev.showMediaPreview,
    }));
  };

  const addMediaItem = (mediaItem: MediaItem) => {
    setPostData(prev => ({
      ...prev,
      mediaItems: [...prev.mediaItems, mediaItem],
      showMediaPreview: true,
    }));
  };

  const addMediaItems = (mediaItems: MediaItem[]) => {
    setPostData(prev => ({
      ...prev,
      mediaItems: [...prev.mediaItems, ...mediaItems],
      showMediaPreview: true,
    }));
  };

  const removeMediaItem = (index: number) => {
    setPostData(prev => {
      const newMediaItems = prev.mediaItems.filter((_, i) => i !== index);
      return {
        ...prev,
        mediaItems: newMediaItems,
        showMediaPreview: newMediaItems.length > 0,
      };
    });
  };

  const toggleMediaPreview = () => {
    setPostData(prev => ({
      ...prev,
      showMediaPreview: !prev.showMediaPreview,
    }));
  };

  const setBackgroundImage = (uri: string) => {
    setPostData(prev => ({ ...prev, backgroundImage: uri }));
  };

  const resetPost = () => {
    setPostData({
      text: '',
      audience: '1',
      exclusivity: '1',
      selectedTheme: null,
      selectedThemeData: null,
      mediaItems: [],
      showMediaPreview: false,
    });
  };

  const handleSubmit = async () => {
    // Validate post data
    if (!postData.text.trim()) {
      throw new Error('Post content is required');
    }

    // TODO: Implement API call to create post
    console.log('Submitting post:', postData);
    
    // Reset form after successful submission
    resetPost();
    
    return postData;
  };

  return {
    postData,
    updateText,
    updateAudience,
    updateExclusivity,
    updateTheme,
    addMediaItem,
    addMediaItems,
    removeMediaItem,
    setBackgroundImage,
    toggleMediaPreview,
    handleSubmit,
    resetPost,
  };
};
