import ImageAdvanced from '@/components/ui/ImageAdvanced';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { FlexLayouts, Spacing } from '@/utils/layoutStyles';
import { StyleSheet, Text, View, TouchableOpacity, Animated } from 'react-native';
import { useRef, useState } from 'react';

interface ChatThreadProps {
  item: {
    id: string;
    name: string;
    message: string;
    time: string;
    avatar: any;
    isOnline: boolean;
    isRead: boolean;
    unreadCount: number;
  };
  onPress?: (item: any) => void;
  onLongPress?: (item: any) => void;
  disabled?: boolean;
}

const ChatThread = ({ item, onPress, onLongPress, disabled = false }: ChatThreadProps) => {
  const scaleValue = useRef(new Animated.Value(1)).current;
  const [isPressed, setIsPressed] = useState(false);

  const handlePressIn = () => {
    if (disabled) return;
    setIsPressed(true);
    Animated.spring(scaleValue, {
      toValue: 0.98,
      useNativeDriver: true,
      tension: 300,
      friction: 20,
    }).start();
  };

  const handlePressOut = () => {
    if (disabled) return;
    setIsPressed(false);
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 20,
    }).start();
  };

  const handlePress = () => {
    if (disabled) return;
    onPress?.(item);
  };

  const handleLongPress = () => {
    if (disabled) return;
    onLongPress?.(item);
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      onLongPress={handleLongPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      delayLongPress={500}
      disabled={disabled}
      activeOpacity={1}
      style={[styles.touchableContainer, disabled && styles.disabledContainer]}
    >
      <Animated.View 
        style={[
          styles.chatContainer,
          { transform: [{ scale: scaleValue }] },
          isPressed && styles.pressedContainer,
          disabled && styles.disabledContent
        ]}
      >
        <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm, flex: 1 }]}>
          <View style={styles.avatarContainer}>
            <ImageAdvanced 
              source={item.avatar} 
              style={[styles.avatar, disabled && styles.disabledAvatar]} 
              contentFit="cover"
            >
              {item.isOnline && !disabled && <View style={styles.onlineCircle} />}
            </ImageAdvanced>
          </View>

          <View style={styles.messageContainer}>
            <Text 
              style={[
                styles.nameText, 
                item.isRead ? styles.readName : styles.unreadName,
                disabled && styles.disabledText
              ]}
              numberOfLines={1}
            >
              {item.name}
            </Text>
            <Text
              style={[
                styles.messageText, 
                item.isRead ? styles.readMessage : styles.unreadMessage,
                disabled && styles.disabledText
              ]}
              numberOfLines={2}
            >
              {item.message}
            </Text>
          </View>

          <View style={styles.rightContainer}>
            <Text 
              style={[
                styles.timeText, 
                item.isRead ? styles.readTime : styles.unreadTime,
                disabled && styles.disabledText
              ]}
            >
              {item.time}
            </Text>

            {item.unreadCount > 0 && !disabled && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadBadgeText}>
                  {item.unreadCount > 99 ? '99+' : item.unreadCount}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Ripple effect overlay */}
        {isPressed && !disabled && <View style={styles.rippleOverlay} />}
      </Animated.View>
    </TouchableOpacity>
  );
};

export default ChatThread;

const styles = StyleSheet.create({
  touchableContainer: {
    overflow: 'hidden',
  },
  disabledContainer: {
    opacity: 0.5,
  },
  chatContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    backgroundColor: 'transparent',
    position: 'relative',
  },
  pressedContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  disabledContent: {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  rippleOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  disabledAvatar: {
    opacity: 0.6,
  },
  onlineCircle: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 15,
    height: 15,
    borderRadius: 9999,
    backgroundColor: Colors.success,
    borderWidth: 2,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  messageContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 16,
    marginBottom: 2,
  },
  unreadName: {
    fontWeight: '600',
    color: '#000000',
    fontFamily: Fonts.roboto.bold,
  },
  readName: {
    fontWeight: '400',
    fontFamily: Fonts.roboto.medium,
    color: '#333333',
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: Fonts.roboto.regular,
  },
  unreadMessage: {
    color: '#666666',
    fontWeight: '400',
  },
  readMessage: {
    color: '#999999',
    fontWeight: '300',
  },
  disabledText: {
    color: '#CCCCCC',
  },
  rightContainer: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginLeft: 'auto',
    minHeight: 48,
  },
  timeText: {
    fontSize: 12,
    marginBottom: 4,
  },
  unreadTime: {
    color: '#666666',
    fontWeight: '500',
  },
  readTime: {
    color: '#999999',
    fontWeight: '400',
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    shadowColor: '#FF3B30',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 3.84,
    elevation: 5,
  },
  unreadBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});