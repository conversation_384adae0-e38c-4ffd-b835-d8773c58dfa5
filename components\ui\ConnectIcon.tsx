import Svg, { Path } from 'react-native-svg';
import { CustomSvgProps } from '../../types/svgTypes';

const ConnectIcon = ({ size = 38, color = '#99A1BE', ...props }: CustomSvgProps) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 38 37"
    fill="none"
    {...props}
  >
    <Path
      fill={color}
      d="M18.8.085C5.34.085.56 4.864.56 18.325c0 13.473 4.779 18.24 18.24 18.24 13.473 0 18.24-4.767 18.24-18.24C37.04 4.864 32.273.085 18.8.085ZM14.921 24.49a6.16 6.16 0 0 1 0-12.318.91.91 0 0 1 .912.912c0 .51-.401.912-.912.912a4.339 4.339 0 0 0-4.329 4.329c0 2.395 1.946 4.341 4.329 4.341a4.344 4.344 0 0 0 4.341-4.341.91.91 0 1 1 1.824 0 6.165 6.165 0 0 1-6.165 6.165Zm7.527.219a.91.91 0 0 1-.912-.912c0-.499.413-.912.912-.912a4.562 4.562 0 0 0 4.56-4.56 4.57 4.57 0 0 0-4.56-4.56 4.57 4.57 0 0 0-4.56 4.56c0 .51-.401.912-.912.912a.91.91 0 0 1-.912-.912c0-3.514 2.87-6.384 6.384-6.384a6.383 6.383 0 1 1 0 12.768Z"
    />
  </Svg>
);

export default ConnectIcon;
