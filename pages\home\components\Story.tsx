import StoryData from '@/store/data/StoryData';
import { useCallback, useState } from 'react';
import React from 'react';
import { FlatList, View } from 'react-native';
import StoryItem from './StoryItem';

const createStoryData = {
  id: '0',
  name: 'Create your story',
  image:
    'https://images.unsplash.com/photo-1469854523086-cc02fe5d8800?q=80&w=821&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  isSeen: false,
};

const Story = () => {
  const [storyData, setStoryData] = useState(StoryData);

  const handleStoryPress = (storyId: string) => {
    if (storyId === '0') {
      return;
    }

    setStoryData((prevData) =>
      prevData.map((story) => (story.id === storyId ? { ...story, isSeen: true } : story))
    );
  };

  const renderStoryItem = ({ item, index }: { item: any; index: number }) => {
    const isCreateStory = index === 0;
    return (
      <StoryItem
        item={item}
        isCreateStory={isCreateStory}
        onPress={() => handleStoryPress(item.id)}
      />
    );
  };

  const combinedData = [createStoryData, ...storyData];

  return (
    <View style={{ height: 192 }}>
      <FlatList
        data={combinedData}
        renderItem={renderStoryItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingLeft: 16, paddingRight: 16 }}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        windowSize={10}
        initialNumToRender={3}
      />
    </View>
  );
};

export default Story;
