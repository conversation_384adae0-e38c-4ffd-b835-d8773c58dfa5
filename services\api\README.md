# API Services

This folder contains all API integration services and HTTP client configurations.

## Purpose
- REST API client implementations
- GraphQL clients and queries
- HTTP interceptors for authentication and error handling
- API endpoint configurations
- Request/response transformers
- Network error handling and retry logic

## Examples
- `authApi.ts` - Authentication API calls
- `userApi.ts` - User management endpoints
- `httpClient.ts` - Configured HTTP client (Axios, Fetch, etc.)
- `apiTypes.ts` - API request/response type definitions
