import ImageAdvanced from '@/components/ui/ImageAdvanced';
import { Colors } from '@/utils/colors';
import { calculateDynamicLayout } from '@/utils/layoutUtils';
import React from 'react';
import { View } from 'react-native';
import VideoItem from './VideoItem';

interface MediaItemProps {
  item: any;
  index: number;
  isVisible: boolean;
  onVideoPress: (index: number) => void;
  layoutDimensions: ReturnType<typeof calculateDynamicLayout>;
}

const MediaItem: React.FC<MediaItemProps> = React.memo(({ 
  item, 
  index, 
  isVisible,
  onVideoPress,
  layoutDimensions
}) => {
  const content = item.type === 'video' ? (
    <VideoItem
      mediaItem={item}
      itemIndex={index}
      isVisible={isVisible}
      onVideoPress={onVideoPress}
      layoutDimensions={layoutDimensions}
    />
  ) : (
    <ImageAdvanced
      source={item.url}
      style={{
        width: layoutDimensions.MEDIA_ITEM_SIZE,
        height: layoutDimensions.MEDIA_ITEM_SIZE,
        borderRadius: 12,
        backgroundColor: Colors.background.secondary,
      }}
      contentFit="cover"
      transition={100}
      showPlaceholder={true}
      placeholderText=""
      placeholderColor={Colors.background.secondary}
      recyclingKey={`image-${item.id || index}-${item.url?.slice(-10)}`}
      cachePolicy="memory"
      priority="normal"
    />
  );

  return (
    <View style={{ marginBottom: layoutDimensions.GRID_GAP }}>
      {content}
    </View>
  );
});

MediaItem.displayName = 'MediaItem';

export default MediaItem; 