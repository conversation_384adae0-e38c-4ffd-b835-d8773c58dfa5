import { CustomSvgProps } from '@/types/svgTypes';
import Svg, { SvgProps, Path } from 'react-native-svg';
const ChatHelpIcon = ({ size = 52, color = '#99A1BE', ...props }: CustomSvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 52 52" fill="none" {...props}>
    <Path
      fill={color}
      fillRule="evenodd"
      d="M35.992 29.079a2.445 2.445 0 0 1-2.448-2.436 2.425 2.425 0 0 1 2.426-2.435h.022a2.435 2.435 0 1 1 0 4.871Zm-9.765 0a2.444 2.444 0 0 1-2.448-2.436 2.427 2.427 0 0 1 2.426-2.435h.022a2.435 2.435 0 1 1 0 4.871Zm-9.764 0a2.443 2.443 0 0 1-2.446-2.436 2.423 2.423 0 0 1 2.424-2.435h.022a2.435 2.435 0 1 1 0 4.871ZM44.458 7.548C39.633 2.723 33.21.063 26.376.063c-6.835 0-13.258 2.66-18.083 7.485-7.555 7.558-9.615 19.156-5.17 28.738.08.395-.137 2-.293 3.174-.59 4.398-.891 7.628.602 9.124 1.49 1.493 4.718 1.19 9.117.599 1.174-.156 2.798-.366 3.051-.339a25.488 25.488 0 0 0 10.72 2.353c6.669 0 13.25-2.587 18.138-7.477 9.969-9.974 9.971-26.2 0-36.172Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default ChatHelpIcon;
