import {StyleSheet, Text, View} from "react-native";
import ViewContainer from "@/components/ui/ViewContainer";
import {Fonts} from "@/utils/fonts";
import Input from "@/components/authentication/Input";
import {Colors} from "@/utils/colors";
import ButtonPrimary from "@/components/ui/ButtonPrimary";
import { router, useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";

const VerificationPage = () => {
    const params = useLocalSearchParams();
    const [previousScreen, setPreviousScreen] = useState<string>("signup");

    useEffect(() => {
        // Check if we have a from parameter that tells us where we came from
        if (params.from === "account-recovery") {
            setPreviousScreen("account-recovery");
        } else {
            setPreviousScreen("signup");
        }
    }, [params]);

    const handleContinue = () => {
        if (previousScreen === "account-recovery") {
            router.push("/recreate-password");
        } else {
            router.push("/create-profile");
        }
    };

    return (
        <View className={"flex-1 bg-background-primary"}>
            <ViewContainer classNameCustom={"h-1/4 items-center justify-end gap-2"}>
                <Text style={styles.title}>Enter Verification Code</Text>
                <View className={"flex-row items-center justify-center gap-1"}>
                    <Text className={"text-[12px] font-roboto-regular text-text-muted"}>We have sent a verification code to</Text>
                    <Text className={"text-[12px] font-roboto-bold text-text-muted"}>+84 123*******</Text>
                </View>
            </ViewContainer>
            <ViewContainer classNameCustom={"items-start justify-start pt-3 gap-4"}>
                <Input
                    enableShadow={false}
                    backgroundColor={Colors.background.muted}
                    placeholder="Enter your sent verification code"
                    placeholderTextColor={Colors.text.muted}
                    inputType={"number"}
                >
                </Input>
                <Text className={"text-left text-[12px] font-roboto-regular text-text-muted"}>Wait to send again (50s)</Text>
            </ViewContainer>
            <ViewContainer classNameCustom={"items-center justify-end h-32"}>
                <ButtonPrimary
                    bgColor={Colors.primary[500]}
                    border={"0px solid"}
                    borderColor="transparent"
                    onPress={handleContinue}
                >
                    <Text className={"text-white text-[16px]"}>Continue</Text>
                </ButtonPrimary>
            </ViewContainer>
            <ViewContainer classNameCustom={"flex-1 items-center justify-end"}>
                <ButtonPrimary
                    bgColor={Colors.background.primary}
                    border="1px solid"
                    borderColor={Colors.text.muted}
                    enableShadow={false}
                    onPress={() => router.back()}
                >
                    <Text className={"text-black text-[16px]"}>Back</Text>
                </ButtonPrimary>
            </ViewContainer>
        </View>
    );

}

export default VerificationPage;

const styles = StyleSheet.create({
    title: {
        fontSize: 26,
        fontFamily: Fonts.roboto.regular,
        color: "black",
        textAlign: "center",
    }
})
