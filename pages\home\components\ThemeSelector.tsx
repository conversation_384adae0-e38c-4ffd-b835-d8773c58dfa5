import DashboardIcon from '@/components/ui/DashboardIcon';
import ImageAdvanced from '@/components/ui/ImageAdvanced';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface ThemeItemProps {
  type: 'image' | 'gradient' | 'dashboard';
  source?: string;
  colors?: [string, string, ...string[]];
  onPress?: () => void;
  isSelected?: boolean;
  isFirst?: boolean;
}

const ThemeItem: React.FC<ThemeItemProps> = ({ type, source, colors, onPress, isSelected, isFirst }) => {
  const renderContent = () => {
    switch (type) {
      case 'image':
        return (
          <ImageAdvanced source={source || ''} style={styles.themeItem} />
        );
      case 'gradient':
        return (
          <LinearGradient
            colors={colors || ['#3b82f6', '#E20071']}
            start={{ x: 1, y: 1 }}
            end={{ x: 0, y: 0 }}
            style={styles.themeItem}
          />
        );
      case 'dashboard':
        return (
          <ImageAdvanced source={source || ''} style={styles.themeItem}>
            <View style={styles.overlay}>
              <DashboardIcon color="white" />
            </View>
          </ImageAdvanced>
        );
      default:
        return null;
    }
  };

  return (
    <TouchableOpacity 
      onPress={isFirst ? undefined : onPress} 
      activeOpacity={isFirst ? 1 : 0.7}
      style={[
        styles.themeItemContainer,
        isSelected && !isFirst && styles.selectedBorder
      ]}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

interface ThemeSelectorProps {
  onThemeSelect?: (theme: string | null) => void;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({ onThemeSelect }) => {
  const [selectedTheme, setSelectedTheme] = useState<string | null>(null);
  
  const defaultImageUri = 
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';

  const themes = [
    { id: 'dashboard', type: 'dashboard' as const, source: defaultImageUri },
    { 
      id: 'sunset', 
      type: 'gradient' as const, 
      colors: ['#FF6B6B', '#FFE66D'] as [string, string, ...string[]]
    },
    { 
      id: 'ocean', 
      type: 'gradient' as const, 
      colors: ['#4ECDC4', '#44A08D'] as [string, string, ...string[]]
    },
    { 
      id: 'purple', 
      type: 'gradient' as const, 
      colors: ['#667eea', '#764ba2'] as [string, string, ...string[]]
    },
    { 
      id: 'fire', 
      type: 'gradient' as const, 
      colors: ['#ff9a9e', '#fad0c4'] as [string, string, ...string[]]
    },
    { 
      id: 'forest', 
      type: 'gradient' as const, 
      colors: ['#56ab2f', '#a8e6cf'] as [string, string, ...string[]]
    },
    { 
      id: 'night', 
      type: 'gradient' as const, 
      colors: ['#2c3e50', '#3498db'] as [string, string, ...string[]]
    },
    { 
      id: 'winter', 
      type: 'gradient' as const, 
      colors: ['#74b9ff', '#0984e3'] as [string, string, ...string[]]
    },
    { 
      id: 'nature', 
      type: 'image' as const, 
      source: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=500&auto=format&fit=crop'
    },
    { 
      id: 'city', 
      type: 'gradient' as const, 
      colors: ['#ffffff', '#ffffff'] as [string, string, ...string[]]
    },
  ];

  const handleThemePress = (themeId: string) => {
    // Toggle selection: if already selected, deselect it
    const newSelection = selectedTheme === themeId ? null : themeId;
    setSelectedTheme(newSelection);
    onThemeSelect?.(newSelection);
  };

  return (
    <ScrollView 
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.scrollContainer}
      style={styles.container}
    >
      {themes.map((theme, index) => (
        <ThemeItem
          key={theme.id}
          type={theme.type}
          source={theme.source}
          colors={theme.colors}
          isSelected={selectedTheme === theme.id}
          isFirst={index === 0}
          onPress={() => handleThemePress(theme.id)}
        />
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    height: screenWidth < 350 ? 38 : 44, // Smaller height on small screens
  },
  scrollContainer: {
    flexDirection: 'row',
    gap: screenWidth < 350 ? 6 : 8, // Smaller gap on small screens
    paddingHorizontal: 4,
    alignItems: 'center',
  },
  themeItemContainer: {
    borderRadius: screenWidth < 350 ? 10 : 12, // Slightly smaller radius on small screens
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedBorder: {
    borderColor: 'black',
    shadowColor: 'rgba(255, 255, 255, 0.5)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 8,
  },
  themeItem: {
    width: screenWidth < 350 ? 34 : screenWidth > 500 ? 48 : 40, // Responsive width
    height: screenWidth < 350 ? 34 : screenWidth > 500 ? 48 : 40, // Responsive height
    borderRadius: screenWidth < 350 ? 8 : screenWidth > 500 ? 12 : 10, // Responsive radius
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: screenWidth < 350 ? 8 : screenWidth > 500 ? 12 : 10, // Match theme item radius
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ThemeSelector;