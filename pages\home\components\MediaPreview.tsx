import ImageAdvanced from '@/components/ui/ImageAdvanced';
import VideoIcon from '@/components/ui/VideoIcon';
import { MediaItem } from '@/types/media';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity } from 'react-native';
import Animated, { FadeOut, Layout } from 'react-native-reanimated';

interface MediaPreviewProps {
  mediaItems: MediaItem[];
  visible?: boolean;
  onRemoveItem?: (index: number) => void;
}

const MediaPreview: React.FC<MediaPreviewProps> = ({
  mediaItems,
  visible = true,
  onRemoveItem,
}) => {
  if (!visible || mediaItems.length === 0) return null;

  const formatDuration = (d?: number) =>
    !d
      ? ''
      : `${Math.floor(d / 60000)}:${Math.floor((d % 60000) / 1000)
          .toString()
          .padStart(2, '0')}`;

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.scrollContainer}
      style={styles.scrollViewContainer}
    >
      {mediaItems.map((item, index) => (
        <Animated.View
          key={item.uri /* make sure it’s unique */}
          layout={Layout.springify()}
          exiting={FadeOut.duration(200)}
          style={styles.mediaContainer}
        >
          <ImageAdvanced
            source={item.uri}
            style={styles.mediaItem}
            contentFit="cover"
            showPlaceholder
            placeholderText={
              item.type === 'video' ? 'Loading video...' : 'Loading image...'
            }
          />

          {item.type === 'video' && (
            <Animated.View style={styles.videoOverlay}>
              <Animated.View style={styles.playIconContainer}>
                <VideoIcon size={24} color="rgba(255,255,255,0.9)" />
              </Animated.View>
              {item.duration != null && (
                <Animated.View style={styles.durationContainer}>
                  <Text style={styles.durationText}>
                    {formatDuration(item.duration)}
                  </Text>
                </Animated.View>
              )}
            </Animated.View>
          )}

          {onRemoveItem && (
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => onRemoveItem(index)}
              activeOpacity={0.7}
            >
              <Ionicons
                name="close-circle"
                size={24}
                color="rgba(255,255,255,0.9)"
              />
            </TouchableOpacity>
          )}
        </Animated.View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollViewContainer: { flex: 1 },
  scrollContainer: {
    gap: 10,
    paddingVertical: 8,
    alignItems: 'flex-start',
    minHeight: 184,
  },
  mediaContainer: { position: 'relative' },
  mediaItem: { width: 115, height: 180, borderRadius: 12 },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  playIconContainer: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 8,
  },
  durationContainer: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  durationText: { color: 'white', fontSize: 12, fontWeight: '600' },
  removeButton: {
    position: 'absolute',
    top: 6,
    right: 6,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
  },
});

export default MediaPreview;
