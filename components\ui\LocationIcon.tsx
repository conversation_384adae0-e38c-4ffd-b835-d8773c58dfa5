import Svg, { Path } from 'react-native-svg';
import { CustomSvgProps } from '../../types/svgTypes';

const LocationIcon = ({ size = 39, color = '#99A1BE', ...props }: CustomSvgProps) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 39 41"
    fill="none"
    {...props}
  >
    <Path
      fill={color}
      fillRule="evenodd"
      d="M25.325 29.526H14.277a1.426 1.426 0 0 1 0-2.85h11.048a1.426 1.426 0 0 1 0 2.85Zm8.248-20.381c-.69-.608-1.474-1.296-2.409-2.16-.424-.342-.887-.734-1.38-1.148C27.012 3.498 23.212.295 19.748.295c-3.424 0-6.979 3.024-9.834 5.455-.529.448-1.023.87-1.526 1.278-.886.821-1.67 1.51-2.362 2.12C1.49 13.142.637 14.189.637 23.5c0 16.696 4.828 16.696 19.163 16.696 14.334 0 19.164 0 19.164-16.696 0-9.313-.854-10.36-5.39-14.354Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default LocationIcon;
