import { useCreatePost } from '@/hooks/useCreatePost';
import { getThemeBackgroundColor, getThemeBackgroundColorSync } from '@/utils/colors';
import { useEffect, useState } from 'react';
import { Alert, Dimensions, ScrollView, StyleSheet, View } from 'react-native';

// Components
import CreatePostHeader from './components/CreatePostHeader';
import MediaPreview from './components/MediaPreview';
import MediaUtils from './components/MediaUtils';
import PostActions from './components/PostActions';
import PostBackground from './components/PostBackground';
import PostPrivacyControls from './components/PostPrivacyControls';
import PostTextInput from './components/PostTextInput';
import ThemeSelector from './components/ThemeSelector';

const { height: screenHeight } = Dimensions.get('window');

const CreatePostPage = () => {
  const {
    postData,
    updateText,
    updateAudience,
    updateExclusivity,
    updateTheme,
    addMediaItem,
    addMediaItems,
    removeMediaItem,
    toggleMediaPreview,
    handleSubmit,
    resetPost,
  } = useCreatePost();

  const [extractedBackgroundColor, setExtractedBackgroundColor] = useState<string | null>(null);

  // Extract background color when theme changes
  useEffect(() => {
    const extractColor = async () => {
      if (postData.selectedThemeData) {
        try {
          const color = await getThemeBackgroundColor(postData.selectedThemeData);
          setExtractedBackgroundColor(color);
        } catch (error) {
          // Fallback to sync version
          const fallbackColor = getThemeBackgroundColorSync(postData.selectedThemeData);
          setExtractedBackgroundColor(fallbackColor);
        }
      } else {
        setExtractedBackgroundColor(null);
      }
    };

    extractColor();
  }, [postData.selectedThemeData]);

  const handlePostSubmit = async () => {
    try {
      await handleSubmit();
      Alert.alert('Success', 'Post created successfully!');
      // Navigate back or reset form
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to create post');
    }
  };

  const handleMediaSelected = (mediaItems: any[]) => {
    addMediaItems(mediaItems);
  };

  const handleCamera = () => {
    // This will be handled by MediaUtils component now
    console.log('Camera pressed - handled by MediaUtils');
  };

  const handleMention = () => {
    // TODO: Implement mention functionality
    console.log('Mention pressed');
  };

  const handleVoice = () => {
    // TODO: Implement voice recording
    console.log('Voice pressed');
  };

  const handleEmoji = () => {
    // TODO: Implement emoji picker
    console.log('Emoji pressed');
  };

  const handleCheckIn = () => {
    // TODO: Implement check-in functionality
    console.log('Check-in pressed');
  };

  const handleMusic = () => {
    // TODO: Implement music selection
    console.log('Music pressed');
  };

  const handleLinks = () => {
    // TODO: Implement link attachment
    console.log('Links pressed');
  };

  const hasMedia = postData.mediaItems.length > 0 && postData.showMediaPreview;

  return (
    <View style={styles.container}>
      <CreatePostHeader onSubmit={handlePostSubmit} />

      <PostPrivacyControls
        onAudienceChange={updateAudience}
        onExclusivityChange={updateExclusivity}
      />

      <View style={styles.contentContainer}>
        <ScrollView 
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <PostBackground selectedTheme={postData.selectedThemeData}>
            <PostTextInput 
              value={postData.text} 
              onChangeText={updateText}
              backgroundColor={extractedBackgroundColor || undefined}
              hasThemeBackground={!!postData.selectedThemeData}
            />
          </PostBackground>
        </ScrollView>

        {hasMedia && (
          <View style={styles.mediaContainer}>
            <MediaPreview
              mediaItems={postData.mediaItems}
              visible={postData.showMediaPreview}
              onRemoveItem={removeMediaItem}
            />
          </View>
        )}
      </View>

      <View style={styles.bottomSection}>
        <ThemeSelector onThemeSelect={updateTheme} />

        <PostActions onCheckIn={handleCheckIn} onMusic={handleMusic} onLinks={handleLinks} />

        <MediaUtils
          onCamera={handleCamera}
          onMention={handleMention}
          onVoice={handleVoice}
          onEmoji={handleEmoji}
          onMediaSelected={handleMediaSelected}
          disableMediaUpload={!!postData.selectedThemeData}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    minHeight: Math.max(300, screenHeight * 0.4),
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    minHeight: 150,
  },
  mediaContainer: {
    paddingTop: 8,
    height: 200,
  },
  bottomSection: {
    gap: 8,
    padding: 16,
    backgroundColor: 'white',
    minHeight: 140,
  },
});

export default CreatePostPage;