import * as React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

interface CareerIconProps extends SvgProps {
  width?: number;
  height?: number;
  color?: string;
}

const CareerIcon = ({ width = 38, height = 40, color = '#99A1BE', ...props }: CareerIconProps) => (
  <Svg width={width} height={height} viewBox="0 0 38 40" fill="none" {...props}>
    <Path
      fill="#99A1BE"
      fillRule="evenodd"
      d="M14.031 6.604c0-1.614 1.118-2.925 2.49-2.925h4.56c1.371 0 2.488 1.311 2.488 2.925v.045a47.826 47.826 0 0 0-4.769-.218c-1.73 0-3.315.072-4.769.218v-.045ZM.761 17.85c4.958 3.055 11.38 4.74 18.077 4.746 6.685-.006 13.09-1.685 18.043-4.727-1.003-6.2-4.167-9.498-10.462-10.78v-.485c0-3.185-2.396-5.775-5.339-5.775h-4.56c-2.943 0-5.339 2.59-5.339 5.775v.484C4.896 8.368 1.735 11.658.724 17.834c.***************.038.016Z"
      clipRule="evenodd"
    />
    <Path
      fill="#99A1BE"
      fillRule="evenodd"
      d="M20.225 25.518v4.11a1.426 1.426 0 0 1-2.85 0v-4.112c-6.196-.207-12.12-1.754-16.978-4.43-.029.605-.046 1.229-.046 1.878 0 12.21 4.828 16.536 18.45 16.536 13.62 0 18.448-4.326 18.448-16.536 0-.634-.015-1.244-.042-1.835-4.866 2.662-10.79 4.193-16.982 4.389Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default CareerIcon;
