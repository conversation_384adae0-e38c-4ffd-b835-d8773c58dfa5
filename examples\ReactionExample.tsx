import ReactLikeButton from '@/components/ui/ReactLikeButton';
import { ReactionType } from '@/components/ui/ReactionPicker';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

const ReactionExample: React.FC = () => {
  // Example reaction types - you can easily add more
  const reactions: ReactionType[] = [
    {
      name: 'Like',
      id: 'like',
      // No source for default like - will show LikeIcon
    },
    {
      name: 'Heart',
      source: require('../../assets/images/icons/Heart.png'),
      id: 'heart',
    },
    {
      name: 'Laugh',
      source: require('../../assets/images/icons/Warm.png'),
      id: 'laugh',
    },
    {
      name: 'Sad',
      id: 'sad',
      emoji: '😢',
    },
    {
      name: 'Angry',
      id: 'angry',
      emoji: '😡',
    },
    // You can easily add more reactions here:
    // {
    //   name: 'Love',
    //   id: 'love',
    //   emoji: '😍',
    // },
    // {
    //   name: 'Wow',
    //   id: 'wow',
    //   emoji: '😮',
    // },
  ];

  const handleReactionChange = (postId: number, reactionType: string | null) => {
    console.log(`Post ${postId} reaction changed to:`, reactionType);
    // Here you can:
    // 1. Update local state
    // 2. Call API to save reaction
    // 3. Update UI accordingly
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Reaction Feature Demo</Text>
      <Text style={styles.subtitle}>
        - Single tap: Like/Unlike with default reaction
        - Long press: Show reaction picker (Facebook-style)
        - Select reaction: Replace current reaction or unlike if same
        - Only shows reaction asset if not default like
      </Text>
      
      <View style={styles.demoArea}>
        <Text style={styles.demoText}>Try the reaction button:</Text>
        <ReactLikeButton
          postId={1}
          reactions={reactions}
          onReactionChange={handleReactionChange}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  demoArea: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
  },
  demoText: {
    fontSize: 16,
    marginBottom: 15,
  },
});

export default ReactionExample;
