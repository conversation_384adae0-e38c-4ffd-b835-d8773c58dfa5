import CareerIcon from '@/components/ui/CareerIcon';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/utils/colors';
import * as WebBrowser from 'expo-web-browser';
import LocationIcon from '@/components/ui/LocationIcon';
import ConnectIcon from '@/components/ui/ConnectIcon';
import InfoIcon from '@/components/ui/InfoIcon';

interface SocialLink {
  url: string;
  platform: string;
  username: string;
}

interface InformationProps {
  career?: string;
  location?: string;
  education?: string;
  socialLinks?: SocialLink[];
  otherInfo?: string[];
}

const Information = ({ career, location, education, socialLinks, otherInfo }: InformationProps) => {
  const handleLinkPress = async (url: string) => {
    try {
      await WebBrowser.openBrowserAsync(url);
    } catch (error) {
      console.error('Failed to open URL:', error);
    }
  };

  const getIconForField = (field: string) => {
    switch (field) {
      case 'career':
        return <CareerIcon width={16} />;
      case 'location':
        return <LocationIcon width={16} />;
      case 'socialLinks':
        return <ConnectIcon width={16} />;
      default:
        return <InfoIcon width={16} />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    return <ConnectIcon width={16} />;
  };

  const InfoItem = ({ 
    icon, 
    children, 
    isLast = false 
  }: { 
    icon: React.ReactNode; 
    children: React.ReactNode; 
    isLast?: boolean;
  }) => (
    <View className={`flex-row items-center gap-3 ${!isLast ? 'mb-3' : ''}`}>
      <View className="items-center justify-center" style={{ width: 16, height: 16 }}>
        {icon}
      </View>
      <View className="flex-1">
        {children}
      </View>
    </View>
  );

  const items = [
    career && (
      <InfoItem key="career" icon={getIconForField('career')}>
        <Text className="font-roboto-regular text-sm text-text-muted leading-5">
          {career}
        </Text>
      </InfoItem>
    ),
    location && (
      <InfoItem key="location" icon={getIconForField('location')}>
        <Text className="font-roboto-regular text-sm text-text-muted leading-5">
          Lives in{' '}
          <Text className="font-roboto-bold text-text-muted2">
            {location}
          </Text>
        </Text>
      </InfoItem>
    ),
    education && (
      <InfoItem key="education" icon={getIconForField('education')}>
        <Text className="font-roboto-regular text-sm text-text-muted leading-5">
          Studied at{' '}
          <Text className="font-roboto-bold text-text-muted2">
            {education}
          </Text>
        </Text>
      </InfoItem>
    ),
    socialLinks && socialLinks.length > 0 && (
      <View key="social-links" className="space-y-2">
        {socialLinks.map((link, index) => (
          <InfoItem 
            key={`social-${index}`} 
            icon={getPlatformIcon(link.platform)}
            isLast={index === socialLinks.length - 1 && (!otherInfo || otherInfo.length === 0)}
          >
            <TouchableOpacity 
              onPress={() => handleLinkPress(link.url)}
              activeOpacity={0.7}
              className="flex-row items-center"
            >
              <Text className="font-roboto-regular text-sm text-blue-600 leading-5">
                {link.platform}
              </Text>
              <Text className="font-roboto-regular text-sm text-text-muted leading-5">
                /{link.username}
              </Text>
              <Ionicons 
                name="open-outline" 
                size={12} 
                color={Colors.text.muted} 
                style={{ marginLeft: 4 }}
              />
            </TouchableOpacity>
          </InfoItem>
        ))}
      </View>
    ),
    otherInfo && otherInfo.length > 0 && (
      <View key="other-info" className="space-y-2">
        {otherInfo.map((info, index) => (
          <InfoItem 
            key={`other-${index}`} 
            icon={getIconForField('other')}
            isLast={index === otherInfo.length - 1}
          >
            <Text className="font-roboto-regular text-sm text-text-muted leading-5">
              {info}
            </Text>
          </InfoItem>
        ))}
      </View>
    ),
  ].filter(Boolean);

  if (items.length === 0) {
    return (
      <View className="h-fit w-full py-4">
        <Text className="font-roboto-regular text-sm text-text-muted text-center">
          No information available
        </Text>
      </View>
    );
  }

  return (
    <View className="h-fit w-full py-2">
      <View className="space-y-1">
        {items.map((item, index) => (
          <View key={index}>
            {item}
          </View>
        ))}
      </View>
    </View>
  );
};

export default Information;