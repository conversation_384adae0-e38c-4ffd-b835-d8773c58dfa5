import { Image, StyleSheet, Text, View } from 'react-native';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';

const LogoTagLine = () => {
  return (
    <View className={'flex-1 items-center justify-center'}>
      <Image
        source={require('../../../assets/images/logo/Logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />
      <Text style={styles.mainLogoText}>WConnect</Text>
      <Text style={styles.logoText}>We Connect - World Connect</Text>
    </View>
  );
};

export default LogoTagLine;

const styles = StyleSheet.create({
  logo: {
    width: 128,
    height: 128,
  },
  logoText: {
    color: Colors.primary['950'],
    fontFamily: Fonts.lexend.regular,
  },
  mainLogoText: {
    color: Colors.primary['500'],
    fontSize: 24,
    fontFamily: Fonts.lexend.bold,
  },
});
