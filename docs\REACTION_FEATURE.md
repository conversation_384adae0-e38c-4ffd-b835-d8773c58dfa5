# Reaction Feature

A Facebook-style reaction system for posts with like button and reaction picker.

## Features

- **Single tap**: Like/Unlike with default reaction (no asset shown for default like)
- **Long press**: Shows reaction picker with available reactions
- **Reaction selection**: Replace current reaction or unlike if same reaction selected  
- **Visual feedback**: Shows reaction asset image for non-default reactions
- **Extensible**: Easy to add new reactions

## Usage

### Basic Implementation

```tsx
import ReactLikeButton from '@/components/ui/ReactLikeButton';
import { ReactionType } from '@/components/ui/ReactionPicker';

const reactions: ReactionType[] = [
  {
    name: 'Like',
    id: 'like',
    // No source for default like - will show LikeIcon
  },
  {
    name: 'Heart',
    source: require('../../assets/images/icons/Heart.png'),
    id: 'heart',
  },
  {
    name: 'Laugh', 
    source: require('../../assets/images/icons/Warm.png'),
    id: 'laugh',
  },
  {
    name: 'Sad',
    id: 'sad',
    emoji: '😢',
  },
  {
    name: 'Angry',
    id: 'angry', 
    emoji: '😡',
  },
];

<ReactLikeButton
  postId={post.id}
  initialReaction={post.userReaction} // string | null
  reactions={reactions}
  onReactionChange={(postId, reactionType) => {
    // Handle reaction change
    console.log(`Post ${postId} reaction:`, reactionType);
  }}
/>
```

### Adding New Reactions

To add new reactions, simply add them to the reactions array:

```tsx
const reactions: ReactionType[] = [
  // ... existing reactions
  {
    name: 'Love',
    id: 'love',
    emoji: '😍',
  },
  {
    name: 'Wow',
    id: 'wow', 
    emoji: '😮',
  },
  {
    name: 'Custom',
    id: 'custom',
    source: require('../../assets/images/icons/Custom.png'),
  },
];
```

## Components

### ReactLikeButton

Main component that handles like button with reaction picker.

**Props:**
- `postId: number` - Unique identifier for the post
- `initialReaction?: string | null` - Initial reaction state
- `reactions: ReactionType[]` - Array of available reactions
- `style?: any` - Custom styles
- `onReactionChange?: (postId: number, reactionType: string | null) => void` - Callback for reaction changes

### ReactionPicker

Popup component showing available reactions.

**Props:**
- `reactions: ReactionType[]` - Available reactions
- `onReactionSelect: (reaction: ReactionType) => void` - Reaction selection callback
- `onClose: () => void` - Close picker callback
- `visible: boolean` - Visibility state
- `position: { x: number; y: number }` - Position for picker

### useReactionManager

Hook for managing reaction state and interactions.

**Parameters:**
- `postId: number` - Post identifier
- `initialReaction?: string | null` - Initial reaction

**Returns:**
- `currentReaction: string | null` - Current reaction state
- `showReactionPicker: boolean` - Picker visibility
- `handleLikePress: (event) => void` - Like button press handler
- `handleLongPressStart: (event) => void` - Long press start handler
- `handleLongPressEnd: () => void` - Long press end handler
- `handleReactionSelect: (reaction) => void` - Reaction selection handler
- `getCurrentReactionAsset: (reactions) => ReactionType | null` - Get current reaction asset
- `isLiked: () => boolean` - Check if post is liked
- `isDefaultLike: () => boolean` - Check if using default like

## Types

### ReactionType

```tsx
interface ReactionType {
  id: string;          // Unique identifier
  name: string;        // Display name
  source?: any;        // Image asset (require() or URI)
  color?: string;      // Optional color
  emoji?: string;      // Emoji fallback
}
```

## Behavior

1. **Default Like**: Single tap sets 'like' reaction (no asset shown)
2. **Reaction Picker**: Long press (500ms) shows reaction picker
3. **Reaction Selection**: Tapping reaction replaces current or unlikes if same
4. **Visual State**: 
   - No reaction: Gray background, like icon
   - Default like: Blue background, white like icon
   - Other reactions: Blue background, reaction asset image

## Integration

Already integrated in:
- `pages/home/<USER>/Post.tsx`
- `pages/profile/components/Post.tsx`

The feature is designed to be easily extensible - you can add new reactions without changing any component code, just update the reactions array.
