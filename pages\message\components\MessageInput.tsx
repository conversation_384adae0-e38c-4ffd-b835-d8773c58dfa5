import { useMediaPicker } from "@/hooks/useMediaPicker";
import { MediaItem } from "@/types/media";
import { Colors } from "@/utils/colors";
import { Fonts } from "@/utils/fonts";
import { ContentContainers, FlexLayouts, Spacing } from "@/utils/layoutStyles";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Image, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";

interface MessageInputProps {
    inputText: string;
    onInputTextChange: (text: string) => void;
    onSendMessage: (text?: string, media?: MediaItem[]) => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
    inputText,
    onInputTextChange,
    onSendMessage
}) => {
    const { 
        selectedMedia, 
        isLoading, 
        showMediaOptions, 
        removeMediaItem, 
        clearAllMedia 
    } = useMediaPicker();

    const handleSendMessage = () => {
        const hasText = inputText.trim();
        const hasMedia = selectedMedia.length > 0;
        
        if (hasText || hasMedia) {
            onSendMessage(inputText, selectedMedia);
            clearAllMedia();
        }
    };

    return (
        <View style={[ContentContainers.viewContainer, styles.inputContainer]}>
            {/* Media Preview */}
            {selectedMedia.length > 0 && (
                <ScrollView 
                    horizontal 
                    style={styles.mediaPreviewContainer}
                    showsHorizontalScrollIndicator={false}
                >
                    {selectedMedia.map((media, index) => (
                        <View key={index} style={styles.mediaPreviewItem}>
                            <Image source={{ uri: media.uri }} style={styles.mediaPreview} />
                            <TouchableOpacity 
                                style={styles.removeMediaButton}
                                onPress={() => removeMediaItem(index)}
                            >
                                <Ionicons name="close" size={16} color={Colors.background.primary} />
                            </TouchableOpacity>
                            {media.type === 'video' && (
                                <View style={styles.videoIndicator}>
                                    <Ionicons name="play" size={20} color={Colors.background.primary} />
                                </View>
                            )}
                        </View>
                    ))}
                </ScrollView>
            )}

            <View style={styles.inputWrapper}>
                <TextInput
                    style={styles.input}
                    placeholder="Text message"
                    value={inputText}
                    onChangeText={onInputTextChange}
                    multiline
                    placeholderTextColor={Colors.text.muted}
                    onSubmitEditing={handleSendMessage}
                />
            </View>

            {/* Utility buttons */}
            <View style={[FlexLayouts.rowBetween, styles.utilityButtons]}>
                <View style={FlexLayouts.rowCenter}>
                    <TouchableOpacity style={[styles.utilityAddButton, styles.utilityButton]}>
                        <Ionicons name="add" size={24} color={Colors.secondary['950']} />
                    </TouchableOpacity>
                    <TouchableOpacity 
                        style={styles.utilityButton}
                        onPress={showMediaOptions}
                        disabled={isLoading}
                    >
                        <Ionicons 
                            name="image" 
                            size={20} 
                            color={isLoading ? Colors.text.muted : Colors.secondary['950']} 
                        />
                        <Text style={[styles.utilityButtonText, isLoading && styles.disabledText]}>
                            {isLoading ? 'Loading...' : 'Media'}
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.utilityButton}>
                        <Ionicons name="happy" size={20} color={Colors.secondary['950']} />
                        <Text style={styles.utilityButtonText}>Emoji</Text>
                    </TouchableOpacity>
                </View>

                <TouchableOpacity style={styles.micButton} onPress={handleSendMessage}>
                    <Ionicons 
                        name={(inputText.trim() || selectedMedia.length > 0) ? "send" : "mic"} 
                        size={32} 
                        color={Colors.background.primary} 
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default MessageInput;

const styles = StyleSheet.create({
    inputContainer: {
        backgroundColor: Colors.background.primary,
        borderTopWidth: 1,
        borderTopColor: Colors.background.muted,
    },
    mediaPreviewContainer: {
        paddingHorizontal: Spacing.horizontal.md,
        paddingTop: Spacing.vertical.sm,
        maxHeight: 100,
    },
    mediaPreviewItem: {
        position: 'relative',
        marginRight: Spacing.horizontal.sm,
    },
    mediaPreview: {
        width: 80,
        height: 80,
        borderRadius: 8,
        backgroundColor: Colors.background.muted,
    },
    removeMediaButton: {
        position: 'absolute',
        top: -8,
        right: -8,
        backgroundColor: Colors.error,
        borderRadius: 12,
        width: 24,
        height: 24,
        justifyContent: 'center',
        alignItems: 'center',
    },
    videoIndicator: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -10 }, { translateY: -10 }],
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderRadius: 15,
        width: 30,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
    },
    inputWrapper: {
        marginBottom: Spacing.vertical.sm,
    },
    input: {
        backgroundColor: Colors.background.muted,
        borderRadius: 25,
        paddingHorizontal: 16,
        paddingVertical: 12,
        fontSize: 16,
        maxHeight: 100,
        minHeight: 40,
        textAlignVertical: 'top',
        color: Colors.text.primary,
        fontFamily: Fonts.roboto.regular,
    },
    utilityButtons: {
        paddingHorizontal: Spacing.horizontal.xs,
    },
    utilityButton: {
        flexDirection: 'row',
        height: 40,
        alignItems: 'center',
        paddingHorizontal: Spacing.horizontal.sm,
        paddingVertical: Spacing.vertical.xs,
        marginRight: Spacing.horizontal.sm,
        backgroundColor: Colors.background.muted,
        borderRadius: 25,
    },
    utilityAddButton: {
        width: 40,
        backgroundColor: Colors.background.muted,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
    },
    utilityButtonText: {
        fontSize: 14,
        color: Colors.secondary['950'],
        marginLeft: 4,
        fontFamily: Fonts.roboto.medium,
        marginRight: 10,
    },
    disabledText: {
        color: Colors.text.muted,
    },
    micButton: {
        backgroundColor: Colors.secondary['950'],
        borderRadius: 25,
        width: 50,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },
});