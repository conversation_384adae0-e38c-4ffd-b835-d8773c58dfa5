import { ShimmerEffect } from '@/components';
import TopNavigationTitle from '@/components/ui/TopNavigationTitle';
import chatData from '@/store/data/ChatData';
import HeaderImage from '@/store/data/HeaderImage';
import { Colors } from '@/utils/colors';
import { FlexLayouts, MainContainers, SafeAreaContainers, Spacing } from '@/utils/layoutStyles';
import { shadows } from '@/utils/shadows';
import { memo, useCallback, useMemo, useState } from 'react';
import { FlatList, Pressable, StyleSheet, Text, TextInput, View } from 'react-native';
import ChatThread from './components/ChatThread';
import DynamicGradientBackground from './components/DynamicMessageBackground';

const Background = memo(() => <DynamicGradientBackground imageUri={HeaderImage.uri} />);
Background.displayName = 'Background';

const Header = memo(() => (
  <View style={SafeAreaContainers.topSafeMargin}>
    <TopNavigationTitle title="Search" isAdditionButton={false} />
  </View>
));
Header.displayName = 'Header';

interface ClearButtonProps {
  onPress: () => void;
  visible: boolean;
}

const ClearButton = memo(({ onPress, visible }: ClearButtonProps) => {
  if (!visible) return null;

  return (
    <Pressable onPress={onPress} style={styles.clearButton}>
      <Text style={styles.clearButtonText}>✕</Text>
    </Pressable>
  );
});
ClearButton.displayName = 'ClearButton';

interface SearchResultsHeaderProps {
  count: number;
  hasQuery: boolean;
}

const SearchResultsHeader = memo(({ count, hasQuery }: SearchResultsHeaderProps) => {
  if (!hasQuery) return null;

  return (
    <View style={styles.searchResultsHeader}>
      <Text style={styles.searchResultsText}>
        {count} result{count !== 1 ? 's' : ''} found
      </Text>
    </View>
  );
});
SearchResultsHeader.displayName = 'SearchResultsHeader';

interface NoResultsMessageProps {
  hasQuery: boolean;
  hasResults: boolean;
}

const NoResultsMessage = memo(({ hasQuery, hasResults }: NoResultsMessageProps) => {
  if (!hasQuery || hasResults) return null;

  return (
    <View style={styles.noResultsContainer}>
      <Text style={styles.noResultsText}>No messages found</Text>
      <Text style={styles.noResultsSubtext}>Try searching with different keywords</Text>
    </View>
  );
});
NoResultsMessage.displayName = 'NoResultsMessage';

interface SearchBarProps {
  isActive: boolean;
  searchQuery: string;
  onPress: () => void;
  onChangeText: (text: string) => void;
  onBlur: () => void;
  onClear: () => void;
}

const SearchBar = memo(
  ({ isActive, searchQuery, onPress, onChangeText, onBlur, onClear }: SearchBarProps) => {
    const handleClear = useCallback(() => {
      onClear();
    }, [onClear]);

    if (isActive) {
      return (
        <View style={styles.searchBarActive}>
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={onChangeText}
            placeholder="Search your message"
            placeholderTextColor={Colors.text.muted}
            autoFocus={true}
            onBlur={onBlur}
          />
          <ClearButton onPress={handleClear} visible={searchQuery.length > 0} />
        </View>
      );
    }

    return (
      <Pressable style={styles.searchBar} onPress={onPress}>
        <Text style={styles.searchPlaceholder}>
          {searchQuery || "Search your message"}
        </Text>
      </Pressable>
    );
  }
);
SearchBar.displayName = 'SearchBar';

const SearchMessagePage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredData, setFilteredData] = useState(chatData);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const shimmerConfig = {
    duration: 800,
    shimmerColors: [
      Colors.background.secondary,
      Colors.background.primary,
      Colors.background.secondary,
    ],
    variant: 'shimmer' as const,
    direction: 'leftToRight' as const,
  };

  const handleSearch = useCallback((text: any) => {
    setSearchQuery(text);
    setIsLoading(true);

    if (text.trim() === '') {
      setFilteredData(chatData);
    } else {
      const filtered = chatData.filter(
        (item) =>
          item.name.toLowerCase().includes(text.toLowerCase()) ||
          item.message.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredData(filtered);
    }
    setIsLoading(false);
  }, []);

  const handleSearchBarPress = useCallback(() => {
    setIsSearchActive(true);
  }, []);

  const handleSearchBlur = useCallback(() => {
    if (searchQuery.trim() === '') {
      setIsSearchActive(false);
    }
  }, [searchQuery]);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setFilteredData(chatData);
    setIsSearchActive(false);
    setIsLoading(false);
  }, []);

  const renderItem = useCallback(({ item }: any) => <ChatThread item={item} />, []);

  const renderShimmerItem = useCallback(({ index }: { index: number }) => (
    <View key={index} style={styles.shimmerChatContainer}>
      <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm, flex: 1 }]}>
        {/* Avatar shimmer */}
        <ShimmerEffect
          isLoading={isLoading}
          {...shimmerConfig}
          style={styles.shimmerAvatar}
        />

        {/* Message content shimmer */}
        <View style={styles.shimmerMessageContainer}>
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerName}
          />
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerMessage}
          />
        </View>

        {/* Time shimmer */}
        <View style={styles.shimmerRightContainer}>
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerTime}
          />
        </View>
      </View>
    </View>
  ), [isLoading, shimmerConfig]);

  const keyExtractor = useCallback((item: any) => item.id, []);

  const hasQuery = useMemo(() => searchQuery.trim() !== '', [searchQuery]);
  const hasResults = useMemo(() => filteredData.length > 0, [filteredData.length]);

  const ListHeaderComponent = useMemo(
    () => (
      <View>
        <View style={styles.searchContainer}>
          <SearchBar
            isActive={isSearchActive}
            searchQuery={searchQuery}
            onPress={handleSearchBarPress}
            onChangeText={handleSearch}
            onBlur={handleSearchBlur}
            onClear={clearSearch}
          />
        </View>

        <SearchResultsHeader count={filteredData.length} hasQuery={hasQuery} />

        <NoResultsMessage hasQuery={hasQuery} hasResults={hasResults} />
      </View>
    ),
    [
      isSearchActive,
      searchQuery,
      filteredData.length,
      hasQuery,
      hasResults,
      handleSearchBarPress,
      handleSearch,
      handleSearchBlur,
      clearSearch,
    ]
  );

  return (
    <View style={MainContainers.page}>
      <Background />
      <Header />

      <FlatList
        data={isLoading ? Array(6).fill(null) : filteredData}
        keyExtractor={isLoading ? (_, index) => `shimmer-${index}` : keyExtractor}
        renderItem={isLoading ? renderShimmerItem : renderItem}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={ListHeaderComponent}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={100}
        initialNumToRender={10}
        windowSize={10}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
      />
    </View>
  );
};

export default memo(SearchMessagePage);

const styles = StyleSheet.create({
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderColor: 'white',
  },
  onlineCircle: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.success,
    borderWidth: 1,
    borderColor: 'white',
  },
  searchContainer: {
    marginTop: 18,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  searchBar: {
    backgroundColor: Colors.background.primary,
    borderRadius: 9999,
    paddingHorizontal: 16,
    paddingVertical: 8,
    height: 53,
    ...FlexLayouts.rowStart,
    gap: Spacing.horizontal.sm,
    ...shadows.medium,
  },
  searchBarActive: {
    backgroundColor: Colors.background.primary,
    borderRadius: 9999,
    paddingHorizontal: 16,
    paddingVertical: 8,
    height: 53,
    ...FlexLayouts.rowStart,
    gap: Spacing.horizontal.sm,
    ...shadows.medium,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text.primary,
    padding: 0,
  },
  searchPlaceholder: {
    color: Colors.text.muted,
    textAlign: 'center',
    fontSize: 16,
  },
  clearButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    color: Colors.text.muted,
    fontSize: 16,
    fontWeight: 'bold',
  },
  searchResultsHeader: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  searchResultsText: {
    color: Colors.text.muted,
    fontSize: 14,
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  noResultsText: {
    color: Colors.text.primary,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  noResultsSubtext: {
    color: Colors.text.muted,
    fontSize: 14,
    textAlign: 'center',
  },
  // Shimmer styles
  shimmerChatContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background.primary,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    ...shadows.medium,
  },
  shimmerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    overflow: 'hidden',
  },
  shimmerMessageContainer: {
    flex: 1,
    gap: 8,
  },
  shimmerName: {
    height: 16,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    width: '60%',
  },
  shimmerMessage: {
    height: 14,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    width: '80%',
  },
  shimmerRightContainer: {
    alignItems: 'flex-end',
  },
  shimmerTime: {
    height: 12,
    width: 40,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
  },
});
