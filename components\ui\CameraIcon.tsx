import Svg, { Path, SvgProps } from "react-native-svg";

interface CustomSvgProps extends SvgProps {
  size?: number;
  color?: string;
}

const CameraIcon = ({
  size = 14,
  color = "#63C48C",
  ...props
}: CustomSvgProps) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 32 30"
    fill="none"
    {...props}
  >
    <Path
      fill={color}
      fillRule="evenodd"
      d="M23.679 12.029c-.68 0-1.238-.533-1.238-1.19 0-.657.543-1.19 1.223-1.19h.015c.68 0 1.23.533 1.23 1.19 0 .657-.55 1.19-1.23 1.19ZM15.6 22.569c-3.526 0-6.394-2.773-6.394-6.18 0-3.407 2.868-6.178 6.394-6.178 3.526 0 6.393 2.771 6.393 6.177 0 3.408-2.867 6.18-6.393 6.18Zm9.223-17.083a2.341 2.341 0 0 1-1.02-.62c-.142-.15-.332-.522-.516-.88-.496-.97-1.174-2.296-2.538-2.851C18.997.424 12.267.42 10.456 1.13 9.11 1.666 8.424 3 7.924 3.971c-.19.365-.383.743-.527.895a2.311 2.311 0 0 1-.994.614C.991 6.927 0 10.326 0 16.498 0 25.42 2.252 29.4 15.6 29.4c13.348 0 15.6-3.98 15.6-12.902 0-6.172-.99-9.571-6.376-11.012Z"
      clipRule="evenodd"
    />
    <Path
      fill={color}
      fillRule="evenodd"
      d="M15.601 12.59c-2.168 0-3.931 1.703-3.931 3.799s1.763 3.8 3.931 3.8 3.931-1.704 3.931-3.8-1.762-3.8-3.931-3.8Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default CameraIcon;
