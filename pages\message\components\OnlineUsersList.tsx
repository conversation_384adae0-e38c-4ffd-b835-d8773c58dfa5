import { ShimmerEffect } from '@/components';
import OnlineUsersData from '@/store/data/OnlineUsersData';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { useCallback, useState } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import OnlineUserItem from './OnlineUserItem';

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 16,
    fontFamily: Fonts.roboto.medium,
    color: Colors.text.primary,
    marginBottom: 12,
    marginHorizontal: 16,
  },
  // Shimmer styles
  shimmerUserContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 70,
  },
  shimmerUserAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.background.secondary,
    marginBottom: 8,
    overflow: 'hidden',
  },
  shimmerUserName: {
    height: 12,
    width: '80%',
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
  },
});

const OnlineUsersList = () => {
  const [isLoading, setIsLoading] = useState(false);

  const shimmerConfig = {
    duration: 800,
    shimmerColors: [
      Colors.background.secondary,
      Colors.background.primary,
      Colors.background.secondary,
    ],
    variant: 'shimmer' as const,
    direction: 'leftToRight' as const,
  };

  const handleUserPress = (userId?: string) => {
    if (userId === 'create') {
      console.log('Create story pressed');
      // Handle create story action
      return;
    }
    console.log('User pressed:', userId);
    // Handle user chat navigation
  };

  const renderUserItem = ({ item, index }: { item: any; index: number }) => {
    const isCreateStory = index === 0;
    return (
      <OnlineUserItem
        item={isCreateStory ? undefined : item}
        isCreateStory={isCreateStory}
        onPress={() => handleUserPress(isCreateStory ? 'create' : item.id)}
      />
    );
  };

  const renderShimmerUserItem = useCallback(({ index }: { index: number }) => (
    <View key={index} style={styles.shimmerUserContainer}>
      <ShimmerEffect
        isLoading={isLoading}
        {...shimmerConfig}
        style={styles.shimmerUserAvatar}
      />
      <ShimmerEffect
        isLoading={isLoading}
        {...shimmerConfig}
        style={styles.shimmerUserName}
      />
    </View>
  ), [isLoading, shimmerConfig]);

  // Add create story item at the beginning
  const combinedData = [{ id: 'create' }, ...OnlineUsersData];

  return (
    <View>
      <FlatList
        data={isLoading ? Array(6).fill(null) : combinedData}
        renderItem={isLoading ? renderShimmerUserItem : renderUserItem}
        keyExtractor={isLoading ? (_, index) => `shimmer-user-${index}` : (item, index) => (index === 0 ? 'create' : item.id)}
        horizontal
        showsHorizontalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        windowSize={10}
        initialNumToRender={4}
        style={{ paddingHorizontal: 16 }}
      />
    </View>
  );
};

export default OnlineUsersList;
