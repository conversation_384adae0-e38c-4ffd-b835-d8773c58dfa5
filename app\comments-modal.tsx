import OptimizedThreadedComments from '@/components/ui/ThreadedCommentsPopup';
import { useLocalSearchParams, useRouter } from 'expo-router';

export default function CommentsModalScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  
  // Parse the params
  const postId = params.postId ? parseInt(params.postId as string) : 0;
  const initialComments = params.initialComments ? JSON.parse(params.initialComments as string) : [];

  const handleClose = () => {
    router.back();
  };

  return (
    <OptimizedThreadedComments
      visible={true}
      onClose={handleClose}
      postId={postId}
      initialComments={initialComments}
    />
  );
}
