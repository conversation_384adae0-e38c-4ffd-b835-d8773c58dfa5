# App Folder

This folder contains the main application screens and routing configuration for the React Native app using Expo Router.

## Contents
- `_layout.tsx` - Root layout component that wraps all screens
- `index.tsx` - Home/landing screen of the application

## Purpose
The app folder follows Expo Router's file-based routing convention where each file represents a route in your application. This is the entry point for all your main application screens.
