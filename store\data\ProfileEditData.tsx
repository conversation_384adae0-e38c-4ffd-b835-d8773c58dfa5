import ProfileData from './ProfileData';

// Simplified structure for profile editing
export interface ProfileEditInfo {
    // Basic Information
    name: string;
    gender: string;
    birthday: string;
    hometown: string;
    bio: string;
    
    // Media
    avatar: string;
    coverImage: string;
    
    // Subscription
    monthlyPrice: string;
    yearlyPrice: string;
    
    // Details
    pronouns: string;
    career: string;
    companies: string[];
    education: string;
    relationship: string;
    
    // Social Links
    socialLinks: Array<{
        platform: string;
        url: string;
        username?: string;
    }>;
    
    // Privacy status for each field (0: private, 1: public, 2: friends)
    privacy: {
        [key: string]: number;
    };
}

// Helper function to get info value from ProfileData.info array
const getInfoValue = (fieldName: string): any => {
    if (!ProfileData?.info || !Array.isArray(ProfileData.info)) {
        return null;
    }
    
    const infoItem = ProfileData.info.find(item => item && item.hasOwnProperty(fieldName));
    return infoItem ? infoItem[fieldName as keyof typeof infoItem] : null;
};

// Helper function to get subscription data
const getSubscriptionValue = (fieldName: 'monthly' | 'yearly'): string => {
    if (!ProfileData?.info || !Array.isArray(ProfileData.info)) {
        return '';
    }
    
    const subscriptionItem = ProfileData.info.find(item => item && item.hasOwnProperty('subscription'));
    if (subscriptionItem && subscriptionItem.subscription) {
        const value = subscriptionItem.subscription[fieldName];
        return value === 'null' || value === null ? '' : String(value);
    }
    return '';
};

// Helper function to get privacy status
const getPrivacyStatus = (fieldName: string): number => {
    const infoItem = ProfileData?.info?.find(item => item && item.hasOwnProperty(fieldName));
    return infoItem?.status ?? 1; // Default to public
};

// Transform ProfileData into simplified structure
export const createProfileEditData = (): ProfileEditInfo => {
    return {
        // Basic Information
        name: ProfileData?.profile?.name || '',
        gender: getInfoValue('gender') || '',
        birthday: getInfoValue('birthday') || '',
        hometown: getInfoValue('hometown') || '',
        bio: getInfoValue('bio') || '',
        
        // Media
        avatar: ProfileData?.profile?.avatar || '',
        coverImage: ProfileData?.profile?.coverImage || '',
        
        // Subscription
        monthlyPrice: getSubscriptionValue('monthly'),
        yearlyPrice: getSubscriptionValue('yearly'),
        
        // Details
        pronouns: getInfoValue('pronouns') || '',
        career: getInfoValue('carriers') || '', // Note: using 'carriers' from original data
        companies: Array.isArray(getInfoValue('companies')) ? getInfoValue('companies') : [],
        education: getInfoValue('education') || '',
        relationship: Array.isArray(getInfoValue('relationship')) ? 
            getInfoValue('relationship').join(', ') : (getInfoValue('relationship') || ''),
        
        // Social Links
        socialLinks: ProfileData?.profile?.socialLinks || [],
        
        // Privacy settings
        privacy: {
            name: 1, // Name is always public
            gender: getPrivacyStatus('gender'),
            birthday: getPrivacyStatus('birthday'),
            hometown: getPrivacyStatus('hometown'),
            bio: getPrivacyStatus('bio'),
            pronouns: getPrivacyStatus('pronouns'),
            career: getPrivacyStatus('carriers'),
            companies: getPrivacyStatus('companies'),
            education: getPrivacyStatus('education'),
            relationship: getPrivacyStatus('relationship'),
        }
    };
};

// Export the transformed data
const ProfileEditData = createProfileEditData();
export default ProfileEditData;

// Field configuration for easy management
export const FIELD_CONFIG = {
    name: {
        label: 'Name',
        placeholder: 'Enter your name',
        guideText: 'Your name will be visible to other users on your profile and posts.',
        isTextArea: false,
        isDropdown: false,
        required: true
    },
    gender: {
        label: 'Gender',
        placeholder: 'Select your gender',
        guideText: 'This information helps personalize your experience and is optional.',
        isTextArea: false,
        isDropdown: true,
        options: [
            { label: 'Male', value: 'Male' },
            { label: 'Female', value: 'Female' },
            { label: 'Non-binary', value: 'Non-binary' },
            { label: 'Other', value: 'Other' },
            { label: 'Prefer not to say', value: 'Prefer not to say' }
        ]
    },
    birthday: {
        label: 'Birthday',
        placeholder: 'DD/MM/YYYY',
        guideText: 'Your birthday will help us celebrate with you and show relevant content.',
        isTextArea: false,
        isDropdown: false
    },
    hometown: {
        label: 'Current hometown',
        placeholder: 'Enter your hometown',
        guideText: 'Share where you\'re from to connect with people from your area.',
        isTextArea: false,
        isDropdown: false
    },
    bio: {
        label: 'Bio',
        placeholder: 'You haven\'t set your bio yet. Enter your thought and share with the world!',
        guideText: 'Tell others about yourself! Share your interests, hobbies, or what makes you unique.',
        isTextArea: true,
        isDropdown: false
    },
    monthlyPrice: {
        label: 'Monthly price',
        placeholder: 'Enter monthly price ($)',
        guideText: 'Set your monthly subscription price. Leave blank if you don\'t offer subscriptions.',
        isTextArea: false,
        isDropdown: false
    },
    yearlyPrice: {
        label: 'Yearly price',
        placeholder: 'Enter yearly price ($)',
        guideText: 'Set your yearly subscription price. This often includes a discount compared to monthly.',
        isTextArea: false,
        isDropdown: false
    },
    pronouns: {
        label: 'Pronouns',
        placeholder: 'e.g., he/him, she/her, they/them',
        guideText: 'Help others know how to refer to you respectfully.',
        isTextArea: false,
        isDropdown: false
    },
    career: {
        label: 'Career',
        placeholder: 'Enter your career/profession',
        guideText: 'Share your profession or what you do. This helps others understand your expertise.',
        isTextArea: false,
        isDropdown: false
    },
    companies: {
        label: 'Companies',
        placeholder: 'Enter company names',
        guideText: 'List companies you work for or have worked with.',
        isTextArea: false,
        isDropdown: false
    },
    education: {
        label: 'Education',
        placeholder: 'Enter your education',
        guideText: 'Share your educational background, schools, or degrees.',
        isTextArea: false,
        isDropdown: false
    },
    relationship: {
        label: 'Relationship',
        placeholder: 'Enter relationship status',
        guideText: 'Your relationship status is optional and can help others understand your situation.',
        isTextArea: false,
        isDropdown: false
    },
    socialLink: {
        label: 'Social Link',
        placeholder: 'Enter your social media URL',
        guideText: 'Update your social media or website link. Make sure the URL is correct and accessible.',
        isTextArea: false,
        isDropdown: false
    },
    website: {
        label: 'Website',
        placeholder: 'Enter your website URL',
        guideText: 'Add your personal website, portfolio, or blog URL.',
        isTextArea: false,
        isDropdown: false
    }
}; 