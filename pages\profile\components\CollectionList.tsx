import { FlatList, StyleSheet, View } from 'react-native';
import StoryItem from './CollectionItem';

interface CollectionListProps {
  collections?: any[];
}

const CollectionList: React.FC<CollectionListProps> = ({ collections = [] }) => {


  const renderStoryItem = ({ item, index }: { item: any; index: number }) => {
    const isCreateStory = index === 0;
    return (
      <StoryItem
        item={item}
        isCreateStory={isCreateStory}
        onPress={() => console.log(`Pressed item ${item.id}`)}
      />
    );
  };

  // Use provided collections data or fallback to story data
  const displayData = collections
  const combinedData = [...displayData];

  return (
    <View>
      <FlatList
        data={combinedData}
        renderItem={renderStoryItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        windowSize={10}
        initialNumToRender={3}
        nestedScrollEnabled={true}
      />
    </View>
  );
};

export default CollectionList;

const styles = StyleSheet.create({
  flatListContent: {
    paddingHorizontal: 16,
    gap: 4,
  },
});
