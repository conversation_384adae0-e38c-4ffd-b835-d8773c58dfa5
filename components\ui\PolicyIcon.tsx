import Svg, { Path } from 'react-native-svg';
import { CustomSvgProps } from '../../types/svgTypes';

const PolicyIcon = ({ size = 45, color = '#99A1BE', ...props }: CustomSvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 45 48" fill="none" {...props}>
    <Path
      fill={color}
      fillRule="evenodd"
      d="M31.125 25.556H13.54a1.827 1.827 0 0 1 0-3.654h17.585a1.827 1.827 0 0 1 0 3.654Zm0 9.156H13.54a1.827 1.827 0 0 1 0-3.654h17.585a1.827 1.827 0 0 1 0 3.654ZM13.54 12.742h6.71a1.827 1.827 0 0 1 0 3.653h-6.71a1.827 1.827 0 0 1 0-3.653ZM22.369.132C6.379.133.713 6.349.713 23.883.713 41.415 6.38 47.63 22.37 47.63c15.987 0 21.655-6.216 21.655-23.747 0-17.535-5.668-23.75-21.655-23.75Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default PolicyIcon;
