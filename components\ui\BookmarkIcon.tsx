import Svg, { SvgProps, Path, Defs, LinearGradient, Stop } from 'react-native-svg';

interface CustomSvgProps extends SvgProps {
  size?: number;
}

const BookmarkIcon = ({ size = 44, ...props }: CustomSvgProps) => (
  <Svg width={size} height={(size * 54) / 44} viewBox="0 0 44 54" fill="none" {...props}>
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M31.132 21.64H12.918a1.994 1.994 0 0 1 0-3.986h18.214a1.994 1.994 0 0 1 0 3.985ZM43.2 29.67l-.008-5.477C43.193 4.015 40.029.468 22.027.468 4.025.468.86 4.015.86 24.193l-.008 5.476C.828 44.615.818 50.211 3.1 52.493c.739.742 1.72 1.116 2.912 1.116 2.538 0 5.36-2.41 8.349-4.963 2.646-2.261 5.646-4.823 7.665-4.823 2.02 0 5.02 2.562 7.666 4.823 2.99 2.553 5.811 4.963 8.349 4.963 1.193 0 2.173-.374 2.912-1.116 2.282-2.282 2.271-7.878 2.247-22.824Z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={22.026}
        x2={22.026}
        y1={-30.088}
        y2={74.866}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#01DDEB" />
        <Stop offset={1} stopColor="#5510E8" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default BookmarkIcon;
