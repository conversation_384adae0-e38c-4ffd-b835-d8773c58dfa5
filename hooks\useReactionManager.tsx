import { ReactionType } from '@/components/ui/ReactionPicker';
import { useEffect, useRef, useState } from 'react';

interface PostReaction {
  postId: number;
  reactionType: string | null; // null means no reaction (unliked)
}

interface UseReactionManagerProps {
  postId: number;
  initialReaction?: string | null;
}

export const useReactionManager = ({ postId, initialReaction = null }: UseReactionManagerProps) => {
  const [currentReaction, setCurrentReaction] = useState<string | null>(initialReaction);
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const isLongPressing = useRef(false);
  const pickerOpenTime = useRef<number>(0);

  // Auto-close picker when user interacts with other elements
  useEffect(() => {
    if (showReactionPicker) {
      // Close picker after a timeout (in case user just abandons it)
      const abandonTimeout = setTimeout(() => {
        setShowReactionPicker(false);
      }, 10000); // Close after 10 seconds of inactivity

      return () => {
        clearTimeout(abandonTimeout);
      };
    }
  }, [showReactionPicker]);

  const handleLikePress = (event: any) => {
    // If user has no reaction, set default like reaction
    if (!currentReaction) {
      setCurrentReaction('like');
      return;
    }
    
    // If user has any reaction (including like), remove it (return to null)
    setCurrentReaction(null);
  };

  const handleLongPressStart = (event: any) => {
    isLongPressing.current = true;
    longPressTimer.current = setTimeout(() => {
      if (isLongPressing.current) {
        setShowReactionPicker(true);
        pickerOpenTime.current = Date.now(); // Record when picker opens
      }
    }, 500); // 500ms long press delay
  };

  const handleLongPressEnd = () => {
    isLongPressing.current = false;
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  };

  const handleReactionSelect = (reaction: ReactionType) => {
    // Always set the selected reaction (no toggle behavior for picker selection)
    setCurrentReaction(reaction.id);
    setShowReactionPicker(false); // Force close immediately for reaction selection
  };

  const handleReactionPickerClose = (force: boolean = false) => {
    // Allow forced closure (for reaction selection) or respect minimum time for other closures
    if (!force) {
      // Prevent immediate closure (must be open for at least 500ms)
      const timeSinceOpen = Date.now() - pickerOpenTime.current;
      if (timeSinceOpen < 500) {
        return;
      }
    }
    
    setShowReactionPicker(false);
  };

  const getCurrentReactionAsset = (reactions: ReactionType[]) => {
    if (!currentReaction) return null;
    
    // Don't show asset for default like reaction
    if (currentReaction === 'like') return null;
    
    return reactions.find(r => r.id === currentReaction);
  };

  const isLiked = () => {
    return currentReaction !== null;
  };

  const isDefaultLike = () => {
    return currentReaction === 'like';
  };

  return {
    currentReaction,
    showReactionPicker,
    handleLikePress,
    handleLongPressStart,
    handleLongPressEnd,
    handleReactionSelect,
    handleReactionPickerClose,
    getCurrentReactionAsset,
    isLiked,
    isDefaultLike,
  };
};
