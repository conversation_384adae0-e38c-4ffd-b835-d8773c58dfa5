import type { ImageColorsResult } from 'react-native-image-colors';
import ImageColors from 'react-native-image-colors';

export interface ExtractedColors {
  primary: string;
  secondary: string;
  detail: string;
  background: string;
}

// Enhanced color quality metrics
interface ColorQuality {
  vibrancy: number;
  contrast: number;
  luminance: number;
  saturation: number;
  score: number;
}

// Predefined color palettes remain as a fallback
const colorPalettes = {
  azure: { primary: '#0ea5e9', secondary: '#7dd3fc', detail: '#0369a1', background: '#e0f2fe' },
  navy: { primary: '#1e40af', secondary: '#60a5fa', detail: '#1e3a8a', background: '#dbeafe' },
  emerald: { primary: '#059669', secondary: '#34d399', detail: '#047857', background: '#d1fae5' },
  forest: { primary: '#16a34a', secondary: '#4ade80', detail: '#15803d', background: '#dcfce7' },
  sage: { primary: '#84cc16', secondary: '#a3e635', detail: '#65a30d', background: '#ecfccb' },
  sunset: { primary: '#ea580c', secondary: '#fb923c', detail: '#c2410c', background: '#fed7aa' },
  amber: { primary: '#d97706', secondary: '#fbbf24', detail: '#92400e', background: '#fef3c7' },
  coral: { primary: '#f97316', secondary: '#fdba74', detail: '#ea580c', background: '#ffedd5' },
  lavender: { primary: '#8b5cf6', secondary: '#c4b5fd', detail: '#7c3aed', background: '#f3f4f6' },
  violet: { primary: '#7c3aed', secondary: '#a78bfa', detail: '#6d28d9', background: '#ede9fe' },
  indigo: { primary: '#4f46e5', secondary: '#818cf8', detail: '#4338ca', background: '#e0e7ff' },
  rose: { primary: '#e11d48', secondary: '#fb7185', detail: '#be123c', background: '#ffe4e6' },
  fuchsia: { primary: '#c2410c', secondary: '#f472b6', detail: '#a21caf', background: '#fce7f3' },
  teal: { primary: '#0d9488', secondary: '#2dd4bf', detail: '#0f766e', background: '#ccfbf1' },
  cyan: { primary: '#0891b2', secondary: '#22d3ee', detail: '#0e7490', background: '#cffafe' },
  slate: { primary: '#475569', secondary: '#94a3b8', detail: '#334155', background: '#f1f5f9' },
  stone: { primary: '#57534e', secondary: '#a8a29e', detail: '#44403c', background: '#f5f5f4' },
};

// --- (Advanced hash and theme detection functions remain unchanged) ---
const advancedHash = (str: string): number => {
  let hash = 5381;
  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) + hash) ^ str.charCodeAt(i);
  }
  return Math.abs(hash);
};

const detectThemeFromUrl = (url: string): string | null => {
  const lowerUrl = url.toLowerCase();
  const themeKeywords = {
    emerald: ['nature', 'forest', 'tree', 'jungle', 'green', 'leaf', 'plant'],
    forest: ['woods', 'wilderness', 'pine', 'oak', 'mountain'],
    sage: ['herb', 'mint', 'garden', 'botanical'],
    azure: ['sky', 'cloud', 'air', 'heaven', 'bright'],
    navy: ['ocean', 'sea', 'deep', 'marine', 'nautical'],
    teal: ['water', 'lake', 'river', 'aqua', 'turquoise'],
    cyan: ['ice', 'crystal', 'glacier', 'arctic'],
    sunset: ['sunset', 'dusk', 'evening', 'orange', 'warm'],
    amber: ['autumn', 'fall', 'golden', 'honey', 'wheat'],
    coral: ['tropical', 'beach', 'summer', 'warm', 'sand'],
    rose: ['rose', 'flower', 'bloom', 'pink', 'romantic'],
    fuchsia: ['magenta', 'bright', 'vibrant', 'bold'],
    lavender: ['lavender', 'purple', 'calm', 'relaxing'],
    violet: ['violet', 'grape', 'royal', 'luxury'],
    indigo: ['night', 'midnight', 'dark', 'deep'],
    slate: ['urban', 'city', 'modern', 'concrete'],
    stone: ['minimal', 'neutral', 'clean', 'simple'],
  };
  const themeScores: { [key: string]: number } = {};
  Object.entries(themeKeywords).forEach(([theme, keywords]) => {
    themeScores[theme] = 0;
    keywords.forEach(keyword => {
      if (lowerUrl.includes(keyword)) {
        themeScores[theme] += keyword.length;
      }
    });
  });
  const bestTheme = Object.entries(themeScores).sort(([, a], [, b]) => b - a).find(([, score]) => score > 0);
  return bestTheme ? bestTheme[0] : null;
};

const getTimeBasedVariation = (): number => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 12) return 10; // Morning
  if (hour >= 12 && hour < 18) return 0;  // Afternoon
  if (hour >= 18 && hour < 22) return -5; // Evening
  return -15; // Night
};

// **ENHANCED**: Advanced color conversion and analysis utilities
const hexToHsl = (hex: string): [number, number, number] => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return [0, 0, 0];
  let r = parseInt(result[1], 16) / 255;
  let g = parseInt(result[2], 16) / 255;
  let b = parseInt(result[3], 16) / 255;
  const max = Math.max(r, g, b), min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  return [h * 360, s, l];
};

const hexToRgb = (hex: string): [number, number, number] => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return [0, 0, 0];
  return [
    parseInt(result[1], 16),
    parseInt(result[2], 16),
    parseInt(result[3], 16)
  ];
};

const hslToHex = (h: number, s: number, l: number): string => {
  let r: number, g: number, b: number;
  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    h /= 360;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }
  const toHex = (x: number) => {
    const hex = Math.round(x * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

// **NEW**: Advanced color quality assessment
const calculateColorQuality = (color: string): ColorQuality => {
  const [h, s, l] = hexToHsl(color);
  const [r, g, b] = hexToRgb(color);
  
  // Calculate relative luminance (WCAG standard)
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Calculate vibrancy based on saturation and distance from gray
  const vibrancy = s * (1 - Math.abs(0.5 - l));
  
  // Calculate contrast potential (how well it contrasts with white/black)
  const contrastWithWhite = (1 + 0.05) / (luminance + 0.05);
  const contrastWithBlack = (luminance + 0.05) / (0 + 0.05);
  const contrast = Math.max(contrastWithWhite, contrastWithBlack);
  
  // Composite score favoring vibrant, well-contrasting colors
  const saturationWeight = 0.35;
  const contrastWeight = 0.25;
  const luminanceWeight = 0.2;
  const vibrancyWeight = 0.2;
  
  // Ideal luminance is neither too dark nor too light
  const luminanceScore = 1 - Math.abs(0.5 - luminance) * 2;
  
  const score = (
    s * saturationWeight +
    Math.min(contrast / 7, 1) * contrastWeight +
    luminanceScore * luminanceWeight +
    vibrancy * vibrancyWeight
  );
  
  return {
    vibrancy,
    contrast,
    luminance,
    saturation: s,
    score
  };
};

// **NEW**: Perceptual color distance calculation (simplified Delta E)
const calculateColorDistance = (color1: string, color2: string): number => {
  const [r1, g1, b1] = hexToRgb(color1);
  const [r2, g2, b2] = hexToRgb(color2);
  
  // Convert to LAB-like space for better perceptual distance
  const deltaR = r1 - r2;
  const deltaG = g1 - g2;
  const deltaB = b1 - b2;
  
  // Weight the differences based on human perception
  const redWeight = 0.3;
  const greenWeight = 0.59;
  const blueWeight = 0.11;
  
  return Math.sqrt(
    redWeight * deltaR * deltaR +
    greenWeight * deltaG * deltaG +
    blueWeight * deltaB * deltaB
  );
};

// **NEW**: Smart color selection with quality ranking
const selectBestColors = (candidates: string[]): string[] => {
  const validCandidates = candidates
    .filter(color => color && color.startsWith('#'))
    .map(color => ({
      color,
      quality: calculateColorQuality(color)
    }))
    .filter(item => item.quality.score > 0.2) // Filter out poor quality colors
    .sort((a, b) => b.quality.score - a.quality.score);

  const selected: string[] = [];
  
  for (const candidate of validCandidates) {
    // Ensure minimum perceptual distance from already selected colors
    const hasMinDistance = selected.every(selectedColor => 
      calculateColorDistance(candidate.color, selectedColor) > 50
    );
    
    if (hasMinDistance || selected.length === 0) {
      selected.push(candidate.color);
    }
    
    if (selected.length >= 4) break;
  }
  
  return selected;
};

// **NEW**: Enhanced vibrancy with intelligent saturation
const enhanceVibrancy = (color: string, targetVibrancy: number = 0.7): string => {
  if (!color || !color.startsWith('#')) return color;
  
  const [h, s, l] = hexToHsl(color);
  const quality = calculateColorQuality(color);
  
  // Adjust saturation based on current quality and target
  let newS = s;
  if (quality.vibrancy < targetVibrancy) {
    // Increase saturation for dull colors, but don't oversaturate
    newS = Math.min(0.9, s + (targetVibrancy - quality.vibrancy) * 0.5);
  }
  
  // Ensure good luminance for visibility
  let newL = l;
  if (l < 0.15) {
    newL = 0.2; // Prevent too dark colors
  } else if (l > 0.9) {
    newL = 0.85; // Prevent too light colors
  }
  
  return hslToHex(h, newS, newL);
};

// **NEW**: Generate harmonious secondary colors
const generateHarmoniousColor = (baseColor: string, type: 'analogous' | 'complementary' | 'triadic' = 'analogous'): string => {
  const [h, s, l] = hexToHsl(baseColor);
  
  let newHue = h;
  switch (type) {
    case 'complementary':
      newHue = (h + 180) % 360;
      break;
    case 'triadic':
      newHue = (h + 120) % 360;
      break;
    case 'analogous':
    default:
      newHue = (h + 30) % 360;
      break;
  }
  
  // Slightly adjust saturation and lightness for variety
  const newS = Math.max(0.1, Math.min(0.9, s * 0.8));
  const newL = Math.max(0.2, Math.min(0.8, l + 0.1));
  
  return hslToHex(newHue, newS, newL);
};

// **ENHANCED**: Intelligent color processor with advanced selection
const processAndSelectColors = (result: ImageColorsResult): ExtractedColors => {
  const fallback = colorPalettes.slate;
  
  // Collect all available colors
  const availableColors: string[] = [];
  
  if (result.platform === 'android' || result.platform === 'web') {
    if (result.vibrant) availableColors.push(result.vibrant);
    if (result.dominant) availableColors.push(result.dominant);
    if (result.lightVibrant) availableColors.push(result.lightVibrant);
    if (result.darkVibrant) availableColors.push(result.darkVibrant);
    if (result.lightMuted) availableColors.push(result.lightMuted);
    if (result.darkMuted) availableColors.push(result.darkMuted);
    if (result.muted) availableColors.push(result.muted);
  } else if (result.platform === 'ios') {
    if (result.primary) availableColors.push(result.primary);
    if (result.secondary) availableColors.push(result.secondary);
    if (result.detail) availableColors.push(result.detail);
    if (result.background) availableColors.push(result.background);
  }
  
  // Select best colors using quality assessment
  const bestColors = selectBestColors(availableColors);
  
  // Assign roles based on quality and characteristics
  let primary = bestColors[0] || fallback.primary;
  let secondary = bestColors[1] || generateHarmoniousColor(primary, 'analogous');
  let detail = bestColors[2] || generateHarmoniousColor(primary, 'triadic');
  
  // Enhance colors for better visual impact
  primary = enhanceVibrancy(primary, 0.75);
  secondary = enhanceVibrancy(secondary, 0.65);
  detail = enhanceVibrancy(detail, 0.55);
  
  // Generate appropriate background color
  const [primaryH, primaryS] = hexToHsl(primary);
  let background = hslToHex(primaryH, Math.min(0.15, primaryS * 0.3), 0.95);
  
  // Ensure background provides good contrast
  const bgQuality = calculateColorQuality(background);
  if (bgQuality.luminance < 0.85) {
    background = hslToHex(primaryH, 0.1, 0.96);
  }
  
  return {
    primary,
    secondary,
    detail,
    background,
  };
};

// **ENHANCED**: Main extraction function with improved error handling
export const extractColorsFromImage = async (imageUri: string): Promise<ExtractedColors> => {
  try {
    const result = await ImageColors.getColors(imageUri, {
      fallback: '#222222',
      cache: true,
      key: imageUri,
      quality: 'high', // Request higher quality analysis
    });

    // Use the enhanced intelligent color processor
    const colors = processAndSelectColors(result);

    // Apply subtle time-based adjustments
    const timeVariation = getTimeBasedVariation();
    return {
      primary: adjustColorBrightness(colors.primary, timeVariation * 0.3),
      secondary: adjustColorBrightness(colors.secondary, timeVariation * 0.2),
      detail: adjustColorBrightness(colors.detail, timeVariation * 0.15),
      background: adjustColorBrightness(colors.background, Math.abs(timeVariation) * 0.1),
    };

  } catch (error) {
    console.warn('Advanced color extraction failed. Falling back to URL analysis.', error);
    
    // Enhanced fallback with better color generation
    try {
      const detectedTheme = detectThemeFromUrl(imageUri);
      const hash = advancedHash(imageUri);
      const paletteKeys = Object.keys(colorPalettes);
      const fallbackTheme = paletteKeys[hash % paletteKeys.length];
      const chosenTheme = detectedTheme || fallbackTheme;
      const palette = colorPalettes[chosenTheme as keyof typeof colorPalettes] || colorPalettes.azure;

      // Enhance fallback colors as well
      const enhancedPalette = {
        primary: enhanceVibrancy(palette.primary, 0.7),
        secondary: enhanceVibrancy(palette.secondary, 0.6),
        detail: enhanceVibrancy(palette.detail, 0.5),
        background: palette.background,
      };

      const timeVariation = getTimeBasedVariation();
      const hashVariation = (hash % 15) - 7; // Reduced variation range

      return {
        primary: adjustColorBrightness(enhancedPalette.primary, timeVariation + hashVariation),
        secondary: adjustColorBrightness(enhancedPalette.secondary, (timeVariation + hashVariation) * 0.6),
        detail: adjustColorBrightness(enhancedPalette.detail, (timeVariation + hashVariation) * 0.4),
        background: adjustColorBrightness(enhancedPalette.background, Math.abs(timeVariation + hashVariation) * 0.2),
      };
    } catch (fallbackError) {
      console.error('URL-based fallback also failed. Using default colors.', fallbackError);
      return getDefaultColors();
    }
  }
};

// **ENHANCED**: Brightness adjustment with better color space handling
const adjustColorBrightness = (color: string, amount: number): string => {
  if (!color || !color.startsWith('#')) return color;
  
  // Use HSL for more perceptually accurate brightness adjustment
  const [h, s, l] = hexToHsl(color);
  const adjustmentFactor = amount / 100; // Convert to percentage
  
  // Adjust lightness while preserving hue and saturation
  let newL = l + adjustmentFactor;
  newL = Math.max(0.05, Math.min(0.95, newL)); // Keep within reasonable bounds
  
  return hslToHex(h, s, newL);
};

// Default colors (unchanged)
const getDefaultColors = (): ExtractedColors => ({
  primary: '#0ea5e9',
  secondary: '#7dd3fc',
  detail: '#0369a1',
  background: '#e0f2fe',
});

// --- (Caching, gradient, and other utility functions remain unchanged) ---

class ColorCache {
  private cache = new Map<string, { colors: ExtractedColors; timestamp: number }>();
  private readonly maxSize = 100;
  private readonly maxAge = 24 * 60 * 60 * 1000;

  get(imageUri: string): ExtractedColors | null {
    const entry = this.cache.get(imageUri);
    if (!entry || Date.now() - entry.timestamp > this.maxAge) {
      if (entry) this.cache.delete(imageUri);
      return null;
    }
    return entry.colors;
  }

  set(imageUri: string, colors: ExtractedColors): void {
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) this.cache.delete(oldestKey);
    }
    this.cache.set(imageUri, { colors, timestamp: Date.now() });
  }
}
const colorCache = new ColorCache();

export const getCachedColors = async (imageUri: string): Promise<ExtractedColors> => {
  const cached = colorCache.get(imageUri);
  if (cached) {
    return cached;
  }
  const colors = await extractColorsFromImage(imageUri);
  colorCache.set(imageUri, colors);
  return colors;
};

export const addAlphaToColor = (color: string, alpha: number): string => {
  alpha = Math.max(0, Math.min(1, alpha));
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    let fullHex = hex.length === 3 ? hex.split('').map(c => c + c).join('') : hex;
    const num = parseInt(fullHex, 16);
    const r = (num >> 16) & 255;
    const g = (num >> 8) & 255;
    const b = num & 255;
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  if (color.startsWith('rgb(')) {
    const match = color.match(/rgb\(s*(\d+)s*,s*(\d+)s*,s*(\d+)s*\)/);
    if (match) return `rgba(${match[1]}, ${match[2]}, ${match[3]}, ${alpha})`;
  }
  if (color.startsWith('rgba(')) {
    return color.replace(/rgba\((.+),s*[\d.]+\)/, `rgba($1, ${alpha})`);
  }
  return `rgba(14, 165, 233, ${alpha})`;
};

export const generateGradientColors = (baseColor: string, steps: number = 5, fadeType: 'linear' | 'exponential' | 'smooth' = 'smooth'): string[] => {
  const colors: string[] = [];
  for (let i = 0; i < steps; i++) {
    let alpha: number;
    const progress = i / (steps - 1);
    switch (fadeType) {
      case 'exponential': alpha = 1 - Math.pow(progress, 2); break;
      case 'smooth': alpha = 1 - (3 * progress * progress - 2 * progress * progress * progress); break;
      default: alpha = 1 - progress * 0.85; break;
    }
    colors.push(addAlphaToColor(baseColor, Math.max(0.05, alpha)));
  }
  return colors;
};

export const getComplementaryColor = (color: string): string => {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = 255 - ((num >> 16) & 255);
  const g = 255 - ((num >> 8) & 255);
  const b = 255 - (num & 255);
  return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;
};

export const isLightColor = (color: string): boolean => {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  const r = (num >> 16) & 255;
  const g = (num >> 8) & 255;
  const b = num & 255;
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5;
};