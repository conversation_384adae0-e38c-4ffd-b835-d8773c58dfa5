import FriendCard from '@/components/ui/FriendCard';
import TopNavigationTitle from '@/components/ui/TopNavigationTitle';
import PostData from '@/store/data/PostData';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { Ionicons } from '@expo/vector-icons';
import { useCallback, useEffect, useState } from 'react';
import { Alert, FlatList, ImageBackground, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { GestureHandlerRootView, ScrollView, TextInput } from 'react-native-gesture-handler';
import { KeyboardAvoidingView, KeyboardProvider } from 'react-native-keyboard-controller';
import AvatarFriends from './components/AvatarFriends';
import Post from './components/Post';

// Voice Recognition Setup Instructions:
// 1. For Android: Add microphone permission in android/app/src/main/AndroidManifest.xml:
//    <uses-permission android:name="android.permission.RECORD_AUDIO" />
// 2. For iOS: Add microphone permission in ios/WConnect/Info.plist:
//    <key>NSMicrophoneUsageDescription</key>
//    <string>This app needs access to microphone for voice search</string>
// 3. For iOS: Run 'cd ios && pod install' after installing the package

const SearchPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [recognizedText, setRecognizedText] = useState('');

  useEffect(() => {
    // Voice event listeners
    // Voice.onSpeechStart = onSpeechStart;
    // Voice.onSpeechRecognized = onSpeechRecognized;
    // Voice.onSpeechEnd = onSpeechEnd;
    // Voice.onSpeechError = onSpeechError;
    // Voice.onSpeechResults = onSpeechResults;
    // Voice.onSpeechPartialResults = onSpeechPartialResults;

    return () => {
      // Cleanup
      // Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  const onSpeechStart = (e: any) => {
    console.log('onSpeechStart: ', e);
    setIsListening(true);
  };

  const onSpeechRecognized = (e: any) => {
    console.log('onSpeechRecognized: ', e);
  };

  const onSpeechEnd = (e: any) => {
    console.log('onSpeechEnd: ', e);
    setIsListening(false);
  };

  const onSpeechError = (e: any) => {
    console.log('onSpeechError: ', e);
    setIsListening(false);
    Alert.alert('Error', 'Speech recognition failed. Please try again.');
  };

  const onSpeechResults = (e: any) => {
    console.log('onSpeechResults: ', e);
    if (e.value && e.value.length > 0) {
      const spokenText = e.value[0];
      setSearchQuery(spokenText);
      setRecognizedText(spokenText);
    }
  };

  const onSpeechPartialResults = (e: any) => {
    console.log('onSpeechPartialResults: ', e);
    if (e.value && e.value.length > 0) {
      setRecognizedText(e.value[0]);
    }
  };

  const startListening = async () => {
    try {
      // await Voice.start('en-US');
      setIsListening(true);
      setRecognizedText('');
    } catch (e) {
      console.error(e);
      Alert.alert('Error', 'Failed to start voice recognition. Please check microphone permissions.');
    }
  };

  const stopListening = async () => {
    try {
      // await Voice.stop();
      setIsListening(false);
    } catch (e) {
      console.error(e);
    }
  };

  const handleVoiceSearch = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };
  const renderPost = useCallback(
    ({ item }: { item: any }) => (
      <View className="px-4">
        <Post post={item} />
      </View>
    ),
    []
  );

  const renderItemSeparator = useCallback(() => <View style={{ height: 16 }} />, []);

  const ListHeaderComponent = () => (
    <View>
      <View style={styles.topContainer}>
        <TopNavigationTitle title={'Search center'}></TopNavigationTitle>
      </View>

      <View className=" py-2" style={styles.friendsContainer}>
        <Text className="px-4 font-roboto-bold text-[18px]">People and Friends</Text>
        <GestureHandlerRootView>
          <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
            <AvatarFriends></AvatarFriends>
            <AvatarFriends></AvatarFriends>
            <AvatarFriends></AvatarFriends>
            <AvatarFriends></AvatarFriends>
          </ScrollView>
        </GestureHandlerRootView>
      </View>

      <View className=" py-2" style={styles.friendsContainer}>
        <View className="flex-row items-center justify-between  px-4 ">
          <Text className="font-roboto-bold text-[18px]">People you may know</Text>
          <Text className="font-roboto-medium text-[14px] color-primary-500">See all</Text>
        </View>
        <GestureHandlerRootView>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              gap: 16
            }}
            style={{ overflow: 'visible' }}>
            <FriendCard></FriendCard>
            <FriendCard></FriendCard>
            <FriendCard></FriendCard>
            <FriendCard></FriendCard>
          </ScrollView>
        </GestureHandlerRootView>
      </View>

      <View className="px-4 py-4">
        <Text className="font-roboto-bold text-[18px]">Posts</Text>
      </View>
    </View>
  );

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <KeyboardProvider>
        <KeyboardAvoidingView
          style={{ flex: 1, backgroundColor: 'transparent' }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
          <View style={styles.container}>
            <FlatList
              data={PostData}
              renderItem={renderPost}
              keyExtractor={(item) => item.id.toString()}
              ListHeaderComponent={ListHeaderComponent}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: Platform.OS === 'ios' ? 110 : 90 }}
              ItemSeparatorComponent={renderItemSeparator}
              bounces={true}
              removeClippedSubviews={true}
              maxToRenderPerBatch={5}
              windowSize={10}
              initialNumToRender={3}
              updateCellsBatchingPeriod={50}
            />
          </View>
          <View style={styles.searchContainer}>
            <ImageBackground
              source={require('../../assets/images/icons/Search Bar.png')}
              style={styles.searchInputContainer}
              imageStyle={styles.searchBackgroundImage}
              resizeMode="stretch">
              <TextInput
                style={styles.searchInput}
                placeholder={isListening ? "Listening..." : "What are you looking for? Search with AI"}
                placeholderTextColor={isListening ? Colors.primary[500] : Colors.primary[500]}
                value={isListening ? recognizedText : searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="search"
                editable={!isListening}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  style={styles.clearIcon}
                  onPress={() => setSearchQuery('')}>
                  <Ionicons
                    name="close-circle"
                    size={20}
                    color={Colors.text.muted}
                  />
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={[styles.micIcon, isListening && styles.micIconActive]}
                onPress={handleVoiceSearch}
                activeOpacity={0.7}>
                <Ionicons
                  name={isListening ? "mic" : "mic-outline"}
                  size={22}
                  color={Colors.primary[500]}
                />
                {isListening && (
                  <View style={styles.listeningIndicator}>
                    <View style={[styles.pulseCircle, styles.pulse1]} />
                    <View style={[styles.pulseCircle, styles.pulse2]} />
                  </View>
                )}
              </TouchableOpacity>
            </ImageBackground>
          </View>
        </KeyboardAvoidingView>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
};

export default SearchPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  topContainer: {
    marginTop: 8,
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0,
  },
  friendsContainer: {
    height: 'auto',
  },
  searchContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 0,
    paddingVertical: 16,
    zIndex: 1000,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 35,
    paddingHorizontal: 25,
    paddingVertical: 12,
    height: 90,
    overflow: 'hidden',
  },
  searchBackgroundImage: {
    borderRadius: 35,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    fontFamily: Fonts.roboto.bold,
    color: Colors.text.primary,
    paddingVertical: 0,
    backgroundColor: 'transparent',
  },
  clearIcon: {
    marginLeft: 12,
    padding: 4,
  },
  micIcon: {
    marginLeft: 8,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    position: 'relative',
  },
  micIconActive: {
    backgroundColor: 'rgba(59, 130, 246, 0.2)',
  },
  listeningIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseCircle: {
    position: 'absolute',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: Colors.primary[500],
  },
  pulse1: {
    width: 40,
    height: 40,
    opacity: 0.6,
  },
  pulse2: {
    width: 50,
    height: 50,
    opacity: 0.3,
  },
});
