import { ImageAdvanced } from "@/components";
import ProfileData from "@/store/data/ProfileData";
import { Colors } from "@/utils/colors";
import { FlexLayouts, SafeAreaContainers, Spacing } from "@/utils/layoutStyles";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface TopBarProps {
    title: string;
    subtitle?: string;
    avatar?: string;
}

const TopBar = ({ title, subtitle, avatar }: TopBarProps) => {
    const handleCallPress = () => {
        console.log('Call pressed');
    };

    const handleVideoPress = () => {
        console.log('Video call pressed');
    };

    return (
        <View style={[FlexLayouts.rowBetween, SafeAreaContainers.topSafeMargin]}>
            <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm }]}>
                <Ionicons
                    name="arrow-back"
                    size={24}
                    color={Colors.primary['950']}
                    onPress={() => {
                        router.back();
                    }}
                />
                <View style={FlexLayouts.rowCenter}>
                    <View style={styles.avatarContainer}>
                        <ImageAdvanced 
                            source={avatar || ProfileData.profile.avatar} 
                            style={styles.avatar} 
                        />
                        {subtitle === 'Online' && <View style={styles.onlineIndicator} />}
                    </View>
                    <View className="ml-2">
                        <Text className="font-roboto-bold text-2xl">{title}</Text>
                        <Text className="font-roboto-regular text-sm text-text-muted2">
                            {subtitle || 'Active 10 minutes ago'}
                        </Text>
                    </View>
                </View>

            </View>
            <View style={[FlexLayouts.rowEnd, { gap: Spacing.horizontal.sm }]}>
                <TouchableOpacity 
                    style={[FlexLayouts.rowCenter, styles.actionButton]}
                    onPress={handleCallPress}
                >
                    <Ionicons name="call" size={24} color={Colors.secondary['950']} />
                </TouchableOpacity>
                <TouchableOpacity 
                    style={[FlexLayouts.rowCenter, styles.actionButton]}
                    onPress={handleVideoPress}
                >
                    <Ionicons name="videocam" size={24} color={Colors.secondary['950']} />
                </TouchableOpacity>
            </View>
        </View>
    )
}

const styles = StyleSheet.create({
    avatarContainer: {
        position: 'relative',
    },
    avatar: {
        width: 40,
        height: 40,
        borderRadius: 100,
    },
    onlineIndicator: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        width: 12,
        height: 12,
        borderRadius: 6,
        backgroundColor: Colors.success,
        borderWidth: 2,
        borderColor: Colors.background.primary,
    },
    actionButton: {
        width: 40,
        height: 40,
        borderRadius: 100,
        backgroundColor: Colors.background.muted,
    },
});

export default TopBar;

