import CameraIcon from '@/components/ui/CameraIcon';
import { useMediaPicker } from '@/hooks/useMediaPicker';
import { Colors } from '@/utils/colors';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface MediaSelectorProps {
  onMediaSelected: (mediaItems: any[]) => void;
  onSingleMediaSelected?: (mediaItem: any) => void;
  multipleSelection?: boolean;
  style?: any;
  iconSize?: number;
  iconColor?: string;
  showText?: boolean;
  text?: string;
  disabled?: boolean;
  disabledTitle?: string;
  disabledMessage?: string;
}

const MediaSelector: React.FC<MediaSelectorProps> = ({
  onMediaSelected,
  onSingleMediaSelected,
  multipleSelection = true,
  style,
  iconSize = 24,
  iconColor = Colors.primary[500],
  showText = true,
  text = 'Media',
  disabled = false,
  disabledTitle = 'Media Upload Disabled',
  disabledMessage = 'Media upload is currently not available.',
}) => {
  const { pickMultipleMedia, pickSingleMedia, takePhoto, isLoading } = useMediaPicker();

  const handleMediaSelection = () => {
    if (disabled) {
      Alert.alert(
        disabledTitle,
        disabledMessage,
        [{ text: 'OK' }]
      );
      return;
    }
    
    Alert.alert(
      'Select Media',
      'Choose how you want to add media',
      [
        {
          text: 'Camera',
          onPress: handleCamera,
        },
        {
          text: 'Gallery',
          onPress: handleGallery,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleCamera = async () => {
    const photo = await takePhoto();
    if (photo) {
      if (onSingleMediaSelected) {
        onSingleMediaSelected(photo);
      } else {
        onMediaSelected([photo]);
      }
    }
  };

  const handleGallery = async () => {
    if (multipleSelection) {
      const mediaItems = await pickMultipleMedia();
      if (mediaItems.length > 0) {
        onMediaSelected(mediaItems);
      }
    } else {
      const mediaItem = await pickSingleMedia();
      if (mediaItem) {
        if (onSingleMediaSelected) {
          onSingleMediaSelected(mediaItem);
        } else {
          onMediaSelected([mediaItem]);
        }
      }
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style, disabled && styles.disabledContainer]}
      onPress={handleMediaSelection}
      disabled={isLoading || disabled}
      activeOpacity={disabled ? 1 : 0.7}
    >
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <Ionicons name="hourglass" size={iconSize} color={iconColor} />
        </View>
      ) : (
        <CameraIcon size={iconSize} color={iconColor} />
      )}
      {showText && (
        <Text style={[styles.text, { color: iconColor }]} numberOfLines={1}>
          {isLoading ? 'Loading...' : text}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  disabledContainer: {
    opacity: 0.5,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default MediaSelector;
