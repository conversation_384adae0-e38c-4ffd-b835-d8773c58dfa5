import { StyleSheet, Text, View } from 'react-native';
import ViewContainer from '@/components/ui/ViewContainer';
import { Fonts } from '@/utils/fonts';
import Input from '@/components/authentication/Input';
import { Colors } from '@/utils/colors';
import AgreementCheckbox from '@/pages/authentication/components/AgreementCheckbox';
import ButtonPrimary from '@/components/ui/ButtonPrimary';
import { router } from 'expo-router';
import { useState } from 'react';

const SignupPage = () => {
  const [isChecked, setChecked] = useState(false);
  const [emailText, setEmailText] = useState('');
  return (
    <View className={'flex-1 bg-background-primary'}>
      <ViewContainer classNameCustom={'h-1/4 items-center justify-end'}>
        <Text style={styles.title}>Sign Up WConnect Account</Text>
      </ViewContainer>
      <ViewContainer classNameCustom={'items-center justify-center pt-3 gap-4'}>
        <Input
          enableShadow={false}
          backgroundColor={Colors.background.muted}
          placeholder="Enter your email/phone number"
          placeholderTextColor={Colors.text.muted}
          onChangeText={setEmailText}></Input>
        <AgreementCheckbox isChecked={isChecked} setChecked={setChecked} />
      </ViewContainer>
      <ViewContainer classNameCustom={'items-center justify-end h-32'}>
        <ButtonPrimary
          bgColor={isChecked && emailText.length > 0 ? Colors.primary[500] : Colors.text.muted}
          border={'0px solid'}
          borderColor="transparent"
          disabled={!isChecked || emailText.length === 0}
          onPress={() => router.push('/verification')}>
          <Text className={'text-[16px] text-white'}>Continue</Text>
        </ButtonPrimary>
      </ViewContainer>
      <ViewContainer classNameCustom={'flex-1 items-center justify-end'}>
        <ButtonPrimary
          bgColor={Colors.background.primary}
          border="1px solid"
          borderColor={Colors.text.muted}
          enableShadow={false}
          onPress={() => router.push('/login')}>
          <Text className={'text-[16px] text-black'}>Back to Login</Text>
        </ButtonPrimary>
      </ViewContainer>
    </View>
  );
};

export default SignupPage;

const styles = StyleSheet.create({
  title: {
    fontSize: 26,
    fontFamily: Fonts.roboto.regular,
    color: 'black',
    textAlign: 'center',
  },
});
