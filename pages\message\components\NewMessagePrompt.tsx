import ProfileData from "@/store/data/ProfileData";
import { Colors } from "@/utils/colors";
import { Fonts } from "@/utils/fonts";
import { FlexLayouts, Spacing } from "@/utils/layoutStyles";
import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface NewMessagePromptProps {
    contactAvatar?: string;
}

const NewMessagePrompt: React.FC<NewMessagePromptProps> = ({ contactAvatar }) => {
    return (
        <View style={styles.newMessageContainer}>
            <View style={[FlexLayouts.columnCenter, styles.contentWrapper]}>
                <View style={styles.avatarContainer}>
                    <Image
                        source={contactAvatar ? { uri: contactAvatar } : { uri: ProfileData.profile.avatar }}
                        style={styles.contactAvatar}
                    />
                    <Image
                        source={{ uri: ProfileData.profile.avatar }}
                        style={[styles.contactAvatar, styles.overlappingAvatar]}
                    />
                </View>
                <Text style={styles.newMessageTitle}>
                    Say hi to your new new friend, <PERSON>!
                </Text>
                <Text style={styles.newMessageSubtitle}>
                    Send a message or tap the sticker below.
                </Text>
                <TouchableOpacity style={styles.waveButton}>
                    <Text style={styles.waveButtonEmoji}>👋</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default NewMessagePrompt;

const styles = StyleSheet.create({
    newMessageContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: Spacing.horizontal.xl,
    },
    contentWrapper: {
        paddingVertical: Spacing.vertical.xl,
        borderRadius: 20,
        paddingHorizontal: Spacing.horizontal.xl,
        backgroundColor: Colors.background.muted,
    },
    avatarContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.vertical.xl,
        position: 'relative',
    },
    contactAvatar: {
        width: 60,
        height: 60,
        borderWidth: 3,
        borderColor: Colors.background.primary,
        borderRadius: 30,
    },
    overlappingAvatar: {
        marginLeft: -10,
        borderColor: Colors.background.primary,
    },
    newMessageTitle: {
        fontSize: 24,
        color: Colors.secondary['950'],
        textAlign: 'center',
        marginBottom: Spacing.vertical.md,
        lineHeight: 32,
        fontFamily: Fonts.roboto.bold,
    },
    newMessageSubtitle: {
        fontSize: 16,
        color: Colors.text.muted,
        textAlign: 'center',
        marginBottom: Spacing.vertical.xl,
    },
    waveButton: {
        backgroundColor: Colors.background.primary,
        borderRadius: 50,
        padding: 20,
        width: 80,
        height: 80,
        justifyContent: 'center',
        alignItems: 'center',
    },
    waveButtonEmoji: {
        fontSize: 30,
    },
});