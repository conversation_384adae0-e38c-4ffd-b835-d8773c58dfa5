import { memo, useCallback } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Colors } from '../../../utils/colors';
import { Fonts, Typography } from '../../../utils/fonts';
import { CommentItemProps } from './types';
import { parseMention } from './utils';

const CommentItem = memo<CommentItemProps>(({ item, onReply, onLike, getMentionDisplayName }) => {
  const { comment, depth } = item;
  const isReply = depth > 0;

  const handleReply = useCallback(() => {
    onReply(comment.id, comment.author);
  }, [comment.id, comment.author, onReply]);

  const handleLike = useCallback(() => {
    onLike(comment.id);
  }, [comment.id, onLike]);

  const renderCommentText = () => {
    const parsed = parseMention(comment.text);
    
    if (!parsed.hasMention) {
      return <Text style={styles.messageText}>{parsed.fullText}</Text>;
    }

    // Get the display name from the mention text (already contains full name)
    const displayName = getMentionDisplayName ? getMentionDisplayName(parsed.mentionText || '') : parsed.mentionText?.substring(1) || '';

    return (
      <Text style={styles.messageText}>
        <Text style={styles.mentionText}>{displayName}</Text>
        <Text>{parsed.messageText}</Text>
      </Text>
    );
  };

  return (
    <View style={[styles.commentContainer, { marginLeft: isReply ? 48 : 0 }]}>
      <Image source={{ uri: comment.avatar }} style={styles.avatar} />
      <View style={styles.commentContent}>
        <View style={styles.messageBubble}>
          <Text style={styles.authorName}>{comment.author}</Text>
          {renderCommentText()}
        </View>
        <View style={styles.actionsContainer}>
          <TouchableOpacity onPress={handleLike} style={styles.actionButton}>
            <Text style={styles.actionText}>Like {comment.likes > 0 && `(${comment.likes})`}</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleReply} style={styles.actionButton}>
            <Text style={styles.actionText}>Reply</Text>
          </TouchableOpacity>
          <Text style={styles.timestamp}>{comment.timestamp}</Text>
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  commentContainer: {
    flexDirection: 'row',
    minHeight: 80,
    paddingVertical: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  messageBubble: {
    backgroundColor: Colors.background.secondary,
    borderRadius: 16,
    padding: 12,
    marginBottom: 2,
  },
  authorName: {
    ...Typography.body2,
    fontFamily: Fonts.roboto.medium,
    color: Colors.text.primary,
    marginBottom: 2,
  },
  messageText: {
    ...Typography.body2,
    color: Colors.text.primary,
    lineHeight: 18,
  },
  mentionText: {
    ...Typography.body2,
    color: Colors.primary[500],
    fontFamily: Fonts.roboto.medium,
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 12,
    paddingTop: 2,
  },
  actionButton: {
    marginRight: 12,
  },
  actionText: {
    ...Typography.caption,
    color: Colors.text.secondary,
    fontFamily: Fonts.roboto.medium,
  },
  timestamp: {
    ...Typography.caption,
    color: Colors.text.secondary,
  },
});

export default CommentItem;
