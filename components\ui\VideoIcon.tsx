import Svg, { Path, SvgProps } from 'react-native-svg';

interface CustomSvgProps extends SvgProps {
  size?: number;
  color?: string;
}

const VideoIcon = ({ size = 24, color = '#CFD7ED', ...props }: CustomSvgProps) => (
  <Svg 
    width={size} 
    height={size} 
    viewBox="0 0 67 67" 
    fill="none" 
    {...props}>
    <Path
      fill={color}
      fillRule="evenodd"
      d="M32.7 44.602c-1.03.426-2.207.828-3.305.828-.851 0-1.65-.24-2.303-.893-.477-.477-1.942-1.939-1.901-11.596.037-9.607 1.441-11.007 1.901-11.47 1.62-1.617 4.3-.515 5.447-.038 2.828 1.17 13.9 7.269 13.9 11.573 0 4.372-11.313 10.59-13.74 11.596ZM33.548.038C8.844.038.086 8.796.086 33.5c0 24.703 8.758 33.462 33.462 33.462 24.707 0 33.462-8.759 33.462-33.462C67.01 8.796 58.255.038 33.548.038Z"
      clipRule="evenodd"
    />
  </Svg>
);

export default VideoIcon;
