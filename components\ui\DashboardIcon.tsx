import Svg, { Path, SvgProps } from 'react-native-svg';

interface CustomSvgProps extends SvgProps {
  size?: number;
  color?: string;
}

const DashboardIcon = ({ size = 24, color = '#CFD7ED', ...props }: CustomSvgProps) => (
  <Svg 
    width={size} 
    height={size} 
    viewBox="0 0 65 65" 
    fill="none" 
    {...props}>
    <Path
      fill={color}
      d="M64.129 13.52c0 7.156-5.801 12.956-12.957 12.956-7.155 0-12.956-5.8-12.956-12.956 0-7.155 5.801-12.956 12.956-12.956 7.156 0 12.957 5.8 12.957 12.956ZM26.04 13.52c0 7.156-5.8 12.956-12.955 12.956-7.156 0-12.956-5.8-12.956-12.956C.129 6.365 5.929.564 13.085.564c7.155 0 12.956 5.8 12.956 12.956ZM64.129 51.608c0 7.156-5.801 12.956-12.957 12.956-7.155 0-12.956-5.8-12.956-12.956 0-7.155 5.801-12.956 12.956-12.956 7.156 0 12.957 5.8 12.957 12.956ZM26.04 51.608c0 7.156-5.8 12.956-12.955 12.956-7.156 0-12.956-5.8-12.956-12.956 0-7.155 5.8-12.956 12.956-12.956 7.155 0 12.956 5.8 12.956 12.956Z"
    />
  </Svg>
);

export default DashboardIcon;
