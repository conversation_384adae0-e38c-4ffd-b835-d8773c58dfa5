import { useEvent } from 'expo';
import { useVideoPlayer, VideoView } from 'expo-video';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import LogoTagLine from './LogoTagLine';
import { useEffect, useState } from 'react';

interface VideoSplashProps {
  videoUri: string | number;
  onComplete: () => void;
  onSkip: () => void;
}

const { width, height } = Dimensions.get('window');

const VideoSplash = ({ videoUri, onComplete, onSkip }: VideoSplashProps) => {
  const [showSkip, setShowSkip] = useState(false);

  const player = useVideoPlayer(videoUri, player => {
    player.loop = false;
    player.play();

    player.addListener('playToEnd', () => {
      onComplete();
    });
  });

  const { isPlaying } = useEvent(player, 'playingChange', { isPlaying: player.playing });

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSkip(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      <VideoView
        style={styles.video}
        player={player}
        nativeControls={false}
        contentFit="cover"
      />

      <View style={styles.logoOverlay}>
        <LogoTagLine />
      </View>

      {showSkip && (
        <TouchableOpacity
          style={styles.skipButton}
          onPress={onSkip}
          className="absolute top-10 right-5 z-10">
          <Text style={styles.skipText}>Skip ads</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  video: {
    width,
    height,
    position: 'absolute',
  },
  logoOverlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 5,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  skipButton: {
    backgroundColor: "rgba(255,255,255,0.36)",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#FFFFFF',
    fontFamily: Fonts.roboto.regular
  },
  skipText: {
    color: '#FFFFFF',
    fontFamily: Fonts.lexend.medium,
    fontSize: 14,
  },
});

export default VideoSplash;
