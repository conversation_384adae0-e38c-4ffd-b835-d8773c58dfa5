import { ShimmerEffect } from '@/components';
import ImageAdvanced from '@/components/ui/ImageAdvanced';
import { ChatItem, detailedChatData } from '@/store/data/ChatData';
import HeaderImage from '@/store/data/HeaderImage';
import { Colors } from '@/utils/colors';
import { FlexLayouts, MainContainers, SafeAreaContainers, Spacing } from '@/utils/layoutStyles';
import { safeNavigate } from '@/utils/navigationUtils';
import { shadows } from '@/utils/shadows';
import { Ionicons } from '@expo/vector-icons';
import { memo, useCallback, useMemo, useState } from 'react';
import { FlatList, Pressable, StyleSheet, Text, View } from 'react-native';
import ChatThread from './components/ChatThread';
import DynamicGradientBackground from './components/DynamicMessageBackground';
import OnlineUsersList from './components/OnlineUsersList';

const Header = memo(() => {
  const handleAvatarPress = useCallback(() => {
    console.log('Avatar pressed');
  }, []);

  return (
    <View style={[FlexLayouts.rowBetween]}>
      <Text className="font-lexend-bold text-[20px] color-primary-500">WMessages</Text>
      <Pressable onPress={handleAvatarPress}>
        <ImageAdvanced
          source="https://plus.unsplash.com/premium_photo-1687294574010-dd69ecd54d01?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          style={styles.avatarImage}
          contentFit="cover">
          <View style={styles.onlineCircle} />
        </ImageAdvanced>
      </Pressable>
    </View>
  );
});

Header.displayName = 'Header';

const SearchBar = memo(() => {
  const handleSearchPress = useCallback(() => {
    safeNavigate('/search-message');
  }, []);

  return (
    <Pressable style={styles.searchBar} onPress={handleSearchPress}>
      <Ionicons name="search" size={20} color={Colors.text.muted} />
      <Text style={styles.searchPlaceholder}>Search your messages</Text>
    </Pressable>
  );
});

SearchBar.displayName = 'SearchBar';

const ListHeader = memo(() => (
  <View>
    <SearchBar />
    <OnlineUsersList />
  </View>
));

ListHeader.displayName = 'ListHeader';

const Background = memo(() => <DynamicGradientBackground imageUri={HeaderImage.uri} />);

Background.displayName = 'Background';

const MessageHomePage = () => {
  const [isLoading, setIsLoading] = useState(false);

  const shimmerConfig = {
    duration: 800,
    shimmerColors: [
      Colors.background.secondary,
      Colors.background.primary,
      Colors.background.secondary,
    ],
    variant: 'shimmer' as const,
    direction: 'leftToRight' as const,
  };

  const handleChatPress = useCallback((chatItem: ChatItem) => {
    const messagesJson = JSON.stringify(chatItem.messages);
    safeNavigate(`/chat?contactId=${encodeURIComponent(chatItem.id)}&contactName=${encodeURIComponent(chatItem.name)}&contactAvatar=${encodeURIComponent(chatItem.avatar)}&isOnline=${chatItem.isOnline}&messages=${encodeURIComponent(messagesJson)}`);
  }, []);

  const renderItem = useCallback(({ item }: { item: ChatItem }) => (
    <ChatThread item={item} onPress={() => handleChatPress(item)} />
  ), [handleChatPress]);

  const renderShimmerItem = useCallback(({ index }: { index: number }) => (
    <View key={index} style={styles.shimmerChatContainer}>
      <View style={[FlexLayouts.rowCenter, { gap: Spacing.horizontal.sm, flex: 1 }]}>
        {/* Avatar shimmer */}
        <ShimmerEffect
          isLoading={isLoading}
          {...shimmerConfig}
          style={styles.shimmerAvatar}
        />

        {/* Message content shimmer */}
        <View style={styles.shimmerMessageContainer}>
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerName}
          />
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerMessage}
          />
        </View>

        {/* Time shimmer */}
        <View style={styles.shimmerRightContainer}>
          <ShimmerEffect
            isLoading={isLoading}
            {...shimmerConfig}
            style={styles.shimmerTime}
          />
        </View>
      </View>
    </View>
  ), [isLoading, shimmerConfig]);

  const keyExtractor = useCallback((item: ChatItem) => item.id, []);

  const memoizedChatData = useMemo(() => detailedChatData, []);

  const handleFabPress = useCallback(() => {
    safeNavigate('/new-message');
  }, []);

  return (
    <View style={MainContainers.page}>
      <View style={SafeAreaContainers.topSafe}>
        <Background />
        <Header />
      </View>

      <FlatList
        data={isLoading ? Array(8).fill(null) : memoizedChatData}
        keyExtractor={isLoading ? (_, index) => `shimmer-${index}` : keyExtractor}
        renderItem={isLoading ? renderShimmerItem : renderItem}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={ListHeader}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={100}
        initialNumToRender={10}
        windowSize={10}
        getItemLayout={undefined}
      />

      <Pressable style={styles.fab} onPress={handleFabPress}>
        <Ionicons name="add" size={28} color={Colors.primary[500]} />
      </Pressable>
    </View>
  );
};

export default memo(MessageHomePage);

const styles = StyleSheet.create({
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderColor: 'white',
  },
  onlineCircle: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.success,
    borderWidth: 1,
    borderColor: 'white',
  },
  searchBar: {
    backgroundColor: Colors.background.primary,
    borderRadius: 9999,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 18,
    marginHorizontal: 16,
    marginBottom: 16,
    height: 53,
    ...FlexLayouts.rowCenter,
    gap: Spacing.horizontal.sm,
    ...shadows.medium,
  },
  searchPlaceholder: {
    color: Colors.text.muted,
    textAlign: 'center',
    fontSize: 16,
  },
  fab: {
    position: 'absolute',
    bottom: 100,
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.medium,
  },
  // Shimmer styles
  shimmerChatContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background.primary,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    ...shadows.medium,
  },
  shimmerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.secondary,
    overflow: 'hidden',
  },
  shimmerMessageContainer: {
    flex: 1,
    gap: 8,
  },
  shimmerName: {
    height: 16,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    width: '60%',
  },
  shimmerMessage: {
    height: 14,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
    width: '80%',
  },
  shimmerRightContainer: {
    alignItems: 'flex-end',
  },
  shimmerTime: {
    height: 12,
    width: 40,
    backgroundColor: Colors.background.secondary,
    borderRadius: 4,
  },
});
