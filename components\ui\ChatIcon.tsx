import Svg, { Path } from "react-native-svg";
import { CustomSvgProps } from "../../types/svgTypes";

const ChatIcon = ({ 
  size = 24, 
  color = "#CFD7ED", 
  ...props 
}: CustomSvgProps) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 66 65"
    fill="none"
    {...props}
  >
    <Path
      fill={color}
      fillRule="evenodd"
      d="M33.005.526C14.898.526.865 13.72.865 31.542c0 9.322 3.841 17.377 10.094 22.942.524.47.841 1.12.867 1.82l.175 5.688c.058 1.814 1.94 2.991 3.608 2.264l6.376-2.798a2.577 2.577 0 0 1 1.714-.129 35.126 35.126 0 0 0 9.306 1.229c18.107 0 32.14-13.195 32.14-31.016C65.145 13.72 51.112.526 33.005.526Z"
      clipRule="evenodd"
    />
  </Svg>
)

export default ChatIcon;