// Constants for threaded comments
import { Dimensions } from 'react-native';

const { height } = Dimensions.get('window');

export const ITEM_HEIGHT = 80;
export const SNAP_POINT_DEFAULT = height * 0.65; // Default snap point ensuring input visibility
export const SNAP_POINT_EXPANDED = height * 0.9; // Expanded view for more comments
export const CLOSE_THRESHOLD = height * 0.2;
export const INPUT_HEIGHT = 80;

// Empty state message
export const EMPTY_COMMENTS_MESSAGE = "No comments yet. Be the first to comment!";
