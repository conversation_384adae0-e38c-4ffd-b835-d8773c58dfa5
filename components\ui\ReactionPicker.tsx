import { Colors } from '@/utils/colors';
import { shadows } from '@/utils/shadows';
import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import {
  Animated,
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  Platform,
  AccessibilityInfo,
} from 'react-native';
import * as Haptics from 'expo-haptics';

export interface ReactionType {
  id: string;
  name: string;
  source?: any;
  color?: string;
  emoji?: string;
}

interface ReactionPickerProps {
  reactions: ReactionType[];
  onReactionSelect: (reaction: ReactionType) => void;
  onClose: () => void;
  visible: boolean;
  position: { x: number; y: number };
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const REACTION_SIZE = 45;
const PICKER_PADDING = 16;
const ANIMATION_DURATION = 200;
const STAGGER_DELAY = 50;
const MIN_EDGE_DISTANCE = 16;

const ReactionPicker: React.FC<ReactionPickerProps> = React.memo(
  ({ reactions, onReactionSelect, onClose, visible, position }) => {
    const scaleAnim = useRef(new Animated.Value(0)).current;
    const opacityAnim = useRef(new Animated.Value(0)).current;
    const reactionAnims = useRef(reactions.map(() => new Animated.Value(0))).current;
    const hasAnimatedIn = useRef(false);

    // Memoize picker dimensions and position calculations
    const pickerDimensions = useMemo(() => {
      const pickerWidth = reactions.length * REACTION_SIZE + PICKER_PADDING * 2;
      const pickerLeft = Math.max(
        MIN_EDGE_DISTANCE,
        Math.min(position.x - pickerWidth / 2, screenWidth - pickerWidth - MIN_EDGE_DISTANCE)
      );

      // Smart vertical positioning - try above first, then below if not enough space
      let pickerTop = position.y - 70;
      if (pickerTop < 60) {
        pickerTop = position.y + 50; // Position below if not enough space above
      }

      // Ensure picker doesn't go off bottom of screen
      pickerTop = Math.min(pickerTop, screenHeight - 120);

      return { width: pickerWidth, left: pickerLeft, top: pickerTop };
    }, [reactions.length, position.x, position.y]);

    // Memoize animation configurations
    const animationConfig = useMemo(
      () => ({
        spring: {
          useNativeDriver: true,
          tension: 120,
          friction: 8,
        },
        timing: {
          duration: ANIMATION_DURATION,
          useNativeDriver: true,
        },
      }),
      []
    );

    useEffect(() => {
      if (visible && !hasAnimatedIn.current) {
        hasAnimatedIn.current = true;

        // Haptic feedback
        if (Platform.OS === 'ios') {
          Haptics.selectionAsync?.();
        }

        Animated.parallel([
          Animated.spring(scaleAnim, {
            toValue: 1,
            ...animationConfig.spring,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            ...animationConfig.timing,
          }),
        ]).start();

        // Stagger reaction animations
        reactions.forEach((_, index) => {
          Animated.timing(reactionAnims[index], {
            toValue: 1,
            duration: ANIMATION_DURATION,
            delay: index * STAGGER_DELAY,
            useNativeDriver: true,
          }).start();
        });

        // Accessibility announcement
        AccessibilityInfo.announceForAccessibility('Reaction picker opened');
      } else if (!visible && hasAnimatedIn.current) {
        hasAnimatedIn.current = false;

        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }),
        ]).start();

        // Reset reaction animations
        reactionAnims.forEach((anim) => anim.setValue(0));

        AccessibilityInfo.announceForAccessibility('Reaction picker closed');
      }
    }, [visible, reactions, animationConfig, scaleAnim, opacityAnim, reactionAnims]);

    const handleReactionPress = useCallback(
      (reaction: ReactionType) => {
        // Haptic feedback
        if (Platform.OS === 'ios') {
          Haptics.impactAsync?.(Haptics.ImpactFeedbackStyle.Light);
        } else {
          Haptics.impactAsync?.(Haptics.ImpactFeedbackStyle.Light);
        }

        // Accessibility announcement
        AccessibilityInfo.announceForAccessibility(`Selected ${reaction.name} reaction`);

        onReactionSelect(reaction);
        onClose();
      },
      [onReactionSelect, onClose]
    );

    const handleOverlayPress = useCallback(() => {
      AccessibilityInfo.announceForAccessibility('Reaction picker closed');
      onClose();
    }, [onClose]);

    // Memoize reaction rendering
    const renderReaction = useCallback(
      (reaction: ReactionType, index: number) => (
        <Animated.View
          key={reaction.id}
          style={[
            styles.reactionContainer,
            {
              transform: [
                {
                  scale: reactionAnims[index],
                },
              ],
            },
          ]}>
          <TouchableOpacity
            style={[styles.reactionButton, reaction.color && { backgroundColor: reaction.color }]}
            onPress={() => handleReactionPress(reaction)}
            activeOpacity={0.7}
            accessible
            accessibilityRole="button"
            accessibilityLabel={`${reaction.name} reaction`}
            accessibilityHint="Tap to select this reaction">
            {reaction.source ? (
              <Image
                source={reaction.source}
                style={styles.reactionImage}
                accessibilityIgnoresInvertColors
              />
            ) : (
              <Text style={styles.reactionEmoji} accessibilityElementsHidden>
                {reaction.emoji}
              </Text>
            )}
          </TouchableOpacity>
        </Animated.View>
      ),
      [reactionAnims, handleReactionPress]
    );

    if (!visible) return null;

    return (
      <>
        {/* Enhanced overlay with accessibility */}
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleOverlayPress}
          accessible
          accessibilityRole="button"
          accessibilityLabel="Close reaction picker"
          accessibilityHint="Tap to close the reaction picker"
        />

        {/* Reaction Picker with smart positioning */}
        <Animated.View
          style={[
            styles.picker,
            {
              left: pickerDimensions.left,
              top: pickerDimensions.top,
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
            },
          ]}
          accessible
          accessibilityRole="menu"
          accessibilityLabel="Reaction picker">
          {reactions.map(renderReaction)}
        </Animated.View>
      </>
    );
  }
);

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  picker: {
    position: 'absolute',
    flexDirection: 'row',
    backgroundColor: Colors.background.primary,
    borderRadius: 30,
    paddingHorizontal: PICKER_PADDING,
    paddingVertical: 8,
    zIndex: 1000,
    ...shadows.large,
    elevation: 10,
  },
  reactionContainer: {
    marginHorizontal: 2,
  },
  reactionButton: {
    width: REACTION_SIZE,
    height: REACTION_SIZE,
    borderRadius: REACTION_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
  },
  reactionImage: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  reactionEmoji: {
    fontSize: Platform.select({ ios: 24, android: 22 }),
  },
});

ReactionPicker.displayName = 'ReactionPicker';

export default ReactionPicker;
