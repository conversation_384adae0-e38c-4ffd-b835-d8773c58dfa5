import ProfileData from '@/store/data/ProfileData';
import React, { memo, useMemo } from 'react';
import { Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Svg, { Path } from 'react-native-svg';
import { Colors } from '../../../utils/colors';
import { Fonts, Typography } from '../../../utils/fonts';
import { CommentInputProps } from './types';

// Beautiful Send Icon Component - Memoized to prevent re-renders
const SendIcon = memo(({ size = 20, color = Colors.primary[500] }: { size?: number; color?: string }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"
      fill={color}
    />
  </Svg>
));

const CommentInput: React.FC<CommentInputProps> = ({
  newComment,
  replyingTo,
  onCommentChange,
  onSubmit,
  onCancelReply,
  onInputPress,
  isExpanded = false,
  textInputRef,
}) => {
  const insets = useSafeAreaInsets();
  
  // Memoize dynamic styles to prevent useInsertionEffect warnings
  const sendButtonStyle = useMemo(() => [
    styles.sendButton, 
    { 
      backgroundColor: newComment.trim() 
        ? Colors.primary[500] 
        : Colors.background.muted,
      opacity: newComment.trim() ? 1 : 0.6 
    }
  ], [newComment]);
  
  const sendIconColor = useMemo(() => 
    newComment.trim() ? '#FFFFFF' : Colors.text.muted, 
    [newComment]
  );

  return (
    <View
      style={[
        styles.inputContainer,
        {
          paddingBottom: Math.max(insets.bottom, 12),
        },
      ]}>
      {/* Reply indicator */}
      {replyingTo && (
        <View style={styles.inputReplyIndicator}>
          <Text style={styles.inputReplyText}>Replying to {replyingTo.author}</Text>
          <TouchableOpacity onPress={onCancelReply}>
            <Text style={styles.inputCancelReplyText}>×</Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.inputRow}>
        <Image
          source={{
            uri: ProfileData.profile.avatar,
          }}
          style={styles.inputAvatar}
        />
        <View style={styles.inputWrapper}>
          <View style={styles.textInputContainer}>
            <TextInput
              ref={textInputRef}
              value={newComment}
              onChangeText={onCommentChange}
              placeholder="Add a comment..."
              placeholderTextColor={Colors.text.muted}
              style={styles.textInput}
              multiline
              maxLength={500}
              onSubmitEditing={onSubmit}
              returnKeyType="send"
            />
            {/* Invisible overlay to capture taps when not expanded */}
            {!isExpanded && (
              <TouchableOpacity
                style={styles.inputOverlay}
                onPress={onInputPress}
                activeOpacity={1}
              />
            )}
          </View>
          <TouchableOpacity
            onPress={onSubmit}
            disabled={!newComment.trim()}
            style={sendButtonStyle}>
            <SendIcon 
              size={16} 
              color={sendIconColor} 
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    backgroundColor: Colors.background.primary,
  },
  inputReplyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.primary[50],
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary[500],
  },
  inputReplyText: {
    ...Typography.body2,
    color: Colors.primary[700],
    fontFamily: Fonts.roboto.medium,
  },
  inputCancelReplyText: {
    fontSize: 16,
    color: Colors.primary[700],
    fontFamily: Fonts.roboto.bold,
    paddingHorizontal: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingBottom: 12,
  },
  inputAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 4,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderRadius: 99999,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxHeight: 100,
  },
  textInputContainer: {
    flex: 1,
    position: 'relative',
  },
  textInput: {
    flex: 1,
    ...Typography.body2,
    color: Colors.text.primary,
    maxHeight: 80,
    paddingVertical: 0,
  },
  inputOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  sendButton: {
    marginLeft: 8,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    width: 32,
    height: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default memo(CommentInput);
