import { useReactionManager } from '@/hooks/useReactionManager';
import { Colors } from '@/utils/colors';
import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  Image, 
  PanResponder, 
  StyleSheet, 
  Text, 
  TouchableOpacity, 
  View,
  Platform,
  AccessibilityInfo,
} from 'react-native';
import LikeIcon from './LikeIcon';
import LocalReactionPicker from './LocalReactionPicker';
import { ReactionType } from './ReactionPicker';

interface ReactLikeButtonProps {
  postId: number;
  initialReaction?: string | null;
  reactions: ReactionType[];
  style?: any;
  onReactionChange?: (postId: number, reactionType: string | null) => void;
}

const SCROLL_THRESHOLD = 10;
const MIN_PICKER_OPEN_TIME = 300;
const POSITION_CHECK_INTERVAL = 150;
const GESTURE_MOVEMENT_THRESHOLD = 10;

const ReactLikeButton: React.FC<ReactLikeButtonProps> = React.memo(({
  postId,
  initialReaction = null,
  reactions,
  style,
  onReactionChange,
}) => {
  const {
    currentReaction,
    showReactionPicker,
    handleLikePress,
    handleLongPressStart,
    handleLongPressEnd,
    handleReactionSelect,
    handleReactionPickerClose,
    getCurrentReactionAsset,
    isLiked,
    isDefaultLike,
  } = useReactionManager({ postId, initialReaction });

  const buttonRef = useRef<View>(null);
  const previousPosition = useRef({ y: 0 });
  const positionCheckInterval = useRef<NodeJS.Timeout | null>(null);
  const positionInitialized = useRef(false);
  const pickerOpenTime = useRef<number>(0);

  // Optimized reaction change callback
  const handleReactionChangeCallback = useCallback((newReaction: string | null) => {
    onReactionChange?.(postId, newReaction);
  }, [postId, onReactionChange]);

  useEffect(() => {
    handleReactionChangeCallback(currentReaction);
  }, [currentReaction, handleReactionChangeCallback]);

  // Enhanced scroll detection with better performance
  const setupScrollDetection = useCallback(() => {
    if (!showReactionPicker || !buttonRef.current) return;

    positionInitialized.current = false;
    previousPosition.current.y = 0;
    pickerOpenTime.current = Date.now();

    const checkPosition = () => {
      if (!buttonRef.current) return;

      buttonRef.current.measure((x, y, width, height, pageX, pageY) => {
        const currentY = pageY;
        
        if (!positionInitialized.current) {
          previousPosition.current.y = currentY;
          positionInitialized.current = true;
          return;
        }
        
        const deltaY = Math.abs(currentY - previousPosition.current.y);
        const timeSinceOpen = Date.now() - pickerOpenTime.current;
        
        // Only close if significant movement and minimum time has passed
        if (deltaY > SCROLL_THRESHOLD && timeSinceOpen > MIN_PICKER_OPEN_TIME) {
          handleReactionPickerClose(false);
        }
        
        previousPosition.current.y = currentY;
      });
    };

    // Add delay before starting position monitoring to avoid conflicts
    const startDelay = setTimeout(() => {
      if (showReactionPicker) {
        positionCheckInterval.current = setInterval(checkPosition, POSITION_CHECK_INTERVAL);
      }
    }, 300);

    return () => {
      clearTimeout(startDelay);
      if (positionCheckInterval.current) {
        clearInterval(positionCheckInterval.current);
        positionCheckInterval.current = null;
      }
      positionInitialized.current = false;
    };
  }, [showReactionPicker, handleReactionPickerClose]);

  useEffect(setupScrollDetection, [setupScrollDetection]);

  // Memoize current reaction asset for performance
  const currentReactionAsset = useMemo(() => 
    getCurrentReactionAsset(reactions), 
    [getCurrentReactionAsset, reactions]
  );

  // Enhanced gesture handling with better movement detection
  const panResponder = useMemo(() => PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: (evt, gestureState) => 
      Math.abs(gestureState.dx) > 5 || Math.abs(gestureState.dy) > 5,
    
    onPanResponderGrant: (evt) => {
      handleLongPressStart(evt);
    },
    
    onPanResponderMove: (evt, gestureState) => {
      // Cancel long press if user moves finger too much
      const movement = Math.abs(gestureState.dx) + Math.abs(gestureState.dy);
      if (movement > GESTURE_MOVEMENT_THRESHOLD) {
        handleLongPressEnd();
      }
    },
    
    onPanResponderRelease: (evt) => {
      handleLongPressEnd();
      
      // Only handle as tap if picker is not showing
      if (!showReactionPicker) {
        // Small delay to prevent conflicts with picker selection
        setTimeout(() => {
          if (!showReactionPicker) {
            handleLikePress(evt);
          }
        }, 50);
      }
    },
    
    onPanResponderTerminate: () => {
      handleLongPressEnd();
    },
  }), [handleLongPressStart, handleLongPressEnd, handleLikePress, showReactionPicker]);

  // Enhanced accessibility support
  const accessibilityLabel = useMemo(() => {
    if (currentReactionAsset) {
      return `${currentReactionAsset.name} reaction selected. Hold to change reaction.`;
    }
    return isLiked() ? 'Liked. Hold to change reaction.' : 'Like button. Tap to like or hold to see reaction options.';
  }, [currentReactionAsset, isLiked]);

  const handleOverlayPress = useCallback(() => {
    AccessibilityInfo.announceForAccessibility('Reaction picker closed');
    handleReactionPickerClose(false);
  }, [handleReactionPickerClose]);

  return (
    <View style={styles.container}>
      {/* Enhanced reaction picker with better overlay handling */}
      {showReactionPicker && (
        <>
          <TouchableOpacity
            style={styles.overlay}
            activeOpacity={1}
            onPress={handleOverlayPress}
            accessible
            accessibilityRole="button"
            accessibilityLabel="Close reaction picker"
            accessibilityHint="Tap to close the reaction picker"
          />
          <View style={styles.pickerContainer}>
            <LocalReactionPicker
              reactions={reactions}
              onReactionSelect={handleReactionSelect}
              onClose={() => handleReactionPickerClose(false)}
              visible={showReactionPicker}
            />
          </View>
        </>
      )}
      
      {/* Enhanced like button with better accessibility */}
      <View
        ref={buttonRef}
        {...panResponder.panHandlers}
        style={[
          styles.likeButton,
          isLiked() ? styles.likedButton : styles.defaultButton,
          style,
        ]}
        accessible
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel}
        accessibilityHint="Hold to see reaction options"
        accessibilityState={{ selected: isLiked() }}>
        {currentReactionAsset ? (
          currentReactionAsset.source ? (
            <Image 
              source={currentReactionAsset.source} 
              style={styles.reactionImage}
              accessibilityIgnoresInvertColors
            />
          ) : (
            <Text 
              style={styles.reactionEmoji}
              accessibilityElementsHidden
            >
              {currentReactionAsset.emoji}
            </Text>
          )
        ) : (
          <LikeIcon
            width={26}
            height={24}
            color={isLiked() ? 'white' : Colors.primary[950]}
          />
        )}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: -2000,
    left: -2000,
    right: -2000,
    bottom: -2000,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  pickerContainer: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    zIndex: 1000,
  },
  likeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  defaultButton: {
    backgroundColor: Colors.background.muted,
  },
  likedButton: {
    backgroundColor: Colors.primary[500],
  },
  reactionImage: {
    width: 26,
    height: 24,
    resizeMode: 'contain',
  },
  reactionEmoji: {
    fontSize: Platform.select({ ios: 20, android: 18 }),
  },
});

ReactLikeButton.displayName = 'ReactLikeButton';

export default ReactLikeButton;