import Input from '@/components/authentication/Input';
import { ExpandableButton } from '@/components/button/Button';
import ButtonPrimary from '@/components/ui/ButtonPrimary';
import AgreementCheckbox from '@/pages/authentication/components/AgreementCheckbox';
import LogoTagLine from '@/pages/splash/components/LogoTagLine';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { router } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, ImageBackground, Pressable, StyleSheet, Text, useWindowDimensions, View } from 'react-native';

const AuthenticationScreen = () => {
  const logoPosition = useRef(new Animated.Value(0)).current;
  const formOpacity = useRef(new Animated.Value(0)).current;
  const [isChecked, setChecked] = useState(false);
  const [primaryLoading, setPrimaryLoading] = useState<boolean>(false);
  const width = useWindowDimensions().width;

  const handlePrimaryPress = useCallback<() => any | Function>(() => {
    setPrimaryLoading(true);
    setTimeout(() => {
      setPrimaryLoading(false);
      router.push('/(tabs)/newsfeed');
    }, 2000);
  }, []);
  useEffect(() => {
    Animated.timing(logoPosition, {
      toValue: 1,
      duration: 800,
      delay: 100,
      useNativeDriver: false,
    }).start();

    Animated.timing(formOpacity, {
      toValue: 1,
      duration: 800,
      delay: 500,
      useNativeDriver: true,
    }).start();
  }, [formOpacity, logoPosition]);

  const logoTranslateY = logoPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -220],
  });

  const logoScale = logoPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0.8],
  });

  return (
    <View className={'flex-1 bg-background-primary'}>
      <View className="absolute left-0 top-0 w-full" style={{ height: '80%', marginTop: -80 }}>
        <ImageBackground
          source={require('@/assets/images/Gradient Background.png')}
          style={{ width: '100%', height: '100%' }}
          resizeMode="cover"></ImageBackground>
      </View>

      <View style={styles.placeholderContainer} className={'h-2/5'} />

      <Animated.View
        style={[
          styles.logoContainer,
          {
            transform: [{ translateY: logoTranslateY }, { scale: logoScale }],
          },
        ]}>
        <LogoTagLine />
      </Animated.View>

      <Animated.View
        style={{ paddingLeft: 14, paddingRight: 14, opacity: formOpacity, width: '100%', flex: 1 }}>
        <View className="mb-4 gap-4">
          <Input placeholder={'Enter your email/phone number'} inputType={'text'} />
          <Input placeholder={'Enter your password'} inputType={'password'} />
          <AgreementCheckbox isChecked={isChecked} setChecked={setChecked} />
        </View>

        <View style={styles.buttonsContainer}>
          {/* <ButtonPrimary
            bgColor={Colors.primary[500]}
            border={'0px solid'}
            borderColor="transparent">
            <Text className={'text-[16px] text-white'}>Log In</Text>
          </ButtonPrimary> */}
          <ExpandableButton
            title="Login"
            isLoading={primaryLoading}
            onPress={handlePrimaryPress}
            width={width - 30}
            height={60}
            backgroundColor={Colors.primary[950]}
            textColor="white"
            fontSize={16}
            iconColor="white"
            borderRadius={16}
            withPressAnimation={true}
            textStyle={{ 
              fontFamily: Fonts.roboto.regular,
              color: 'white',
            }}
            loadingIndicatorColor="white"
            animationConfig={{
              damping: 15,
              stiffness: 150,
              duration: 300,
            }}
            disabled={false}
          />
        </View>

        <View style={styles.bottomContainer}>
          <View className={'flex-row items-center justify-center gap-1'}>
            <Text className={'text-[12px] text-text-muted'}>Forgot password? Go to</Text>
            <Pressable onPress={() => router.push('/account-recovery')}>
              <Text className={'text-[12px] text-primary-500'}>Account Recovery</Text>
            </Pressable>
          </View>
          <ButtonPrimary
            bgColor={Colors.background.primary}
            border="1px solid"
            borderColor={Colors.text.muted}
            enableShadow={false}
            onPress={() => router.push('/signup')}>
            <Text className={'text-[16px] text-black'}>Sign Up</Text>
          </ButtonPrimary>
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  logoContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    marginTop: -64,
    height: 128,
    zIndex: 10,
  },
  placeholderContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 12,
  },
  buttonsContainer: {
    marginBottom: 24,
  },
  bottomContainer: {
    gap: 16,
    paddingBottom: 16,
    marginTop: 'auto',
  },
});

export default AuthenticationScreen;
