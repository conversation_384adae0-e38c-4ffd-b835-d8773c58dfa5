{"expo": {"name": "WConnect", "slug": "WConnect", "version": "1.1.beta", "orientation": "portrait", "icon": "./assets/images/logo/Logo.png", "scheme": "wconnect", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"], "edgeToEdgeEnabled": true, "package": "com.techfox.wconnect"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/adaptive-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "expo-video", "expo-audio"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "32c73dad-b9d5-4508-ae2e-69db0bbe2174"}}}}