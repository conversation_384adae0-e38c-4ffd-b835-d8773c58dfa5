import CameraIcon from '@/components/ui/CameraIcon';
import MediaSelector from '@/components/ui/MediaSelector';
import { Colors } from '@/utils/colors';
import { shadows } from '@/utils/shadows';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

interface MediaUtilsProps {
  onCamera?: () => void;
  onMention?: () => void;
  onVoice?: () => void;
  onEmoji?: () => void;
  onMediaSelected?: (mediaItems: any[]) => void;
  disableMediaUpload?: boolean;
}

const MediaUtils: React.FC<MediaUtilsProps> = ({
  onCamera,
  onMention,
  onVoice,
  onEmoji,
  onMediaSelected,
  disableMediaUpload,
}) => {
  return (
    <View style={styles.container}>
      {onMediaSelected ? (
        <MediaSelector
          onMediaSelected={onMediaSelected}
          iconSize={30}
          iconColor={disableMediaUpload ? Colors.text.muted : Colors.primary[500]}
          showText={false}
          style={styles.mediaSelector}
          disabled={disableMediaUpload}
          disabledTitle="Media Upload Disabled"
          disabledMessage="Media upload is not available when using themed backgrounds. Please remove the theme to add media."
        />
      ) : (
        <TouchableOpacity onPress={onCamera} disabled={disableMediaUpload}>
          <CameraIcon 
            size={30} 
            color={disableMediaUpload ? Colors.text.muted : Colors.primary[500]} 
          />
        </TouchableOpacity>
      )}
      <TouchableOpacity onPress={onMention}>
        <Ionicons name="at" size={30} color={Colors.primary[500]} />
      </TouchableOpacity>
      <TouchableOpacity onPress={onVoice}>
        <Ionicons name="mic" size={30} color={Colors.primary[500]} />
      </TouchableOpacity>
      <TouchableOpacity onPress={onEmoji}>
        <Ionicons name="happy" size={30} color={Colors.primary[500]} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 80,
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'white',
    borderRadius: 12,
    marginTop: 10,
    ...shadows.large,
  },
  mediaSelector: {
    backgroundColor: 'transparent',
    padding: 0,
  },
});

export default MediaUtils;
