import BookmarkIcon from '@/components/ui/BookmarkIcon';
import ChatHelpIcon from '@/components/ui/ChatHelpIcon';
import ContainerTopNavigationTitle from '@/components/ui/ContainerTopNavigationTitle';
import FriendsIcon from '@/components/ui/FriendsIcon';
import MarketIcon from '@/components/ui/MarketIcon';
import MemoryIcon from '@/components/ui/MemoryIcon';
import PolicyIcon from '@/components/ui/PolicyIcon';
import VideoIcon from '@/components/ui/VideoIcon';
import { useScreenDimensions } from '@/hooks/useScreenDimensions';
import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { FlexLayouts, MainContainers, SafeAreaContainers, Spacing } from '@/utils/layoutStyles';
import { safeNavigate } from '@/utils/navigationUtils';
import { Ionicons } from '@expo/vector-icons';
import { useMemo } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import ProfileSwitchHeader from './components/ProfileSwitchHeader';
import ShortcutButton from './components/ShortcutButton';

const DashboardPage = () => {
  const { width: screenWidth } = useScreenDimensions();

  const buttonDimensions = useMemo(() => {
    const HORIZONTAL_PADDING = 32;
    const GAP = Spacing.gap.lg;
    const availableWidth = screenWidth - HORIZONTAL_PADDING;
    const buttonWidth = Math.floor((availableWidth - GAP) / 2);

    return {
      width: buttonWidth,
      height: 65,
    };
  }, [screenWidth]);

  return (
    <ScrollView
      style={[MainContainers.page]}
      contentContainerStyle={[
        { paddingBottom: 100, gap: Spacing.vertical.md },
        SafeAreaContainers.topSafeMargin,
      ]}>
      <ContainerTopNavigationTitle
        isBackButton={false}
        title={'Menu'}
        sideButton={
          <View className="h-full flex-row items-center justify-between gap-3">
            <Pressable
              style={{
                backgroundColor: Colors.background.muted,
              }}
              className="h-11 w-11 items-center justify-center rounded-full"
              onPress={() => {
                console.log('Settings pressed');
              }}>
              <Ionicons name="settings" size={24} color={Colors.secondary['950']} />
            </Pressable>
          </View>
        }></ContainerTopNavigationTitle>
      <ProfileSwitchHeader></ProfileSwitchHeader>
      <View style={[FlexLayouts.rowBetween]}>
        <Text style={styles.sectionTitle}>Shortcuts</Text>
        <Text style={styles.seeAll}>See all</Text>
      </View>
      <View style={styles.gridContainer}>
        <ShortcutButton
          icon={<FriendsIcon size={24} />}
          label={'Friend requests'}
          width={buttonDimensions.width}
          height={buttonDimensions.height}
          isNotified={true}
        />
        <ShortcutButton
          icon={<VideoIcon size={24} color={Colors.primary[500]} />}
          label={'WPlay'}
          width={buttonDimensions.width}
          height={buttonDimensions.height}
          isNotified={true}
        />
        <ShortcutButton
          icon={<MemoryIcon size={24} />}
          label={'Memories'}
          width={buttonDimensions.width}
          height={buttonDimensions.height}
          isNotified={true}
        />
        <ShortcutButton
          icon={<MarketIcon size={24} />}
          label={'Shop'}
          width={buttonDimensions.width}
          height={buttonDimensions.height}
          isNew={true}
        />
        <ShortcutButton
          icon={<BookmarkIcon size={24} />}
          label={'Saved posts'}
          width={buttonDimensions.width}
          height={buttonDimensions.height}
          isNotified={true}
        />
      </View>
      <Text style={styles.sectionTitle}>Help and support</Text>
      <ShortcutButton
        icon={<ChatHelpIcon size={24} color={Colors.text.muted} />}
        label={'Help center'}
        labelStyle={{ color: Colors.text.muted }}
        style={{
          width: '100%',
          height: 70,
        }}
      />
      <ShortcutButton
        icon={<PolicyIcon size={24} color={Colors.text.muted} />}
        label={'Terms & Policies'}
        labelStyle={{ color: Colors.text.muted }}
        style={{
          width: '100%',
          height: 70,
        }}
      />
      <ShortcutButton
        icon={<Ionicons name="log-out" size={24} color={Colors.error} />}
        label={'Log out'}
        labelStyle={{ color: Colors.error }}
        style={{
          width: '100%',
          height: 70,
        }}
        onPress={() => {
          safeNavigate('/login');
        }}
      />
    </ScrollView>
  );
};

export default DashboardPage;

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 18,
    fontFamily: Fonts.roboto.bold,
    color: Colors.secondary[950],
  },
  seeAll: {
    fontSize: 14,
    fontFamily: Fonts.roboto.bold,
    color: Colors.primary[600],
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.gap.lg,
  },
});
