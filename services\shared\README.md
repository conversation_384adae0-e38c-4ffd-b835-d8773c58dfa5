# Shared Services

This folder contains shared service utilities and common functionality used across the application.

## Purpose
- Common utility services that don't fit into specific categories
- Cross-cutting concerns like logging, analytics, and monitoring
- Device-specific services (camera, location, notifications)
- Storage services (AsyncStorage, SecureStore)
- Configuration and environment management

## Examples
- `storage.ts` - Local storage abstraction
- `logger.ts` - Logging service
- `analytics.ts` - Analytics tracking
- `permissions.ts` - Device permissions handling
- `config.ts` - App configuration management
