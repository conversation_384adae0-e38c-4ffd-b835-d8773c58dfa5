import ReactLikeButton from '@/components/ui/ReactLikeButton';
import { Alert, StyleSheet, Text, View } from 'react-native';

const QuickTouchTest = () => {
  const mockReactions = [
    {
      name: 'Like',
      id: 'like',
      source: require('../assets/images/icons/Like.png'),
    },
    {
      name: 'Heart',
      source: require('../assets/images/icons/Heart.png'),
      id: 'heart',
    },
    {
      name: 'Laugh',
      source: require('../assets/images/icons/Warm.png'),
      id: 'laugh',
    },
    {
      name: 'Sad',
      id: 'sad',
      emoji: '😢',
    },
    {
      name: 'Angry',
      id: 'angry',
      emoji: '😡',
    },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Quick Touch & Hold Test</Text>
      
      <View style={styles.instructions}>
        <Text style={styles.instructionText}>Test scenarios:</Text>
        <Text style={styles.bullet}>• Very fast touch and hold</Text>
        <Text style={styles.bullet}>• Rapid multiple touches</Text>
        <Text style={styles.bullet}>• Quick touch and immediate release</Text>
        <Text style={styles.bullet}>• Touch and hold, then quick movement</Text>
      </View>

      <View style={styles.testArea}>
        <Text style={styles.testLabel}>Test Button:</Text>
        <ReactLikeButton
          postId={1}
          reactions={mockReactions}
          onReactionChange={(postId, reactionType) => {
            Alert.alert('Reaction Changed', `Reaction: ${reactionType || 'None'}`);
          }}
        />
      </View>

      <View style={styles.fixes}>
        <Text style={styles.fixesTitle}>Applied Fixes:</Text>
        <Text style={styles.bullet}>✓ 300ms delay before position monitoring</Text>
        <Text style={styles.bullet}>✓ 500ms minimum picker open time</Text>
        <Text style={styles.bullet}>✓ Increased scroll detection threshold (10px)</Text>
        <Text style={styles.bullet}>✓ Position initialization safeguard</Text>
        <Text style={styles.bullet}>✓ Long press state tracking</Text>
        <Text style={styles.bullet}>✓ Slower position check interval (150ms)</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  instructions: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  bullet: {
    fontSize: 14,
    marginLeft: 8,
    marginBottom: 4,
    color: '#666',
  },
  testArea: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  testLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  fixes: {
    backgroundColor: '#e8f5e8',
    padding: 16,
    borderRadius: 8,
  },
  fixesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#2d5a2d',
  },
});

export default QuickTouchTest;
