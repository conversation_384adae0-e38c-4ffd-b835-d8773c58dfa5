import { Colors } from '@/utils/colors';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import DropDownItem from './DropdownItem';

export interface PrivacySettings {
  audience: string;
  exclusivity: string;
}

interface PostPrivacyControlsProps {
  onAudienceChange?: (value: string) => void;
  onExclusivityChange?: (value: string) => void;
}

const PostPrivacyControls: React.FC<PostPrivacyControlsProps> = ({
  onAudienceChange,
  onExclusivityChange,
}) => {
  const audienceData = [
    { label: 'Public', value: '1' },
    { label: 'Friends', value: '2' },
    { label: 'Private', value: '3' },
  ];

  const exclusiveData = [
    { label: 'Normal', value: '1' },
    { label: 'Exclusive', value: '2' },
  ];

  return (
    <View style={styles.container}>
      <DropDownItem
        data={audienceData}
        backgroundColor={Colors.primary[500]}
        placeholder="Privacy"
        iconName="earth"
        onSelectionChange={onAudienceChange}
      />
      <DropDownItem
        data={exclusiveData}
        gradientColors={['#3b82f6', '#E20071']}
        placeholder="Exclusivity"
        iconName="lock-closed"
        onSelectionChange={onExclusivityChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    paddingTop: 4,
    paddingBottom: 10
  },
});

export default PostPrivacyControls;
