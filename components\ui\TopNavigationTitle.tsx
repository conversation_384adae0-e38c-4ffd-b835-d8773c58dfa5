import { Colors } from '@/utils/colors';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Platform, StatusBar, View, Text } from 'react-native';

interface TopNavigationTitleProps {
  title: string;
  isAdditionButton?: boolean;
  onAdditionActions?: () => void;
}

const TopNavigationTitle = ({
  title,
  isAdditionButton = false,
  onAdditionActions,
}: TopNavigationTitleProps) => {
  return (
    <View className="flex-row justify-between">
      <View className="item-center flex-row justify-center gap-2">
        <Ionicons
          name="arrow-back"
          size={24}
          color={Colors.primary['950']}
          onPress={() => {
            router.back();
          }}
        />
        <Text className="font-roboto-bold text-2xl color-primary-500">{title}</Text>
      </View>
      <View>
        {isAdditionButton && (
          <Ionicons
            name="checkmark"
            size={24}
            color={Colors.primary['950']}
            onPress={onAdditionActions}
          />
        )}
      </View>
    </View>
  );
};

export default TopNavigationTitle;
