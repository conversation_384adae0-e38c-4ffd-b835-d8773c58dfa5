import { ImageAdvanced } from "@/components";
import { Fonts } from "@/utils/fonts";
import { FlexLayouts, Spacing } from "@/utils/layoutStyles";
import { useRef, useState } from "react";
import { Animated, StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface PeopleThreadProps {
    item: {
        id: string;
        name: string;
        avatar: any;
    }
    onPress?: (item: any) => void;
    onLongPress?: (item: any) => void;
    disabled?: boolean;
}

const PeopleThread = ({ item, onPress, onLongPress, disabled = false }: PeopleThreadProps) => {
    const scaleValue = useRef(new Animated.Value(1)).current;
    const [isPressed, setIsPressed] = useState(false);

    const handlePressIn = () => {
        if (disabled) return;
        setIsPressed(true);
        Animated.spring(scaleValue, {
            toValue: 0.98,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        if (disabled) return;
        setIsPressed(false);
        Animated.spring(scaleValue, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    };

    const handlePress = () => {
        if (disabled) return;
        onPress?.(item);
    };

    const handleLongPress = () => {
        if (disabled) return;
        onLongPress?.(item);
    };

    return (
        <TouchableOpacity
            onPress={handlePress}
            onLongPress={handleLongPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            delayLongPress={500}
            disabled={disabled}
            activeOpacity={1}
            style={[styles.touchableContainer, disabled && styles.disabledContainer]}
        >
            <Animated.View
                style={[
                    styles.chatContainer,
                    { transform: [{ scale: scaleValue }] },
                    isPressed && styles.pressedContainer,
                    disabled && styles.disabledContent
                ]}
            >
                <ImageAdvanced
                    source={item.avatar}
                    style={styles.avatar}
                    contentFit="cover"
                />
                <View>
                    <Text
                        style={
                            styles.nameText
                        }
                        numberOfLines={1}
                    >
                        {item.name}
                    </Text>
                </View>
            </Animated.View>
        </TouchableOpacity>
    )
}

export default PeopleThread

const styles = StyleSheet.create({
    touchableContainer: {
        overflow: 'hidden',
    },
    disabledContainer: {
        opacity: 0.5,
    },
    chatContainer: {
        ...FlexLayouts.rowStart,
        gap: Spacing.horizontal.sm,
        flex: 1,
    },
    pressedContainer: {
        transform: [{ scale: 0.98 }],
    },
    disabledContent: {
        backgroundColor: 'rgba(0, 0, 0, 0.02)',
    },
    avatar: {
        width: 45,
        height: 45,
        borderRadius: 28,
    },
    nameText: {
        fontSize: 16,
        marginBottom: 2,
        fontFamily: Fonts.roboto.bold,
    },
})