import { Colors } from '@/utils/colors';
import { shadows } from '@/utils/shadows';
import * as Haptics from 'expo-haptics';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import {
    AccessibilityInfo,
    Animated,
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
} from 'react-native';
import { ReactionType } from './ReactionPicker';

interface LocalReactionPickerProps {
  reactions: ReactionType[];
  onReactionSelect: (reaction: ReactionType) => void;
  onClose: () => void;
  visible: boolean;
}

const REACTION_SIZE = 45;
const PICKER_PADDING = 16;
const ANIMATION_DURATION = 200;
const STAGGER_DELAY = 50;

const LocalReactionPicker: React.FC<LocalReactionPickerProps> = React.memo(({
  reactions,
  onReactionSelect,
  onClose,
  visible,
}) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const reactionAnims = useRef(reactions.map(() => new Animated.Value(0))).current;
  const hasAnimatedIn = useRef(false);

  // Memoize animation configurations
  const animationConfig = useMemo(() => ({
    spring: {
      useNativeDriver: true,
      tension: 120,
      friction: 8,
    },
    timing: {
      duration: ANIMATION_DURATION,
      useNativeDriver: true,
    },
  }), []);

  useEffect(() => {
    if (visible && !hasAnimatedIn.current) {
      hasAnimatedIn.current = true;
      
      // Haptic feedback on show
      Haptics.selectionAsync();

      // Start from larger initial scale for better responsiveness
      scaleAnim.setValue(0.3);
      reactionAnims.forEach(anim => anim.setValue(0.5));
      
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          ...animationConfig.spring,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          ...animationConfig.timing,
        }),
      ]).start();

      // Stagger reaction animations for better visual effect
      reactions.forEach((_, index) => {
        Animated.timing(reactionAnims[index], {
          toValue: 1,
          duration: ANIMATION_DURATION,
          delay: index * STAGGER_DELAY,
          useNativeDriver: true,
        }).start();
      });

      // Accessibility announcement
      AccessibilityInfo.announceForAccessibility('Reaction picker opened');
    } else if (!visible && hasAnimatedIn.current) {
      hasAnimatedIn.current = false;
      
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();

      // Reset animations
      reactionAnims.forEach(anim => anim.setValue(0));
      
      AccessibilityInfo.announceForAccessibility('Reaction picker closed');
    }
  }, [visible, reactions, animationConfig, scaleAnim, opacityAnim, reactionAnims]);

  const handleReactionPress = useCallback((reaction: ReactionType) => {
    // Haptic feedback on selection
    Haptics.impactAsync?.(Haptics.ImpactFeedbackStyle.Light);
    
    // Accessibility announcement
    AccessibilityInfo.announceForAccessibility(
      `Selected ${reaction.name} reaction`
    );
    
    onReactionSelect(reaction);
    
    // Immediate close animation for better responsiveness
    if (visible) {
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [onReactionSelect, visible, scaleAnim, opacityAnim]);

  // Memoize reaction rendering for performance
  const renderReaction = useCallback((reaction: ReactionType, index: number) => (
    <Animated.View
      key={reaction.id}
      style={[
        styles.reactionContainer,
        {
          transform: [{
            scale: reactionAnims[index],
          }],
        },
      ]}>
      <TouchableOpacity
        style={[
          styles.reactionButton,
          reaction.color && { backgroundColor: reaction.color },
        ]}
        onPress={() => handleReactionPress(reaction)}
        activeOpacity={0.7}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        accessible
        accessibilityRole="button"
        accessibilityLabel={`${reaction.name} reaction`}
        accessibilityHint="Tap to select this reaction">
        {reaction.source ? (
          <Image 
            source={reaction.source} 
            style={styles.reactionImage}
            accessibilityIgnoresInvertColors
          />
        ) : (
          <Text 
            style={styles.reactionEmoji}
            accessibilityElementsHidden
          >
            {reaction.emoji}
          </Text>
        )}
      </TouchableOpacity>
    </Animated.View>
  ), [reactionAnims, handleReactionPress]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.picker,
        {
          transform: [{ scale: scaleAnim }],
          opacity: opacityAnim,
        },
      ]}
      accessible
      accessibilityRole="menu"
      accessibilityLabel="Reaction picker">
      {reactions.map(renderReaction)}
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  picker: {
    flexDirection: 'row',
    backgroundColor: Colors.background.primary,
    borderRadius: 30,
    paddingHorizontal: PICKER_PADDING,
    paddingVertical: 8,
    ...shadows.large,
    elevation: 10,
  },
  reactionContainer: {
    marginHorizontal: 2,
  },
  reactionButton: {
    width: REACTION_SIZE,
    height: REACTION_SIZE,
    borderRadius: REACTION_SIZE / 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
  },
  reactionImage: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  reactionEmoji: {
    fontSize: Platform.select({ ios: 24, android: 22 }),
  },
});

LocalReactionPicker.displayName = 'LocalReactionPicker';

export default LocalReactionPicker;