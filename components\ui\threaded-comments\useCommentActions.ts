import { useCallback, useRef, useState } from 'react';
import { FlatList } from 'react-native';
import { Comment, FlattenedComment, ReplyingTo } from './types';
import { addReplyToComment, updateCommentLikes } from './utils';

export const useCommentActions = (initialComments: Comment[] = [], expandToFull?: (callback?: () => void) => void, isExpanded?: boolean) => {
  const [newComment, setNewComment] = useState<string>('');
  const [replyingTo, setReplyingTo] = useState<ReplyingTo | null>(null);
  const [comments, setComments] = useState<Comment[]>(initialComments);

  const textInputRef = useRef<any>(null);
  const flatListRef = useRef<FlatList>(null);

  const handleReply = useCallback((commentId: number, authorName: string) => {
    setReplyingTo({ id: commentId, author: authorName });
    
    // Use the two-step process if expansion function is provided
    if (expandToFull && !isExpanded) {
      // First expand to 90%, then focus input
      expandToFull(() => {
        textInputRef.current?.focus();
      });
    } else {
      // Already expanded or no expansion function, just focus
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    }
  }, [expandToFull, isExpanded]);

  const handleLike = useCallback((commentId: number) => {
    setComments((prev: Comment[]) => updateCommentLikes(prev, commentId));
  }, []);

  const addComment = useCallback(() => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now(),
      author: 'You',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face',
      text: replyingTo ? `@${replyingTo.author} ${newComment}` : newComment,
      timestamp: 'now',
      likes: 0,
      replies: [],
    };

    if (replyingTo) {
      setComments((prev: Comment[]) => addReplyToComment(prev, replyingTo.id, comment));
      setReplyingTo(null);

      setTimeout(() => {
        const flatData = flatListRef.current?.props.data as FlattenedComment[];
        if (flatData) {
          const replyIndex = flatData.findIndex(
            (item) => item.comment.id === comment.id && item.depth > 0
          );
          if (replyIndex !== -1) {
            flatListRef.current?.scrollToIndex({
              index: replyIndex,
              animated: true,
              viewPosition: 0.5,
            });
          }
        }
      }, 300);
    } else {
      setComments((prev: Comment[]) => [comment, ...prev]);
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({
          offset: 0,
          animated: true,
        });
      }, 100);
    }
    setNewComment('');
  }, [newComment, replyingTo]);

  const cancelReply = useCallback(() => {
    setReplyingTo(null);
  }, []);

  const resetComments = useCallback(() => {
    setReplyingTo(null);
    setNewComment('');
  }, []);

  return {
    newComment,
    setNewComment,
    replyingTo,
    comments,
    textInputRef,
    flatListRef,
    handleReply,
    handleLike,
    addComment,
    cancelReply,
    resetComments,
  };
};
