import {View, StyleSheet} from "react-native";
import {Colors} from "@/utils/colors";
import LogoTagLine from "@/pages/splash/components/LogoTagLine";
import VideoSplash from "@/pages/splash/components/VideoSplash";
import {useState, useEffect} from "react";

interface LogoSplashScreenProps {
    onComplete: () => void;
}

const LogoSplashScreen = ({onComplete}: LogoSplashScreenProps) => {
    const [hasAd, setHasAd] = useState(false);
    const [adVideoUri, setAdVideoUri] = useState("");

    useEffect(() => {

        const shouldShowAd = Math.random() > 0.5;
        const ads = [
            require('@/assets/videos/ads.mp4'),
            require('@/assets/videos/ads2.mp4'),
            require('@/assets/videos/ads3.mp4'),
            require('@/assets/videos/ads4.mp4'),
        ];

        const randomAd = ads[Math.floor(Math.random() * ads.length)];

        if (shouldShowAd) {
            setAdVideoUri(randomAd);
            setHasAd(true);
        } else {
            const timer = setTimeout(() => {
                onComplete();
            }, 2000);

            return () => clearTimeout(timer);
        }
    }, [onComplete]);

    if (hasAd && adVideoUri) {
        return (
            <VideoSplash
                videoUri={adVideoUri}
                onComplete={onComplete}
                onSkip={onComplete}
            />
        );
    }

    return (
        <View style={styles.container} className="flex-1 items-center justify-center">
            <LogoTagLine/>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.background.primary,
    }
});

export default LogoSplashScreen;
