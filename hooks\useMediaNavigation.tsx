import { MediaData } from '@/types/media';
import { useRouter } from 'expo-router';
import { useCallback } from 'react';

export const useMediaNavigation = () => {
  const router = useRouter();

  const openMediaOverlay = useCallback((media: MediaData[], focusIndex: number = 0) => {
    const mediaString = JSON.stringify(media);
    router.push({
      pathname: '/media-modal',
      params: {
        media: mediaString,
        focusIndex: focusIndex.toString(),
      },
    });
  }, [router]);

  return { openMediaOverlay };
};

export default useMediaNavigation;
