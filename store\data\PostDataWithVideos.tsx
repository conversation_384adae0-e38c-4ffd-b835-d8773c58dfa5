// Example data structure for posts with video support
const PostDataWithVideos = [
  {
    id: 1,
    name: '<PERSON>',
    avatar:
      'https://images.unsplash.com/photo-1504150558240-0b4fd8946624?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    privacyStatus: '1',
    timestamp: '2025-07-12T07:30:00Z',
    content: "Amazing sunset at the beach! 🌅 Such a peaceful evening. Nature always finds a way to surprise us. #sunset #beach #nature",
    media: [
      {
        url: 'https://cdn.pixabay.com/video/2025/02/27/261244_large.mp4',
        type: 'video' as const,
        thumbnail: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=870&auto=format&fit=crop'
      },
      {
        url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        type: 'image' as const
      }
    ],
    interaction: {
      likes: 320,
      hearts: 89,
      sad: 2,
      angry: 0,
      comments: 45,
      shares: 23,
    },
    isInteracted: {
      liked: true,
      hearted: false,
      sad: false,
      angry: false,
    },
    comments: []
  },
  {
    id: 2,
    name: 'Sarah Johnson',
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616b1e05f1e?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    privacyStatus: '1',
    timestamp: '2025-07-12T09:15:00Z',
    content: "Check out this amazing cooking video! 👨‍🍳 Learn how to make the perfect pasta from scratch. #cooking #pasta #recipe",
    media: [
      {
        url: 'https://cdn.pixabay.com/video/2025/02/27/261244_large.mp4',
        type: 'video' as const,
        thumbnail: 'https://images.unsplash.com/photo-1551782450-17144efb9c50?q=80&w=869&auto=format&fit=crop'
      }
    ],
    interaction: {
      likes: 156,
      hearts: 42,
      sad: 0,
      angry: 1,
      comments: 18,
      shares: 8,
    },
    isInteracted: {
      liked: false,
      hearted: true,
      sad: false,
      angry: false,
    },
    comments: []
  },
  {
    id: 3,
    name: 'Mike Wilson',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    privacyStatus: '1',
    timestamp: '2025-07-12T11:45:00Z',
    content: "Beautiful mountain hiking adventure! 🏔️ The views were absolutely breathtaking. Here are some photos from today's hike.",
    media: [
      {
        url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        type: 'image' as const
      },
      {
        url: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        type: 'image' as const
      },
      {
        url: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        type: 'image' as const
      }
    ],
    interaction: {
      likes: 89,
      hearts: 23,
      sad: 0,
      angry: 0,
      comments: 12,
      shares: 5,
    },
    isInteracted: {
      liked: false,
      hearted: false,
      sad: false,
      angry: false,
    },
    comments: []
  }
];

export default PostDataWithVideos;
