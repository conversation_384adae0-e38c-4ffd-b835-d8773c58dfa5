// Export main component
export { default } from '../ThreadedCommentsPopup';

// Export types for external use
export type {
    Comment, CommentInputProps, CommentItemProps, FlattenedComment, ReplyingTo,
    ThreadedCommentsPopupProps
} from './types';

// Export individual components if needed elsewhere
export { default as CommentInput } from './CommentInput';
export { default as CommentItem } from './CommentItem';
export { default as EmptyCommentsState } from './EmptyCommentsState';

// Export hooks for reuse
export { useBottomSheetGesture } from './useBottomSheetGesture';
export { useCommentActions } from './useCommentActions';

// Export utilities
export * from './constants';
export * from './utils';

