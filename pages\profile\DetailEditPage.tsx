import TopNavigationTitle from "@/components/ui/TopNavigationTitle";
import { Colors } from "@/utils/colors";
import { MainContainers, SafeAreaContainers } from "@/utils/layoutStyles";
import { useNavigation } from "expo-router";
import { useEffect, useState } from "react";
import { Alert, StyleSheet, Text, View } from "react-native";
import { Dropdown } from "react-native-element-dropdown";
import { TextInput } from "react-native-gesture-handler";

interface DetailEditPageProps {
    title: string;
    isAdditionButton: boolean;
    isTextArea: boolean;
    value: string;
    guideText: string;
    privacyField: boolean;
    placeholder?: string;
    label?: string;
    isDropdown?: boolean;
    dropdownData?: Array<{label: string, value: string}>;
}

const privacyOptions = [
    { label: "Private", value: "0" },
    { label: "Public", value: "1" }, 
    { label: "Friends", value: "2" },
];

const DetailEditPage = ({ 
    title, 
    isAdditionButton, 
    isTextArea, 
    value, 
    guideText, 
    privacyField,
    placeholder = "Enter text here...",
    label = "Field",
    isDropdown = false,
    dropdownData = []
}: DetailEditPageProps) => {
    const navigation = useNavigation();
    const [inputValue, setInputValue] = useState(value || '');
    const [privacyValue, setPrivacyValue] = useState("1"); // Default to Public
    const [isLoading, setIsLoading] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);

    // Track changes to enable/disable save button
    useEffect(() => {
        setHasChanges(inputValue !== (value || ''));
    }, [inputValue, value]);

    // Validate input based on field type
    const validateInput = (): boolean => {
        if (!inputValue.trim() && label?.toLowerCase().includes('name')) {
            Alert.alert('Validation Error', 'Name cannot be empty');
            return false;
        }

        if (label?.toLowerCase().includes('birthday') && inputValue) {
            const datePattern = /^\d{2}\/\d{2}\/\d{4}$/;
            if (!datePattern.test(inputValue)) {
                Alert.alert('Validation Error', 'Please use DD/MM/YYYY format for birthday');
                return false;
            }
        }

        if ((label?.toLowerCase().includes('website') || label?.toLowerCase().includes('url')) && inputValue) {
            const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
            if (!urlPattern.test(inputValue)) {
                Alert.alert('Validation Error', 'Please enter a valid URL');
                return false;
            }
        }

        if ((label?.toLowerCase().includes('price') || label?.toLowerCase().includes('monthly') || label?.toLowerCase().includes('yearly')) && inputValue) {
            const pricePattern = /^\d+(\.\d{1,2})?$/;
            if (!pricePattern.test(inputValue)) {
                Alert.alert('Validation Error', 'Please enter a valid price (e.g., 10.99)');
                return false;
            }
        }

        return true;
    };

    const handleSave = async () => {
        if (!validateInput()) {
            return;
        }

        setIsLoading(true);
        
        try {
            // Here you would typically save to your backend/storage
            // For now, we'll simulate an API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            console.log('Saving data:', {
                field: label,
                value: inputValue,
                privacy: privacyValue
            });

            // Show success feedback
            Alert.alert(
                'Success', 
                `${label} has been ${value ? 'updated' : 'added'} successfully!`,
                [
                    {
                        text: 'OK',
                        onPress: () => navigation.goBack()
                    }
                ]
            );
        } catch (error) {
            Alert.alert('Error', 'Failed to save changes. Please try again.');
            console.error('Save error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        if (hasChanges) {
            Alert.alert(
                'Discard Changes?',
                'You have unsaved changes. Are you sure you want to go back?',
                [
                    { text: 'Stay', style: 'cancel' },
                    { 
                        text: 'Discard', 
                        style: 'destructive',
                        onPress: () => navigation.goBack()
                    }
                ]
            );
        } else {
            navigation.goBack();
        }
    };

    // Enhanced dropdown data validation
    const validDropdownData = Array.isArray(dropdownData) && dropdownData.length > 0 
        ? dropdownData 
        : [{ label: 'No options available', value: '' }];

    return (
        <View style={MainContainers.page}>
            <View style={SafeAreaContainers.topSafeMargin}>
                <TopNavigationTitle 
                    title={title} 
                    isAdditionButton={isAdditionButton && hasChanges && !isLoading} 
                    onAdditionActions={handleSave}
                />
                
                {/* Input Field Container */}
                <View style={styles.inputContainer}>
                    <Text style={styles.inputLabel}>{label}</Text>
                    
                    {isDropdown ? (
                        <View style={styles.mainDropdownContainer}>
                            <Dropdown
                                data={validDropdownData}
                                labelField="label"
                                valueField="value"
                                value={inputValue}
                                placeholder={placeholder}
                                placeholderStyle={styles.mainDropdownPlaceholder}
                                selectedTextStyle={styles.mainDropdownSelectedText}
                                containerStyle={styles.mainDropdownListContainer}
                                style={styles.mainDropdown}
                                disable={validDropdownData.length === 1 && validDropdownData[0].value === ''}
                                renderRightIcon={() => (
                                    <Text style={styles.mainDropdownArrow}>›</Text>
                                )}
                                onChange={(item) => {
                                    if (item.value !== '') {
                                        setInputValue(item.value);
                                    }
                                }}
                            />
                        </View>
                    ) : (
                        <TextInput
                            style={[
                                styles.input,
                                isTextArea && styles.textAreaInput
                            ]}
                            value={inputValue}
                            onChangeText={setInputValue}
                            placeholder={placeholder}
                            placeholderTextColor={Colors.text?.secondary || '#999'}
                            multiline={isTextArea}
                            numberOfLines={isTextArea ? 4 : 1}
                            textAlignVertical={isTextArea ? 'top' : 'center'}
                            autoCapitalize={
                                label?.toLowerCase().includes('email') ? 'none' :
                                label?.toLowerCase().includes('website') ? 'none' :
                                label?.toLowerCase().includes('url') ? 'none' :
                                'sentences'
                            }
                            keyboardType={
                                label?.toLowerCase().includes('price') ? 'decimal-pad' :
                                label?.toLowerCase().includes('email') ? 'email-address' :
                                label?.toLowerCase().includes('website') ? 'url' :
                                label?.toLowerCase().includes('url') ? 'url' :
                                'default'
                            }
                            maxLength={isTextArea ? 500 : 100}
                            editable={!isLoading}
                        />
                    )}
                    
                    {/* Character count for text areas */}
                    {isTextArea && (
                        <Text style={styles.characterCount}>
                            {inputValue.length}/500
                        </Text>
                    )}
                </View>

                {/* Guide Text */}
                <Text style={styles.guideText}>{guideText}</Text>

                {/* Privacy Section */}
                {privacyField && (
                    <View style={styles.privacySection}>
                        <Text style={styles.sectionTitle}>Privacy</Text>
                        <View style={styles.dropdownContainer}>
                            <View style={styles.dropdownRow}>
                                <Text style={styles.dropdownLabel}>Who can see this?</Text>
                                <View style={styles.dropdownWrapper}>
                                    <Dropdown
                                        data={privacyOptions}
                                        labelField="label"
                                        valueField="value"
                                        value={privacyValue}
                                        placeholder="Select privacy"
                                        placeholderStyle={styles.dropdownPlaceholder}
                                        selectedTextStyle={styles.dropdownSelectedText}
                                        containerStyle={styles.dropdownListContainer}
                                        style={styles.dropdown}
                                        renderRightIcon={() => (
                                            <Text style={styles.dropdownArrow}>›</Text>
                                        )}
                                        onChange={(item) => {
                                            setPrivacyValue(item.value);
                                        }}
                                    />
                                </View>
                            </View>
                        </View>
                        <Text style={styles.privacyHint}>
                            {privacyValue === "0" && "Only you can see this information"}
                            {privacyValue === "1" && "Everyone can see this information"}
                            {privacyValue === "2" && "Only your friends can see this information"}
                        </Text>
                    </View>
                )}

                {/* Save/Cancel Buttons for better UX on mobile */}
                <View style={styles.buttonContainer}>
                    <Text style={styles.saveHint}>
                        {hasChanges ? 
                            `Tap "Save" above to confirm changes` : 
                            'Make changes to enable saving'
                        }
                    </Text>
                </View>
            </View>
        </View>
    );
};

export default DetailEditPage;

const styles = StyleSheet.create({
    inputContainer: {
        marginTop: 20,
        marginBottom: 16,
    },
    inputLabel: {
        fontSize: 14,
        color: Colors.text?.secondary || '#666',
        marginBottom: 8,
        marginLeft: 4,
        fontWeight: '500',
    },
    input: {
        borderWidth: 1,
        borderColor: '#E5E5E5',
        borderRadius: 24,
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: Colors.background?.primary || '#FFFFFF',
        fontSize: 16,
        color: Colors.text?.primary || '#000',
        minHeight: 52,
    },
    textAreaInput: {
        borderRadius: 16,
        minHeight: 100,
        paddingTop: 16,
        textAlignVertical: 'top',
    },
    characterCount: {
        fontSize: 12,
        color: Colors.text?.secondary || '#999',
        textAlign: 'right',
        marginTop: 4,
        marginRight: 4,
    },
    guideText: {
        fontSize: 14,
        color: Colors.text?.secondary || '#666',
        lineHeight: 20,
        marginBottom: 24,
        paddingHorizontal: 4,
    },
    privacySection: {
        marginTop: 8,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text?.primary || '#333',
        marginBottom: 16,
    },
    dropdownContainer: {
        backgroundColor: Colors.background?.secondary || '#F8F8F8',
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 8,
    },
    dropdownRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        minHeight: 60,
    },
    dropdownLabel: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text?.primary || '#000',
        flex: 1,
    },
    dropdownWrapper: {
        flex: 1,
        alignItems: 'flex-end',
    },
    dropdown: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        minWidth: 100,
    },
    dropdownPlaceholder: {
        fontSize: 16,
        color: Colors.text?.secondary || '#999',
        textAlign: 'right',
    },
    dropdownSelectedText: {
        fontSize: 16,
        color: Colors.text?.primary || '#333',
        textAlign: 'right',
        fontWeight: '500',
    },
    dropdownArrow: {
        fontSize: 20,
        color: Colors.text?.secondary || '#666',
        marginLeft: 8,
        transform: [{ rotate: '90deg' }],
    },
    dropdownListContainer: {
        borderRadius: 12,
        borderColor: '#E5E5E5',
        marginTop: 4,
    },
    mainDropdownContainer: {
        borderWidth: 1,
        borderColor: '#E5E5E5',
        borderRadius: 24,
        backgroundColor: Colors.background?.primary || '#FFFFFF',
        minHeight: 52,
        justifyContent: 'center',
        paddingHorizontal: 20,
    },
    mainDropdown: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        paddingVertical: 0,
    },
    mainDropdownPlaceholder: {
        fontSize: 16,
        color: Colors.text?.secondary || '#999',
    },
    mainDropdownSelectedText: {
        fontSize: 16,
        color: Colors.text?.primary || '#000',
    },
    mainDropdownArrow: {
        fontSize: 18,
        color: Colors.text?.secondary || '#666',
        transform: [{ rotate: '90deg' }],
    },
    mainDropdownListContainer: {
        borderRadius: 12,
        borderColor: '#E5E5E5',
        marginTop: 4,
    },
    privacyHint: {
        fontSize: 12,
        color: Colors.text?.secondary || '#666',
        fontStyle: 'italic',
        marginLeft: 4,
    },
    buttonContainer: {
        marginTop: 20,
        paddingHorizontal: 4,
    },
    saveHint: {
        fontSize: 12,
        color: Colors.text?.secondary || '#999',
        textAlign: 'center',
        fontStyle: 'italic',
    },
});