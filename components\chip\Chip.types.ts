import type { ComponentType } from "react";
import type { StyleProp, ViewStyle } from "react-native";

export type IconType = 'symbol' | 'ionicon' | 'svg' | 'iconify';

export interface ChipItem {
  label: string;
  icon: string | ComponentType<any>;
  activeIcon?: string | ComponentType<any>;
  activeColor?: string;
  iconType?: IconType;
  count?: number;
}

export interface AnimatedChipProps {
  label: string;
  icon: string | ComponentType<any>;
  activeIcon?: string | ComponentType<any>;
  isActive: boolean;
  onPress: () => void;
  activeColor?: string;
  iconType?: IconType;
}

export interface ChipGroupProps<T extends ChipItem> {
  chips: T[];
  selectedIndex?: number;
  onChange?: (index: number) => void;
  containerStyle?: StyleProp<ViewStyle>;
}
