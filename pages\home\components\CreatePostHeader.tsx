import TopNavigationTitle from '@/components/ui/TopNavigationTitle';
import React from 'react';
import { Platform, StatusBar, StyleSheet, View } from 'react-native';

interface CreatePostHeaderProps {
  onSubmit?: () => void;
}

const CreatePostHeader: React.FC<CreatePostHeaderProps> = ({
  onSubmit,
}) => {
  return (
    <View style={styles.container}>
      <TopNavigationTitle 
        title="Create Post" 
        isAdditionButton={true} 
        onAdditionActions={onSubmit}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) : 0,
  },
});

export default CreatePostHeader;
