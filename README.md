# WConnect Social Media Platform

<div align="center">
  <img src='assets/images/logo/Logo.png'></img>
  <h3>🌐 "We connect – World connect" 🌐</h3>
  <p><em>A next-generation social media platform designed for Generation Z</em></p>
  
  ![Project Status](https://img.shields.io/badge/Status-In%20Development-orange)
  ![Target Platform](https://img.shields.io/badge/Platform-Mobile-blue)
  ![Framework](https://img.shields.io/badge/Framework-React%20Native-61DAFB)
  ![License](https://img.shields.io/badge/License-MIT-green)
</div>

---

## 📋 Table of Contents

- [Overview](#-overview)
- [Key Features](#-key-features)
- [Technology Stack](#-technology-stack)
- [Design Philosophy](#-design-philosophy)
- [Project Structure](#-project-structure)
- [Installation & Setup](#-installation--setup)
- [Build](#️-build)
- [Development Phases](#-development-phases)
- [User Flow](#-user-flow)
- [Contributing](#-contributing)
- [Challenges & Solutions](#-challenges--solutions)
- [Future Roadmap](#-future-roadmap)
- [Acknowledgments](#-acknowledgments)
- [Contact](#-contact)

---

## 🚀 Overview

**WConnect** is an innovative mobile social networking platform specifically designed to address the evolving digital interaction needs of Generation Z. Born from the recognition of limitations in existing Vietnamese social media platforms like Zalo, WConnect aims to create a more dynamic, engaging, and youth-focused online environment.

### 🎯 Mission Statement
To provide a dual-purpose social media platform that seamlessly integrates robust messaging with comprehensive social interaction features, offering unlimited creative expression and meaningful connections for young users worldwide.

### ✨ What Makes WConnect Different
- **Gen Z-Centric Design**: Modern, customizable UI/UX tailored for digital natives
- **Hybrid Storage Solution**: Innovative cloud + local storage for unlimited chat history
- **Creative Expression Tools**: Comprehensive suite for content creation and customization
- **Open Social Discovery**: Interest-based and location-based friend suggestions
- **Interactive Communication**: Voice and gesture-triggered effects in video calls

---

## 🌟 Key Features

### 🔐 Authentication & Security
- Secure email/password login with third-party integration support
- Two-factor authentication
- Multi-device session management
- Account recovery and verification systems

### 📱 Social Interaction
- **Dynamic News Feed**: Personalized content discovery with AI-powered recommendations
- **Story Creation**: Rich multimedia stories with drawing tools, music integration, and privacy controls
- **Post Creation**: Advanced content tools with templates, location tagging, and emotion expression
- **Profile Customization**: Extensive personalization options including avatars, themes, and exclusive content

### 🎥 Content Creation
- **Short Videos (Wplay)**: TikTok-style short-form video creation and discovery
- **Music Integration**: Third-party streaming integration with taste sharing
- **Memory Collections**: Curated photo albums and timeline preservation
- **AI-Assisted Creation**: Smart templates and content suggestions

### 💬 Messaging System
- **Unlimited Messaging**: Free text, voice, and video communication
- **Hybrid Storage**: 15GB cloud storage + unlimited local P2P storage
- **Interactive Effects**: Voice and gesture-triggered animations during calls
- **Rich Media Sharing**: Seamless file and media exchange without restrictions

### 🔍 Discovery & Connection
- **Smart Search**: AI-powered search for posts, videos, and users
- **Friend Discovery**: Beyond phone contacts - interest and location-based suggestions
- **Open Social Network**: Connect with diverse users across geographical boundaries

---

## 🛠 Technology Stack

### **Frontend**
- **React Native with Expo** - Cross-platform mobile development
- **JavaScript/TypeScript** - Modern development practices

### **Backend** (Planned)
- **Node.js** - Event-driven server architecture
- **Socket.IO** - Real-time communication
- **PostgreSQL** - Primary database for user data and interactions

### **Design & Prototyping**
- **Figma** - UI/UX design and prototyping
- **FigJam** - User flow and collaboration

### **Development Tools**
- Git version control
- ESLint & Prettier for code quality
- Jest for testing (planned)

---

## 🎨 Design Philosophy

WConnect's design is built on three core principles:

### 1. **Youth-Centric Experience**
- Modern, intuitive interfaces that resonate with Gen Z
- Customizable themes and personalization options
- Interactive elements that make the platform feel alive

### 2. **Creative Expression**
- Comprehensive tools for content creation
- Multiple formats: text, video, audio, visual art
- AI-assisted creativity without limiting personal touch

### 3. **Meaningful Connections**
- Quality over quantity in social interactions
- Interest-based discovery mechanisms
- Safe spaces for authentic self-expression

---

## 📁 Project Structure

```
WConnect/
├── docs/                    # Documentation and design files
│   ├── user-flows/         # User flow diagrams
│   ├── wireframes/         # UI wireframes and mockups
│   └── technical-specs/    # Technical specifications
├── src/                    # Source code (to be developed)
│   ├── components/         # Reusable UI components
│   ├── screens/           # App screens/pages
│   ├── navigation/        # Navigation configuration
│   ├── services/          # API and external services
│   ├── utils/             # Helper functions
│   └── assets/            # Images, fonts, and static files
├── design/                # Figma design files and exports
└── README.md              # This file
```

---

## 🔧 Installation & Setup

> **Note**: WConnect is currently in the design and planning phase. The implementation will begin with Phase One development.

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)
- Expo EAS CLI (`npm install -g eas-cli`)

### Getting Started (Future Implementation)
```bash
# Clone the repository
git clone https://github.com/buituandev/WConnect

# Navigate to project directory
cd WConnect

# Install dependencies
npm install

# Start the development server
expo start
```

---

## 🏗️ Build

### Setting up EAS Build
1. Install EAS CLI globally (if not already installed):
   ```bash
   npm install -g eas-cli
   ```

2. Login to your Expo account:
   ```bash
   eas login
   ```

3. Configure your project for EAS Build:
   ```bash
   eas build:configure
   ```

### Building for Production

#### Android Build
To build the Android app for production:
```bash
eas build --platform android --profile production
```

This will:
- Trigger a build on Expo's cloud build service
- Generate an Android App Bundle (.aab) file
- Make the build available for download through your Expo dashboard

#### iOS Build
To build the iOS app for production:
```bash
eas build --platform ios --profile production
```

### Build Profiles
The build profiles are defined in the `eas.json` file and can be customized for different build configurations (development, preview, production).

### Viewing Build Status
```bash
eas build:list
```

### Installing Builds on Devices
- Android: Download the APK from the Expo dashboard or use the QR code
- iOS: Use TestFlight for distribution to testers

---

## 🚧 Development Phases

### **Phase 1: Core Social Interaction System** ⏳
- [x] UI/UX Design and Prototyping
- [ ] Feed Construction and Post Creation
- [ ] User Profiles and Customization
- [ ] Basic Social Interactions (likes, comments, shares)
- [ ] Authentication System Implementation

### **Phase 2: Messaging and Communication Module** 📋
- [ ] Real-time Messaging System
- [ ] Voice and Video Calling
- [ ] Interactive Effects Implementation
- [ ] Hybrid Storage Solution
- [ ] Group Chat Functionality

### **Phase 3: Advanced Features** 🔮
- [ ] AI-Powered Recommendations
- [ ] Short Video Platform (Wplay)
- [ ] Advanced Content Creation Tools
- [ ] Analytics and Insights Dashboard

---

## 👥 User Flow

### Authentication Flow
1. **App Launch** → Splash Screen → Login/Sign Up
2. **New Users**: Sign Up → Email Verification → Profile Creation → Home Feed
3. **Returning Users**: Login → Home Feed
4. **Password Recovery**: Forgot Password → Email Verification → Reset Password

### Core Features Flow
- **Home Feed**: Content discovery, interactions, story viewing
- **Search**: AI-powered content and user discovery
- - **Profile**: Personal customization and content management
- **Messaging**: Real-time communication with interactive effects
- **Creation**: Multi-format content creation tools

---

## 🤝 Contributing

We welcome contributions from developers, designers, and enthusiasts who share our vision for better social media experiences.

### How to Contribute
1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Contribution Guidelines
- Follow the existing code style and conventions
- Write clear, concise commit messages
- Add appropriate documentation for new features
- Ensure your code is tested and functional

---

## 🚨 Challenges & Solutions

### **Market Challenges**
- **Competition with Zalo**: Differentiation through Gen Z-focused features and modern UX
- **Regulatory Compliance**: Transparent content monitoring and adherence to Vietnamese regulations
- **User Adoption**: Community-driven growth and word-of-mouth marketing

### **Technical Challenges**
- **Scalability**: Microservices architecture and cloud-native deployment
- **Real-time Communication**: Optimized Socket.IO implementation with fallback mechanisms
- **Storage Innovation**: Hybrid cloud/local storage system for cost-effective unlimited storage

---

## 🗺 Future Roadmap

### **Short Term (6 months)**
- Complete Phase 1 implementation
- Beta testing with target demographic
- User feedback integration and iteration

### **Medium Term (1 year)**
- Phase 2 completion with full messaging functionality
- AI recommendation system implementation
- Platform optimization and performance enhancements

### **Long Term (2+ years)**
- International expansion beyond Vietnam
- Advanced AI features and content moderation
- Web platform development
- Integration with emerging technologies (AR/VR)

---

## 🏆 Acknowledgments

- **Supervisor**: Mr. Ung Van Giau for exceptional guidance and mentorship
- **Eastern International University** - Software Engineering Department
- **Open Source Community** for invaluable tools and resources
- **Design Inspiration**: Tencent QQ's youth engagement strategies
- **Market Research**: Decision Lab Consumer Reports 2024

---

## 📞 Contact

**Developer**: Bùi Anh Tuấn  
**Student ID**: **********  
**Institution**: Eastern International University  
**Email**: [<EMAIL>]  
**Project Supervisor**: Mr. Ung Van Giau  

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

<div align="center">
  <p><strong>Built with ❤️ for Generation Z</strong></p>
  <p><em>WConnect - Where creativity meets connectivity</em></p>
</div>
