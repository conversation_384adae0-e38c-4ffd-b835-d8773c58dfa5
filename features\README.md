# Features Folder

This folder contains feature-specific components and logic organized by business domains.

## Purpose
- Groups related components, hooks, and utilities by feature/domain
- Implements feature-driven development approach
- Contains business logic specific to particular app features
- Each subfolder represents a distinct feature or module

## Structure
Features should be organized as self-contained modules, each potentially containing:
- Components specific to that feature
- Custom hooks for feature logic
- Types and interfaces
- Feature-specific utilities

## Example
```
features/
  user-profile/
    components/
    hooks/
    types/
  messaging/
    components/
    hooks/
    types/
```
