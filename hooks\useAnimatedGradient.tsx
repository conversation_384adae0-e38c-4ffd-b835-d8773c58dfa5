import { ExtractedColors, addAlphaToColor, getCachedColors } from '@/utils/colorExtraction';
import { Colors } from '@/utils/colors';
import { useEffect, useRef, useState } from 'react';
import { Animated, Easing } from 'react-native';

export const useAnimatedGradient = (imageUri?: string, useContext = false) => {
  const [extractedColors, setExtractedColors] = useState<ExtractedColors>({
    primary: Colors.primary[500],
    secondary: Colors.primary[400],
    detail: Colors.primary[600],
    background: Colors.primary[100],
  });

  const animatedValue = useRef(new Animated.Value(0)).current;
  const animatedValue2 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageUri && !useContext) {
      getCachedColors(imageUri).then(setExtractedColors);
    }
  }, [imageUri, useContext]);

  useEffect(() => {
    // Start the animation loop
    const createAnimation = (animValue: Animated.Value, duration: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1,
            duration: duration,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: false,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: duration,
            easing: Easing.inOut(Easing.sin),
            useNativeDriver: false,
          }),
        ])
      );
    };

    const animation1 = createAnimation(animatedValue, 4000);
    const animation2 = createAnimation(animatedValue2, 6000);

    animation1.start();
    animation2.start();

    return () => {
      animation1.stop();
      animation2.stop();
    };
  }, [animatedValue, animatedValue2]);

  // Create animated colors that interpolate between different shades
  const animatedColor1 = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [
      addAlphaToColor(extractedColors.primary, 0.6),
      addAlphaToColor(extractedColors.secondary, 0.4),
      addAlphaToColor(extractedColors.detail, 0.5),
    ],
  });

  const animatedColor2 = animatedValue2.interpolate({
    inputRange: [0, 0.3, 0.7, 1],
    outputRange: [
      addAlphaToColor(extractedColors.secondary, 0.3),
      addAlphaToColor(extractedColors.primary, 0.5),
      addAlphaToColor(extractedColors.background, 0.2),
      addAlphaToColor(extractedColors.detail, 0.4),
    ],
  });

  return {
    extractedColors,
    animatedColor1,
    animatedColor2,
    animatedValue,
    animatedValue2,
  };
};
