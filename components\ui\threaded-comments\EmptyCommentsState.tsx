import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Colors } from '../../../utils/colors';
import { Fonts, Typography } from '../../../utils/fonts';
import { EMPTY_COMMENTS_MESSAGE } from './constants';

const EmptyCommentsState: React.FC = () => {
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Text style={styles.icon}>💬</Text>
      </View>
      <Text style={styles.message}>{EMPTY_COMMENTS_MESSAGE}</Text>
      <Text style={styles.subMessage}>Share your thoughts below</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
  },
  iconContainer: {
    marginBottom: 16,
  },
  icon: {
    fontSize: 48,
    opacity: 0.6,
  },
  message: {
    ...Typography.h3,
    fontFamily: Fonts.roboto.medium,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  subMessage: {
    ...Typography.body2,
    color: Colors.text.muted,
    textAlign: 'center',
  },
});

export default EmptyCommentsState;
