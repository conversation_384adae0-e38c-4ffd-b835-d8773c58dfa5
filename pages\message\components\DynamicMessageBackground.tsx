import { useAnimatedGradient } from '@/hooks/useAnimatedGradient';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Animated, View } from 'react-native';

interface DynamicGradientBackgroundProps {
  imageUri?: string;
  height?: number;
  borderRadius?: number;
}

const DynamicGradientBackground = ({
  imageUri,
  height = 300,
  borderRadius = 20,
}: DynamicGradientBackgroundProps) => {
  // Default to the header image URI if none provided
  const defaultImageUri =
    'https://instag.com/api/image/?source=https%3A//plus.unsplash.com/premium_photo-1750513954684-57fe0df264ef%3Fixid%3DM3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzUyNTcxNDM5fA%26ixlib%3Drb-4.1.0';
  const targetImageUri = imageUri || defaultImageUri;

  const { extractedColors, animatedValue, animatedValue2 } = useAnimatedGradient(targetImageUri);

  // More noticeable opacity variations
  const softOpacity1 = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.6, 0.9, 0.75],
    extrapolate: 'clamp',
  });

  const softOpacity2 = animatedValue2.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.5, 0.8, 0.6],
    extrapolate: 'clamp',
  });

  // More visible movement - increased from 2-8px to 15-25px
  const gentleTranslateX1 = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 25],
  });

  const gentleTranslateY1 = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const gentleTranslateX2 = animatedValue2.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const gentleTranslateY2 = animatedValue2.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 15],
  });

  // More noticeable scale changes - increased from 1-2% to 3-6%
  const gentleScale1 = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 1.06, 1.03],
  });

  const gentleScale2 = animatedValue2.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 1.04, 1.05],
  });

  // More noticeable background layer opacity
  const backgroundOpacity = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.25, 0.5, 0.35],
    extrapolate: 'clamp',
  });

  // Additional rotation for more dynamic movement
  const gentleRotation1 = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '2deg'],
  });

  const gentleRotation2 = animatedValue2.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '-1.5deg'],
  });

  return (
    <View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height,
        borderBottomLeftRadius: borderRadius,
        borderBottomRightRadius: borderRadius,
        overflow: 'hidden',
      }}>
      {/* Main gradient layer with bold colors at top */}
      <Animated.View
        style={{
          position: 'absolute',
          top: -100,
          left: -100,
          right: -100,
          height: '180%',
          opacity: softOpacity1,
          transform: [
            { translateX: gentleTranslateX1 },
            { translateY: gentleTranslateY1 },
            { scale: gentleScale1 },
            { rotate: gentleRotation1 },
          ],
        }}>
        <BlurView intensity={25} style={{ flex: 1 }}>
          <LinearGradient
            colors={[
              `${extractedColors.primary}95`, // Bold at top
              `${extractedColors.primary}75`,
              `${extractedColors.secondary}50`,
              `${extractedColors.secondary}25`,
              'rgba(255, 255, 255, 0.08)',
              'rgba(255, 255, 255, 0)', // Complete transparency to blend with background
            ]}
            style={{
              flex: 1,
              borderRadius: borderRadius * 3,
            }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            locations={[0, 0.2, 0.5, 0.75, 0.9, 1]}
          />
        </BlurView>
      </Animated.View>

      {/* Secondary layer for depth */}
      <Animated.View
        style={{
          position: 'absolute',
          top: -80,
          left: -80,
          right: -80,
          height: '160%',
          opacity: softOpacity2,
          transform: [
            { translateX: gentleTranslateX2 },
            { translateY: gentleTranslateY2 },
            { scale: gentleScale2 },
            { rotate: gentleRotation2 },
          ],
        }}>
        <BlurView intensity={30} style={{ flex: 1 }}>
          <LinearGradient
            colors={[
              `${extractedColors.secondary}80`, // Bold secondary at top
              `${extractedColors.detail}60`,
              `${extractedColors.background}40`,
              'rgba(255, 255, 255, 0.1)',
              'rgba(255, 255, 255, 0.02)',
              'rgba(255, 255, 255, 0)', // Complete transparency
            ]}
            style={{
              flex: 1,
              borderRadius: borderRadius * 2.5,
            }}
            start={{ x: 0.2, y: 0 }}
            end={{ x: 0.8, y: 1 }}
            locations={[0, 0.25, 0.5, 0.75, 0.9, 1]}
          />
        </BlurView>
      </Animated.View>

      {/* Soft background enhancement layer */}
      <Animated.View
        style={{
          position: 'absolute',
          top: -60,
          left: -60,
          right: -60,
          height: '140%',
          opacity: backgroundOpacity,
          transform: [
            {
              translateX: animatedValue2.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 12],
              }),
            },
            {
              translateY: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -8],
              }),
            },
            {
              scale: animatedValue.interpolate({
                inputRange: [0, 0.5, 1],
                outputRange: [1, 1.02, 1.01],
              }),
            },
          ],
        }}>
        <BlurView intensity={35} style={{ flex: 1 }}>
          <LinearGradient
            colors={[
              `${extractedColors.primary}60`, // Strong at top
              `${extractedColors.detail}40`,
              `${extractedColors.background}20`,
              'rgba(255, 255, 255, 0.05)',
              'rgba(255, 255, 255, 0.01)',
              'rgba(255, 255, 255, 0)', // Perfect blend with background
            ]}
            style={{
              flex: 1,
              borderRadius: borderRadius * 4,
            }}
            start={{ x: 0.1, y: 0 }}
            end={{ x: 0.9, y: 1 }}
            locations={[0, 0.3, 0.55, 0.8, 0.95, 1]}
          />
        </BlurView>
      </Animated.View>

      {/* Ambient floating layer - more visible */}
      <Animated.View
        style={{
          position: 'absolute',
          top: '20%',
          left: '-15%',
          right: '-15%',
          height: '80%',
          opacity: animatedValue2.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.2, 0.4, 0.3],
          }),
          transform: [
            {
              scale: animatedValue2.interpolate({
                inputRange: [0, 0.5, 1],
                outputRange: [1.5, 1.58, 1.54],
              }),
            },
            {
              translateX: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 10],
              }),
            },
            {
              rotate: animatedValue2.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '0.5deg'],
              }),
            },
          ],
        }}>
        <BlurView intensity={40} style={{ flex: 1 }}>
          <LinearGradient
            colors={[
              `${extractedColors.background}40`,
              `${extractedColors.primary}25`,
              'rgba(255, 255, 255, 0.08)',
              'rgba(255, 255, 255, 0.02)',
              'rgba(255, 255, 255, 0)', // Seamless background blend
            ]}
            style={{
              flex: 1,
              borderRadius: 400,
            }}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            locations={[0, 0.3, 0.6, 0.9, 1]}
          />
        </BlurView>
      </Animated.View>

      {/* Bottom White Gradient Overlay */}
      <LinearGradient
        colors={[
          'rgba(255, 255, 255, 0)', // 100% - transparent
          'rgba(255, 255, 255, 0.2)', // 80%
          'rgba(255, 255, 255, 0.5)', // 60%
          'rgba(255, 255, 255, 0.85)', // 35%
          'rgba(255, 255, 255, 0.95)', // 20%
          'rgba(255, 255, 255, 1)', // 10%
          'rgba(255, 255, 255, 1)', // 0%
        ]}
        locations={[0, 0.2, 0.4, 0.65, 0.8, 0.9, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '25%',
          zIndex: 20,
        }}
      />
    </View>
  );
};

export default DynamicGradientBackground;