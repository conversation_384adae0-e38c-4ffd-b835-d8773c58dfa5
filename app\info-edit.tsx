import DetailEditPage from "@/pages/profile/DetailEditPage";
import { useLocalSearchParams } from "expo-router";

export default function InfoEdit() {
    const params = useLocalSearchParams();
    const props = {
        title: params.title as string || '',
        isAdditionButton: params.isAdditionButton === 'true',
        isTextArea: params.isTextArea === 'true',
        value: params.value as string || '',
        guideText: params.guideText as string || '',
        privacyField: params.privacyField === 'true',
        placeholder: params.placeholder as string,
        label: params.label as string,
        isDropdown: params.isDropdown === 'true',
        dropdownData: (() => {
            if (!params.dropdownData) return [];
            try {
                return JSON.parse(params.dropdownData as string);
            } catch (error) {
                console.warn('Failed to parse dropdownData:', error);
                return [];
            }
        })()
    };

    return <DetailEditPage {...props} />;
} 