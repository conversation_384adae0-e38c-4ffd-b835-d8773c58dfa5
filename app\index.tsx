import {SafeAreaView} from "react-native";
import LogoSplashScreen from "@/pages/splash/LogoSplash";
import Authentication from "@/pages/authentication/Authentication";
import {useState} from "react";
import NewsFeedPage from "@/pages/home/<USER>";

export default function Index() {
    const [showSplash, setShowSplash] = useState(true);
    // @@iconify-code-gen
    const handleSplashComplete = () => {
        setShowSplash(false);
    };

    return (
        <SafeAreaView className={'flex-1'}>
            {showSplash ? (
                <LogoSplashScreen onComplete={handleSplashComplete}/>
            ) : (
                <Authentication/>
            )}
        </SafeAreaView>
    );
}