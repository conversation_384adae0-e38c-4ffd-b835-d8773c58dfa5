import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { Platform, StyleSheet, Text, View } from 'react-native';

export default function Explore() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Explore</Text>
      <Text style={styles.subtitle}>Discover new content and people. Coming soon!</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 110 : 90,
  },
  title: {
    fontSize: 32,
    fontFamily: Fonts.roboto.medium,
    color: Colors.text.primary,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: Fonts.roboto.regular,
    color: Colors.text.muted,
    textAlign: 'center',
  },
});
