import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { Ionicons } from '@expo/vector-icons';
import MaskedView from '@react-native-masked-view/masked-view';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';

interface StoryItemProps {
  item: {
    id: string;
    name: string;
    avatar?: string;
    image: string;
    isSeen: boolean;
    isActive?: boolean;
  };
  isCreateStory?: boolean;
  onPress: () => void;
}

const styles = StyleSheet.create({
  container: {
    height: 192,
    width: 110,
    marginRight: 12,
  },
  pressable: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
  },
  image: {
    borderRadius: 10,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 90,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    overflow: 'hidden',
  },
  bottomSection: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  createStoryContainer: {
    borderWidth: 2,
    borderColor: Colors.primary[950],
    borderRadius: 9999,
    borderStyle: 'dashed',
    padding: 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    width: 40,
    height: 40,
  },
  createStoryInner: {
    backgroundColor: 'white',
    borderRadius: 24,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userAvatarContainer: {
    borderWidth: 2,
    borderRadius: 9999,
    padding: 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    width: 40,
    height: 40,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  nameText: {
    marginTop: 6,
    color: 'white',
    textAlign: 'center',
    fontSize: 10,
    fontFamily: Fonts.roboto.regular,
  },
});

const StoryItem = ({ item, isCreateStory = false, onPress }: StoryItemProps) => {
  return (
    <View style={styles.container}>
      <Pressable style={styles.pressable} onPress={onPress}>
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: item.image,
            }}
            resizeMode="cover"
            style={styles.image}
          />
          <View style={styles.overlay}>
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)', 'black']}
              locations={[0, 0.9, 1]}
              style={StyleSheet.absoluteFill}
            />
          </View>
        </View>

        <View style={styles.bottomSection}>
          {isCreateStory ? (
            <View style={styles.createStoryContainer}>
              <View style={styles.createStoryInner}>
                <Ionicons name="add" size={20} color={Colors.primary[950]} />
              </View>
            </View>
          ) : (
            <View
              style={[
                styles.userAvatarContainer,
                {
                  borderColor: item.isSeen ? 'rgba(255,255,255,0.3)' : Colors.primary[950],
                  borderStyle: item.isSeen ? 'solid' : 'dashed',
                },
              ]}>
              <Image source={{ uri: item.avatar }} style={styles.userAvatar} resizeMode="cover" />
            </View>
          )}

          <Text style={styles.nameText}>{isCreateStory ? 'Create your story' : item.name}</Text>
        </View>
      </Pressable>
    </View>
  );
};

export default StoryItem;
