import {
  Roboto_300Light,
  Roboto_400Regular,
  Roboto_500Medium,
  Roboto_700Bold,
  useFonts as useRobotoFonts,
} from '@expo-google-fonts/roboto';

import {
  Lexend_300Light,
  Lexend_400Regular,
  Lexend_500Medium,
  Lexend_600SemiBold,
  Lexend_700Bold,
  useFonts as useLexendFonts,
} from '@expo-google-fonts/lexend';

// Font constants for programmatic use
export const Fonts = {
  roboto: {
    light: 'Roboto_300Light',
    regular: 'Roboto_400Regular',
    medium: 'Roboto_500Medium',
    bold: 'Roboto_700Bold',
  },
  lexend: {
    light: 'Lexend_300Light',
    regular: 'Lexend_400Regular',
    medium: 'Lexend_500Medium',
    semibold: 'Lexend_600SemiBold',
    bold: 'Lexend_700Bold',
  },
} as const;

// Font loading hook
export const useFonts = () => {
  const [robotoLoaded] = useRobotoFonts({
    Roboto_300Light,
    Roboto_400Regular,
    Roboto_500Medium,
    Roboto_700Bold,
  });

  const [lexendLoaded] = useLexendFonts({
    Lexend_300Light,
    Lexend_400Regular,
    Lexend_500Medium,
    Lexend_600SemiBold,
    Lexend_700Bold,
  });

  return robotoLoaded && lexendLoaded;
};

// Typography scale with font families
export const Typography = {
  // Headings (Lexend)
  h1: {
    fontFamily: Fonts.lexend.bold,
    fontSize: 32,
    lineHeight: 40,
  },
  h2: {
    fontFamily: Fonts.lexend.bold,
    fontSize: 28,
    lineHeight: 36,
  },
  h3: {
    fontFamily: Fonts.lexend.semibold,
    fontSize: 24,
    lineHeight: 32,
  },
  h4: {
    fontFamily: Fonts.lexend.semibold,
    fontSize: 20,
    lineHeight: 28,
  },
  h5: {
    fontFamily: Fonts.lexend.medium,
    fontSize: 18,
    lineHeight: 24,
  },
  h6: {
    fontFamily: Fonts.lexend.medium,
    fontSize: 16,
    lineHeight: 24,
  },

  // Body text (Roboto)
  body1: {
    fontFamily: Fonts.roboto.regular,
    fontSize: 16,
    lineHeight: 24,
  },
  body2: {
    fontFamily: Fonts.roboto.regular,
    fontSize: 14,
    lineHeight: 20,
  },
  caption: {
    fontFamily: Fonts.roboto.regular,
    fontSize: 12,
    lineHeight: 16,
  },

  // Button text
  button: {
    fontFamily: Fonts.roboto.medium,
    fontSize: 14,
    lineHeight: 20,
  },

  // Labels
  label: {
    fontFamily: Fonts.roboto.medium,
    fontSize: 12,
    lineHeight: 16,
  },
} as const;

export type FontFamily = keyof typeof Fonts;
export type FontWeight = keyof typeof Fonts.roboto | keyof typeof Fonts.lexend;
