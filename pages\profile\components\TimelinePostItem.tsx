import React from 'react';
import { StyleSheet, View } from 'react-native';
import Post from './Post';

interface TimelinePostItemProps {
  item: any;
  isVisible: boolean;
  onOpenComments?: (post: any) => void;
}

const TimelinePostItem: React.FC<TimelinePostItemProps> = React.memo(({ item, isVisible, onOpenComments }) => {
  if (!isVisible) {
    return <View style={styles.timelinePostContainer} />;
  }
  
  return (
    <View style={styles.timelinePostContainer}>
      <Post post={item} onOpenComments={onOpenComments} />
    </View>
  );
});

TimelinePostItem.displayName = 'TimelinePostItem';

export default TimelinePostItem;

const styles = StyleSheet.create({
  timelinePostContainer: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    minHeight: 200,
  },
}); 