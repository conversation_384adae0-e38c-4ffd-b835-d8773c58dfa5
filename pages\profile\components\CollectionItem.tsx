import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { LinearGradient } from 'expo-linear-gradient';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';

interface StoryItemProps {
  item: {
    id: string;
    title: string;
    media: string[];
  };
  isCreateStory?: boolean;
  onPress: () => void;
}

const styles = StyleSheet.create({
  container: {
    height: 192,
    width: 110,
    marginRight: 12,
  },
  pressable: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
  },
  image: {
    borderRadius: 10,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 90,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    overflow: 'hidden',
  },
  bottomSection: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  nameText: {
    marginTop: 6,
    color: 'white',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.roboto.regular,
  },
  badgeContainer: {
    position: 'absolute',
    left: 10,
    top: 10,
    height: 30,
    width: 30,
    borderRadius: 15,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'white',
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeInner: {
    height: 24,
    width: 24,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.primary[500],
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

const StoryItem = ({ item, isCreateStory = false, onPress }: StoryItemProps) => {
  return (
    <View style={styles.container}>
      <Pressable style={styles.pressable} onPress={onPress}>
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: item.media?.[0],
            }}
            resizeMode="cover"
            style={styles.image}
          />
          <View style={styles.badgeContainer}>
            <View style={styles.badgeInner}>
              <Text style={styles.badgeText}>{item.media.length}</Text>
            </View>
          </View>
          <View style={styles.overlay}>
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)', 'black']}
              locations={[0, 0.9, 1]}
              style={StyleSheet.absoluteFill}
            />
          </View>
        </View>

        <View style={styles.bottomSection}>
          <Text style={styles.nameText} numberOfLines={1}>{item.title}</Text>
        </View>
      </Pressable>
    </View>
  );
};

export default StoryItem;
