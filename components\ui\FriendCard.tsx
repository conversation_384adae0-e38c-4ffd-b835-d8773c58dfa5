import { Colors } from '@/utils/colors';
import { Fonts } from '@/utils/fonts';
import { shadows } from '@/utils/shadows';
import { Ionicons } from '@expo/vector-icons';
import { StyleSheet, Text, View } from 'react-native';
import { AvatarGroup } from '../avatar-group/AvatarGroup';
import { ExpandableButton } from '../button/Button';
import ImageAdvanced from './ImageAdvanced';

const FriendCard = () => {
  const defaultFriends = [
    {
      name: 'Heart',
      uri: 'https://images.unsplash.com/photo-1602233158242-3ba0ac4d2167?q=80&w=736&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '1',
    },
    {
      name: 'Like',
      uri: 'https://plus.unsplash.com/premium_photo-1664464229780-5d4f367dd754?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '2',
    },
    {
      name: 'Laugh',
      uri: 'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '3',
    },
    {
      name: 'Laugh5',
      uri: 'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '4',
    },
    {
      name: 'Laugh4',
      uri: 'https://plus.unsplash.com/premium_photo-1671656349322-41de944d259b?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      id: '5',
    },
  ];

  const displayFriends = defaultFriends;
  return (
    <View style={styles.container}>
      <View style={styles.closeButton}>
        <Ionicons name="close" size={20} color={Colors.primary[500]} />
      </View>

      <View style={styles.content}>
        <ImageAdvanced
          source={
            'https://images.unsplash.com/photo-1504150558240-0b4fd8946624?q=80&w=464&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
          }
          style={styles.avatarImage}
          contentFit="cover">
          <View style={styles.newTag}>
            <Text style={styles.newTagText}>NEW</Text>
          </View>
        </ImageAdvanced>

        <View style={styles.userInfo}>
          <Text style={styles.userName}>Nancy Wheeler</Text>
          <View style={styles.mutualFriends}>
            <AvatarGroup avatars={displayFriends} size={22} max={3} overlap={8} />
            <Text style={styles.mutualFriendsText}>7 mutual friends</Text>
          </View>
        </View>
      </View>

      <ExpandableButton
        title={'Add Friend'}
        width={158}
        height={45}
        withPressAnimation={true}
        textStyle={{
          fontFamily: Fonts.roboto.medium,
          fontSize: 14,
          textAlign: 'center',
        }}
        backgroundColor={Colors.primary[500]}
        isLoading={false}
        onPress={function (): void {
          console.log('Add friend pressed');
        }}
        style={styles.addButton}
      />
    </View>
  );
};

export default FriendCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    ...shadows.large,
    borderRadius: 16,
    height: 240,
    width: 190,
    padding: 16,
    position: 'relative',
    marginTop: 8,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  avatarImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
  },
  newTag: {
    position: 'absolute',
    bottom: 5,
    right: -3,
    backgroundColor: Colors.primary[500],
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    minWidth: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  newTagText: {
    fontFamily: Fonts.roboto.medium,
    fontSize: 9,
    color: 'white',
    fontWeight: '600',
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 'auto',
  },
  userName: {
    fontFamily: Fonts.roboto.bold,
    fontSize: 16,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: 8,
  },
  mutualFriends: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  mutualFriendsText: {
    fontFamily: Fonts.roboto.regular,
    fontSize: 12,
    color: Colors.text.muted,
  },
  addButton: {
    alignSelf: 'stretch',
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: Colors.background.muted,
    borderRadius: 12,
    padding: 6,
    zIndex: 1,
  },
});
