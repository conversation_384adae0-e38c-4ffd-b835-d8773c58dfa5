# Services Folder

This folder contains all service layer implementations for external integrations, data management, and cross-cutting concerns.

## Structure
- `api/` - API integration and HTTP client services
- `shared/` - Shared service utilities and common functionality
- `ui/` - UI-related services and utilities

## Purpose
- Abstracts external dependencies and third-party integrations
- Provides clean interfaces for data access and business operations
- Centralizes service configurations and implementations
- Separates business logic from UI components
